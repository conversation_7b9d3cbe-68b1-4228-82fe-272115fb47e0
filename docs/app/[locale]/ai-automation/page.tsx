import { Metada<PERSON> } from "next";
import { <PERSON> } from "@/i18n/routing";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowRight,
  Brain,
  Zap,
  BarChart3,
  Globe,
  Database,
  Settings,
  TrendingUp,
  Users,
  Shield,
  Clock,
} from "lucide-react";

export const metadata: Metadata = {
  title: "AI & Automation",
  description: "Learn about SME Analytica's AI capabilities, automation features, and the technology powering intelligent business insights.",
};

export default function AIAutomationPage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-background via-background to-muted/20 py-20 md:py-32">
        <div className="container relative">
          <div className="mx-auto max-w-4xl text-center">
            <Badge variant="outline" className="mb-4">
              AI & Automation
            </Badge>
            <h1 className="mb-6 text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl">
              Intelligent Business Automation
            </h1>
            <p className="mb-8 text-xl text-muted-foreground sm:text-2xl">
              Discover how SME Analytica leverages cutting-edge AI models and automation 
              to deliver actionable business insights and streamline operations.
            </p>
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button size="lg" asChild>
                <Link href="/modules">
                  Explore AI Features
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/integrations">
                  API Documentation
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* AI Models */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-4xl">
            <h2 className="mb-8 text-3xl font-bold tracking-tight sm:text-4xl">
              AI Models & Capabilities
            </h2>
            <div className="mb-8 space-y-4 text-lg text-muted-foreground">
              <p>
                SME Analytica integrates with multiple state-of-the-art AI models through OpenRouter, 
                providing businesses with access to the most advanced language models and AI capabilities 
                available today.
              </p>
            </div>
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <Brain className="h-8 w-8 text-primary" />
                  <CardTitle>Language Models</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold">GPT-4 & GPT-3.5</h4>
                    <p className="text-sm text-muted-foreground">Advanced reasoning and analysis capabilities</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Claude (Anthropic)</h4>
                    <p className="text-sm text-muted-foreground">Constitutional AI for safe and helpful responses</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">DeepSeek</h4>
                    <p className="text-sm text-muted-foreground">Specialized models for technical analysis</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Perplexity</h4>
                    <p className="text-sm text-muted-foreground">Real-time web search and information synthesis</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <BarChart3 className="h-8 w-8 text-primary" />
                  <CardTitle>Analysis Capabilities</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold">Sentiment Analysis</h4>
                    <p className="text-sm text-muted-foreground">Customer feedback and review sentiment tracking</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Market Intelligence</h4>
                    <p className="text-sm text-muted-foreground">Competitive landscape and market trend analysis</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Dynamic Pricing</h4>
                    <p className="text-sm text-muted-foreground">Real-time price optimization based on demand</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Sales Forecasting</h4>
                    <p className="text-sm text-muted-foreground">Predictive analytics for revenue planning</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Automation Features */}
      <section className="bg-muted/30 py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              Automation Features
            </h2>
            <p className="mb-12 text-lg text-muted-foreground">
              Streamline your business operations with intelligent automation
            </p>
          </div>
          <div className="grid gap-6 md:grid-cols-3">
            {[
              {
                icon: Zap,
                title: "Real-time Processing",
                description: "Instant analysis and insights as data becomes available",
              },
              {
                icon: TrendingUp,
                title: "Dynamic Pricing",
                description: "Automatic price adjustments based on market conditions",
              },
              {
                icon: Globe,
                title: "Multi-source Data",
                description: "Automated data collection from Google Places, TripAdvisor, Yelp",
              },
              {
                icon: Users,
                title: "Customer Insights",
                description: "Automated sentiment tracking and customer behavior analysis",
              },
              {
                icon: Shield,
                title: "Security & Privacy",
                description: "Automated data protection and compliance monitoring",
              },
              {
                icon: Clock,
                title: "Scheduled Reports",
                description: "Automated generation and delivery of business reports",
              },
            ].map((feature, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <feature.icon className="mx-auto h-12 w-12 text-primary" />
                  <CardTitle className="text-lg">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">{feature.description}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Data Sources */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-4xl">
            <h2 className="mb-8 text-3xl font-bold tracking-tight sm:text-4xl text-center">
              Data Sources & Integration
            </h2>
            <div className="grid gap-8 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <Database className="h-8 w-8 text-primary" />
                  <CardTitle>External Data Sources</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold">Google Places API</h4>
                    <p className="text-sm text-muted-foreground">Business information, reviews, and location data</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">TripAdvisor API</h4>
                    <p className="text-sm text-muted-foreground">Travel and hospitality industry insights</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Yelp API</h4>
                    <p className="text-sm text-muted-foreground">Local business reviews and ratings</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Real-time Market Data</h4>
                    <p className="text-sm text-muted-foreground">Live market conditions and competitor pricing</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <Settings className="h-8 w-8 text-primary" />
                  <CardTitle>Processing Pipeline</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold">Data Ingestion</h4>
                    <p className="text-sm text-muted-foreground">Automated collection and normalization</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">AI Analysis</h4>
                    <p className="text-sm text-muted-foreground">Multi-model processing for comprehensive insights</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Quality Assurance</h4>
                    <p className="text-sm text-muted-foreground">Automated validation and confidence scoring</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Delivery</h4>
                    <p className="text-sm text-muted-foreground">Real-time updates via API and mobile app</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-muted/30 py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              Ready to Harness AI for Your Business?
            </h2>
            <p className="mb-8 text-lg text-muted-foreground">
              Discover how SME Analytica's AI and automation can transform your business operations
            </p>
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button size="lg" asChild>
                <Link href="/modules">
                  Explore AI Features
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/for-businesses">
                  For Businesses
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
