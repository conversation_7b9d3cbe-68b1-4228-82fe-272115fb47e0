import { <PERSON>ada<PERSON> } from "next";
import { <PERSON> } from "@/i18n/routing";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowRight,
  Mail,
  MapPin,
  Globe,
  Users,
  Target,
  Lightbulb,
  Heart,
} from "lucide-react";

export const metadata: Metadata = {
  title: "About SME Analytica",
  description: "Learn about SME Analytica's mission to empower small and medium enterprises with AI-powered business intelligence solutions.",
};

export default function AboutPage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-background via-background to-muted/20 py-20 md:py-32">
        <div className="container relative">
          <div className="mx-auto max-w-4xl text-center">
            <Badge variant="outline" className="mb-4">
              About SME Analytica
            </Badge>
            <h1 className="mb-6 text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl">
              Empowering SMEs with AI
            </h1>
            <p className="mb-8 text-xl text-muted-foreground sm:text-2xl">
              We believe every small and medium enterprise deserves access to enterprise-level
              business intelligence and AI-powered insights.
            </p>
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button size="lg" asChild>
                <Link href="/for-businesses">
                  For Businesses
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="mailto:<EMAIL>">
                  Contact Us
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-20">
        <div className="container">
          <div className="grid gap-12 md:grid-cols-2">
            <Card>
              <CardHeader>
                <Target className="h-8 w-8 text-primary" />
                <CardTitle className="text-2xl">Our Mission</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-base leading-relaxed">
                  To democratize business intelligence by providing small and medium enterprises
                  with affordable, easy-to-use AI-powered analytics tools that were previously
                  only available to large corporations.
                </CardDescription>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <Lightbulb className="h-8 w-8 text-primary" />
                <CardTitle className="text-2xl">Our Vision</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-base leading-relaxed">
                  A world where every business, regardless of size, can make data-driven decisions
                  and compete effectively in the global marketplace through intelligent automation
                  and AI-powered insights.
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Our Story */}
      <section className="bg-muted/30 py-20">
        <div className="container">
          <div className="mx-auto max-w-4xl">
            <h2 className="mb-8 text-3xl font-bold tracking-tight sm:text-4xl text-center">
              Our Story
            </h2>
            <div className="space-y-4 text-lg text-muted-foreground">
              <p>
                SME Analytica was born from a simple observation: while large enterprises have access
                to sophisticated business intelligence tools and data science teams, small and medium
                enterprises often struggle with basic analytics and decision-making.
              </p>
              <p>
                Our founders, with backgrounds in AI, data science, and small business operations,
                recognized that the same technologies powering Fortune 500 companies could be made
                accessible to SMEs through intelligent design and automation.
              </p>
              <p>
                Today, SME Analytica serves businesses across multiple industries, from restaurants
                and retail stores to service providers and event organizers, helping them increase
                revenue, improve customer satisfaction, and make better business decisions.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Values */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              Our Values
            </h2>
            <p className="mb-12 text-lg text-muted-foreground">
              The principles that guide everything we do
            </p>
          </div>
          <div className="grid gap-6 md:grid-cols-3">
            {[
              {
                icon: Users,
                title: "Customer-Centric",
                description: "We build solutions based on real business needs and customer feedback.",
              },
              {
                icon: Lightbulb,
                title: "Innovation",
                description: "We continuously explore new technologies to solve business challenges.",
              },
              {
                icon: Heart,
                title: "Accessibility",
                description: "We make advanced technology accessible to businesses of all sizes.",
              },
              {
                icon: Target,
                title: "Results-Driven",
                description: "We focus on delivering measurable business value and ROI.",
              },
              {
                icon: Globe,
                title: "Global Perspective",
                description: "We design solutions that work across different markets and cultures.",
              },
              {
                icon: Users,
                title: "Transparency",
                description: "We believe in open communication and honest business practices.",
              },
            ].map((value, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <value.icon className="mx-auto h-12 w-12 text-primary" />
                  <CardTitle className="text-lg">{value.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">{value.description}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Information */}
      <section className="bg-muted/30 py-20">
        <div className="container">
          <div className="mx-auto max-w-4xl">
            <h2 className="mb-8 text-3xl font-bold tracking-tight sm:text-4xl text-center">
              Get in Touch
            </h2>
            <div className="grid gap-8 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <Mail className="h-8 w-8 text-primary" />
                  <CardTitle>Contact Us</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold">General Inquiries</h4>
                    <p className="text-sm text-muted-foreground"><EMAIL></p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Business Partnerships</h4>
                    <p className="text-sm text-muted-foreground"><EMAIL></p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Technical Support</h4>
                    <p className="text-sm text-muted-foreground"><EMAIL></p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Media & Press</h4>
                    <p className="text-sm text-muted-foreground"><EMAIL></p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <Globe className="h-8 w-8 text-primary" />
                  <CardTitle>Online Presence</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold">Main Website</h4>
                    <Link
                      href="https://smeanalytica.dev"
                      className="text-sm text-primary hover:underline"
                      target="_blank"
                    >
                      smeanalytica.dev
                    </Link>
                  </div>
                  <div>
                    <h4 className="font-semibold">API Documentation</h4>
                    <Link
                      href="https://api.smeanalytica.dev/docs"
                      className="text-sm text-primary hover:underline"
                      target="_blank"
                    >
                      api.smeanalytica.dev/docs
                    </Link>
                  </div>
                  <div>
                    <h4 className="font-semibold">MenuFlow Demo</h4>
                    <Link
                      href="https://restaurants.smeanalytica.dev"
                      className="text-sm text-primary hover:underline"
                      target="_blank"
                    >
                      restaurants.smeanalytica.dev
                    </Link>
                  </div>
                  <div>
                    <h4 className="font-semibold">Documentation</h4>
                    <Link
                      href="https://docs.smeanalytica.dev"
                      className="text-sm text-primary hover:underline"
                      target="_blank"
                    >
                      docs.smeanalytica.dev
                    </Link>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              Ready to Transform Your Business?
            </h2>
            <p className="mb-8 text-lg text-muted-foreground">
              Join thousands of businesses already using SME Analytica to make better decisions
            </p>
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button size="lg" asChild>
                <Link href="/modules">
                  Explore Solutions
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="mailto:<EMAIL>">
                  Schedule a Demo
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
