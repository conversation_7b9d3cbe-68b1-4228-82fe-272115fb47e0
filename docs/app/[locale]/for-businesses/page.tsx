import { <PERSON>ada<PERSON> } from "next";
import { <PERSON> } from "@/i18n/routing";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowRight,
  Building,
  TrendingUp,
  Users,
  DollarSign,
  BarChart3,
  Smartphone,
  Globe,
  CheckCircle,
  Star,
  Target,
} from "lucide-react";

export const metadata: Metadata = {
  title: "For Businesses",
  description: "Discover how SME Analytica helps businesses across industries make data-driven decisions and increase revenue through AI-powered insights.",
};

export default function ForBusinessesPage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-background via-background to-muted/20 py-20 md:py-32">
        <div className="container relative">
          <div className="mx-auto max-w-4xl text-center">
            <Badge variant="outline" className="mb-4">
              For Businesses
            </Badge>
            <h1 className="mb-6 text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl">
              Transform Your Business with AI
            </h1>
            <p className="mb-8 text-xl text-muted-foreground sm:text-2xl">
              Join thousands of businesses using SME Analytica to make data-driven decisions, 
              increase revenue, and stay ahead of the competition.
            </p>
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button size="lg" asChild>
                <Link href="/modules">
                  Explore Solutions
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="mailto:<EMAIL>">
                  Schedule Demo
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Industry Solutions */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              Industry Solutions
            </h2>
            <p className="mb-12 text-lg text-muted-foreground">
              Tailored AI solutions for different business types
            </p>
          </div>
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader>
                <Building className="h-8 w-8 text-primary" />
                <CardTitle>Restaurants & Food Service</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Dynamic pricing, QR menus, and customer sentiment analysis
                </p>
                <ul className="space-y-1 text-sm">
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>MenuFlow dynamic pricing</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>QR-based digital menus</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Customer review analysis</span>
                  </li>
                </ul>
                <Button asChild className="w-full">
                  <Link href="/modules/menuflow">Learn More</Link>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <Users className="h-8 w-8 text-primary" />
                <CardTitle>Retail & E-commerce</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Market analysis, competitor tracking, and sales forecasting
                </p>
                <ul className="space-y-1 text-sm">
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Competitor benchmarking</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Sales forecasting</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Market trend analysis</span>
                  </li>
                </ul>
                <Button asChild className="w-full">
                  <Link href="/modules/sme-app">Learn More</Link>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <Globe className="h-8 w-8 text-primary" />
                <CardTitle>Service Businesses</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Customer insights, service optimization, and growth analytics
                </p>
                <ul className="space-y-1 text-sm">
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Customer sentiment tracking</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Service quality analysis</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Growth opportunity identification</span>
                  </li>
                </ul>
                <Button asChild className="w-full">
                  <Link href="/modules/connecto">Learn More</Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="bg-muted/30 py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              Why Choose SME Analytica?
            </h2>
            <p className="mb-12 text-lg text-muted-foreground">
              Proven results for businesses of all sizes
            </p>
          </div>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            {[
              {
                icon: TrendingUp,
                title: "Increase Revenue",
                description: "Up to 25% revenue increase through dynamic pricing and optimization",
                metric: "25%",
              },
              {
                icon: Users,
                title: "Better Decisions",
                description: "Data-driven insights replace guesswork and intuition",
                metric: "100%",
              },
              {
                icon: DollarSign,
                title: "Cost Effective",
                description: "Enterprise-level analytics at SME-friendly pricing",
                metric: "90%",
              },
              {
                icon: BarChart3,
                title: "Real-time Insights",
                description: "Live data and instant notifications for critical changes",
                metric: "24/7",
              },
            ].map((benefit, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <benefit.icon className="mx-auto h-12 w-12 text-primary" />
                  <div className="text-3xl font-bold text-primary">{benefit.metric}</div>
                  <CardTitle className="text-lg">{benefit.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">{benefit.description}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Success Stories */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-4xl">
            <h2 className="mb-8 text-3xl font-bold tracking-tight sm:text-4xl text-center">
              Success Stories
            </h2>
            <div className="grid gap-8 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <div className="flex items-center space-x-2">
                    <Star className="h-5 w-5 text-yellow-500" />
                    <Star className="h-5 w-5 text-yellow-500" />
                    <Star className="h-5 w-5 text-yellow-500" />
                    <Star className="h-5 w-5 text-yellow-500" />
                    <Star className="h-5 w-5 text-yellow-500" />
                  </div>
                  <CardTitle>Restaurant Chain - Spain</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-4">
                    "MenuFlow's dynamic pricing helped us increase revenue by 22% during peak hours 
                    while maintaining customer satisfaction. The QR menu system reduced wait times 
                    and improved the overall dining experience."
                  </p>
                  <div className="flex items-center space-x-4 text-sm">
                    <Badge variant="secondary">+22% Revenue</Badge>
                    <Badge variant="secondary">-30% Wait Time</Badge>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <div className="flex items-center space-x-2">
                    <Star className="h-5 w-5 text-yellow-500" />
                    <Star className="h-5 w-5 text-yellow-500" />
                    <Star className="h-5 w-5 text-yellow-500" />
                    <Star className="h-5 w-5 text-yellow-500" />
                    <Star className="h-5 w-5 text-yellow-500" />
                  </div>
                  <CardTitle>Retail Business - SME App</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-4">
                    "The competitor analysis and market insights from SME App helped us identify 
                    new opportunities and adjust our strategy. We've seen significant improvement 
                    in our market position."
                  </p>
                  <div className="flex items-center space-x-4 text-sm">
                    <Badge variant="secondary">+15% Market Share</Badge>
                    <Badge variant="secondary">Better Positioning</Badge>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Getting Started */}
      <section className="bg-muted/30 py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              Get Started Today
            </h2>
            <p className="mb-12 text-lg text-muted-foreground">
              Choose the solution that fits your business needs
            </p>
          </div>
          <div className="grid gap-8 md:grid-cols-3">
            {[
              {
                title: "MenuFlow",
                description: "Perfect for restaurants and food service businesses",
                price: "Contact for pricing",
                features: ["Dynamic pricing", "QR menus", "Analytics dashboard", "POS integration"],
                cta: "Try MenuFlow",
                href: "https://restaurants.smeanalytica.dev",
                external: true,
              },
              {
                title: "SME App",
                description: "Mobile business intelligence for all business types",
                price: "Free trial available",
                features: ["Market analysis", "Competitor tracking", "Sales forecasting", "Mobile access"],
                cta: "Download App",
                href: "https://testflight.apple.com/join/kCjhqR4Q",
                external: true,
              },
              {
                title: "Custom Solution",
                description: "Tailored AI solutions for enterprise needs",
                price: "Custom pricing",
                features: ["Custom integrations", "Dedicated support", "Advanced analytics", "White-label options"],
                cta: "Contact Sales",
                href: "mailto:<EMAIL>",
                external: true,
              },
            ].map((plan, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <CardTitle className="text-xl">{plan.title}</CardTitle>
                  <CardDescription>{plan.description}</CardDescription>
                  <div className="text-2xl font-bold text-primary">{plan.price}</div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ul className="space-y-2 text-sm">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button asChild className="w-full">
                    {plan.external ? (
                      <a href={plan.href} target="_blank" rel="noopener noreferrer">
                        {plan.cta}
                      </a>
                    ) : (
                      <Link href={plan.href}>{plan.cta}</Link>
                    )}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              Ready to Transform Your Business?
            </h2>
            <p className="mb-8 text-lg text-muted-foreground">
              Join the AI revolution and start making data-driven decisions today
            </p>
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button size="lg" asChild>
                <Link href="/modules">
                  Explore Solutions
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="mailto:<EMAIL>">
                  Contact Sales
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
