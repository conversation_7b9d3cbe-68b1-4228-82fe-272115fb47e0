import { <PERSON>ada<PERSON> } from "next";
import { <PERSON> } from "@/i18n/routing";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowRight,
  QrCode,
  TrendingUp,
  BarChart3,
  Users,
  Clock,
  DollarSign,
  Smartphone,
  Globe,
  Zap,
  CheckCircle,
  Settings,
  Brain,
} from "lucide-react";

export const metadata: Metadata = {
  title: "MenuFlow - Dynamic Restaurant Pricing",
  description: "MenuFlow is SME Analytica's dynamic pricing system for restaurants with QR-based live menus, POS integration, and real-time analytics.",
};

export default function MenuFlowPage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-background via-background to-muted/20 py-20 md:py-32">
        <div className="container relative">
          <div className="mx-auto max-w-4xl text-center">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <div className="p-2 rounded-lg bg-green-500 text-white">
                <QrCode className="h-6 w-6" />
              </div>
              <Badge variant="default">Live & Available</Badge>
            </div>
            <h1 className="mb-6 text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl">
              MenuFlow
            </h1>
            <p className="mb-8 text-xl text-muted-foreground sm:text-2xl">
              Dynamic pricing system for restaurants with QR-based live menus,
              POS integration, and real-time traffic analytics.
            </p>
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button size="lg" asChild>
                <Link href="https://restaurants.smeanalytica.dev" target="_blank">
                  Visit MenuFlow
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="#demo">
                  View Demo
                </Link>
              </Button>
              <Button variant="ghost" size="lg" asChild>
                <Link href="/integrations#menuflow">
                  API Docs
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Overview */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-4xl">
            <h2 className="mb-8 text-3xl font-bold tracking-tight sm:text-4xl">
              What is MenuFlow?
            </h2>
            <div className="mb-8 space-y-4 text-lg text-muted-foreground">
              <p>
                MenuFlow is a revolutionary restaurant management system that combines dynamic pricing,
                QR-based digital menus, and comprehensive analytics to maximize revenue and improve customer experience.
              </p>
              <p>
                Built specifically for restaurants, cafes, and food service businesses, MenuFlow uses AI to
                optimize pricing in real-time based on demand, time of day, inventory levels, and market conditions.
              </p>
            </div>
            <div className="grid gap-6 md:grid-cols-3">
              <Card className="text-center">
                <CardHeader>
                  <TrendingUp className="mx-auto h-12 w-12 text-green-500" />
                  <CardTitle>Increase Revenue</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Up to 25% revenue increase through intelligent dynamic pricing
                  </CardDescription>
                </CardContent>
              </Card>
              <Card className="text-center">
                <CardHeader>
                  <Users className="mx-auto h-12 w-12 text-green-500" />
                  <CardTitle>Better Customer Experience</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    QR menus with real-time updates and personalized recommendations
                  </CardDescription>
                </CardContent>
              </Card>
              <Card className="text-center">
                <CardHeader>
                  <BarChart3 className="mx-auto h-12 w-12 text-green-500" />
                  <CardTitle>Data-Driven Insights</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Comprehensive analytics on sales, customer behavior, and market trends
                  </CardDescription>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Key Features */}
      <section className="bg-muted/30 py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              Key Features
            </h2>
            <p className="mb-12 text-lg text-muted-foreground">
              Everything you need to modernize your restaurant operations
            </p>
          </div>
          <div className="grid gap-8 md:grid-cols-2">
            <Card>
              <CardHeader>
                <DollarSign className="h-8 w-8 text-green-500" />
                <CardTitle>Dynamic Pricing Engine</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Real-time Price Optimization</h4>
                    <p className="text-sm text-muted-foreground">AI adjusts prices based on demand, time, and inventory</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Market-Based Pricing</h4>
                    <p className="text-sm text-muted-foreground">Competitor analysis and local market conditions</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Revenue Maximization</h4>
                    <p className="text-sm text-muted-foreground">Optimize for maximum profit margins</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <QrCode className="h-8 w-8 text-green-500" />
                <CardTitle>QR-Based Digital Menus</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Contactless Ordering</h4>
                    <p className="text-sm text-muted-foreground">Customers scan QR codes to view and order</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Real-time Updates</h4>
                    <p className="text-sm text-muted-foreground">Instant menu and price updates across all tables</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Multi-language Support</h4>
                    <p className="text-sm text-muted-foreground">Serve international customers in their language</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <BarChart3 className="h-8 w-8 text-green-500" />
                <CardTitle>Advanced Analytics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Sales Performance</h4>
                    <p className="text-sm text-muted-foreground">Track revenue, popular items, and trends</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Customer Behavior</h4>
                    <p className="text-sm text-muted-foreground">Understand ordering patterns and preferences</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Traffic Analytics</h4>
                    <p className="text-sm text-muted-foreground">Monitor peak hours and table turnover</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <Zap className="h-8 w-8 text-green-500" />
                <CardTitle>POS Integration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Seamless Integration</h4>
                    <p className="text-sm text-muted-foreground">Works with existing POS systems</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Order Management</h4>
                    <p className="text-sm text-muted-foreground">Streamlined order processing and tracking</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Inventory Sync</h4>
                    <p className="text-sm text-muted-foreground">Real-time inventory updates and alerts</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              How MenuFlow Works
            </h2>
            <p className="mb-12 text-lg text-muted-foreground">
              Simple setup, powerful results
            </p>
          </div>
          <div className="grid gap-8 md:grid-cols-4">
            {[
              {
                step: "1",
                title: "Setup & Integration",
                description: "Connect MenuFlow to your existing POS system and upload your menu",
                icon: Settings,
              },
              {
                step: "2",
                title: "Generate QR Codes",
                description: "Create unique QR codes for each table or location in your restaurant",
                icon: QrCode,
              },
              {
                step: "3",
                title: "AI Optimization",
                description: "Our AI analyzes data and optimizes pricing in real-time",
                icon: Brain,
              },
              {
                step: "4",
                title: "Monitor & Grow",
                description: "Track performance and watch your revenue increase",
                icon: TrendingUp,
              },
            ].map((step, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-500 text-2xl font-bold text-white">
                    {step.step}
                  </div>
                  <CardTitle className="text-lg">{step.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">{step.description}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Demo Section */}
      <section id="demo" className="bg-muted/30 py-20">
        <div className="container">
          <div className="mx-auto max-w-4xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              See MenuFlow in Action
            </h2>
            <p className="mb-8 text-lg text-muted-foreground">
              Experience the power of dynamic pricing and QR menus
            </p>
            <Card className="p-8">
              <div className="aspect-video bg-muted rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <Globe className="mx-auto h-16 w-16 text-muted-foreground mb-4" />
                  <p className="text-lg font-semibold">Interactive Demo</p>
                  <p className="text-muted-foreground">Visit restaurants.smeanalytica.dev to try MenuFlow</p>
                  <Button className="mt-4" asChild>
                    <Link href="https://restaurants.smeanalytica.dev" target="_blank">
                      Launch Demo
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              Ready to Revolutionize Your Restaurant?
            </h2>
            <p className="mb-8 text-lg text-muted-foreground">
              Join restaurants already using MenuFlow to increase revenue and improve customer experience
            </p>
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button size="lg" asChild>
                <Link href="https://restaurants.smeanalytica.dev" target="_blank">
                  Get Started with MenuFlow
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/for-businesses">
                  Learn More
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
