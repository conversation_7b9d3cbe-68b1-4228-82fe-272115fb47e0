import { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowRight,
  BarChart3,
  QrCode,
  Smartphone,
  Mic,
  Calendar,
  TrendingUp,
  Users,
  Globe,
  Zap,
} from "lucide-react";

export const metadata: Metadata = {
  title: "Modules",
  description: "Explore SME Analytica's comprehensive suite of business intelligence modules for different industries and use cases.",
};

export default function ModulesPage() {
  const modules = [
    {
      id: "menuflow",
      title: "MenuFlow",
      description: "Dynamic pricing system for restaurants with QR-based live menus and POS integration",
      status: "Live",
      statusVariant: "default" as const,
      icon: QrCode,
      features: ["Dynamic Pricing", "QR Menus", "POS Integration", "Traffic Analytics", "Order Management"],
      targetAudience: "Restaurants, Cafes, Food Trucks",
      link: "https://restaurants.smeanalytica.dev",
      demoAvailable: true,
      color: "bg-green-500",
    },
    {
      id: "sme-app",
      title: "SME App",
      description: "Mobile business analytics with sales analysis, customer sentiment tracking, and competitor benchmarking",
      status: "Live",
      statusVariant: "default" as const,
      icon: Smartphone,
      features: ["Sales Analysis", "Sentiment Tracking", "Competitor Benchmarking", "KPI Dashboards", "Real-time Insights"],
      targetAudience: "All Business Types",
      link: "/modules/sme-app",
      demoAvailable: true,
      color: "bg-blue-500",
    },
    {
      id: "connecto",
      title: "Connecto",
      description: "AI voice receptionist for automated customer service across different business types",
      status: "Coming Soon",
      statusVariant: "outline" as const,
      icon: Mic,
      features: ["Voice AI", "Multi-language Support", "Business Integration", "24/7 Availability", "Call Analytics"],
      targetAudience: "Restaurants, Salons, Clinics, Service Businesses",
      link: "/modules/connecto",
      demoAvailable: false,
      color: "bg-purple-500",
    },
    {
      id: "ticketing",
      title: "Event Ticketing Platform",
      description: "Dynamic pricing platform for events with AI-powered demand forecasting and revenue optimization",
      status: "Planned",
      statusVariant: "outline" as const,
      icon: Calendar,
      features: ["Dynamic Pricing", "Event Management", "Demand Forecasting", "Revenue Analytics", "Customer Insights"],
      targetAudience: "Event Organizers, Clubs, Venues",
      link: "/modules/ticketing",
      demoAvailable: false,
      color: "bg-orange-500",
    },
  ];

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-background via-background to-muted/20 py-20 md:py-32">
        <div className="container relative">
          <div className="mx-auto max-w-4xl text-center">
            <Badge variant="outline" className="mb-4">
              Business Modules
            </Badge>
            <h1 className="mb-6 text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl">
              Comprehensive Business Solutions
            </h1>
            <p className="mb-8 text-xl text-muted-foreground sm:text-2xl">
              Explore our suite of AI-powered modules designed for different industries and business needs. 
              Each module can work independently or as part of an integrated solution.
            </p>
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button size="lg" asChild>
                <Link href="/for-businesses">
                  For Businesses
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/integrations">
                  API Documentation
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Modules Grid */}
      <section className="py-20">
        <div className="container">
          <div className="grid gap-8 md:grid-cols-2">
            {modules.map((module) => (
              <Card key={module.id} className="group hover:shadow-lg transition-all duration-300">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded-lg ${module.color} text-white`}>
                        <module.icon className="h-6 w-6" />
                      </div>
                      <CardTitle className="text-2xl">{module.title}</CardTitle>
                    </div>
                    <Badge variant={module.statusVariant}>{module.status}</Badge>
                  </div>
                  <CardDescription className="text-base mt-2">
                    {module.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <h4 className="font-semibold mb-2">Key Features</h4>
                    <div className="flex flex-wrap gap-2">
                      {module.features.map((feature) => (
                        <Badge key={feature} variant="secondary" className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold mb-1">Target Audience</h4>
                    <p className="text-sm text-muted-foreground">{module.targetAudience}</p>
                  </div>

                  <div className="flex gap-2">
                    <Button asChild className="flex-1">
                      <Link href={module.link}>
                        Learn More
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                    {module.demoAvailable && (
                      <Button variant="outline" asChild>
                        <Link href={`${module.link}#demo`}>
                          Demo
                        </Link>
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Integration Benefits */}
      <section className="bg-muted/30 py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              Integrated Business Intelligence
            </h2>
            <p className="mb-12 text-lg text-muted-foreground">
              While each module works independently, they're designed to work together for comprehensive business insights
            </p>
          </div>
          <div className="grid gap-6 md:grid-cols-3">
            {[
              {
                icon: BarChart3,
                title: "Unified Analytics",
                description: "All modules feed into a central analytics dashboard for comprehensive business insights.",
              },
              {
                icon: Users,
                title: "Shared Customer Data",
                description: "Customer interactions across modules provide a complete view of your business relationships.",
              },
              {
                icon: Zap,
                title: "Cross-Module Automation",
                description: "Automate workflows that span multiple modules for maximum efficiency.",
              },
            ].map((benefit, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <benefit.icon className="mx-auto h-12 w-12 text-primary" />
                  <CardTitle className="text-lg">{benefit.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">{benefit.description}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Module Comparison */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-4xl">
            <h2 className="mb-8 text-3xl font-bold tracking-tight sm:text-4xl text-center">
              Choose the Right Module
            </h2>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-4 font-semibold">Module</th>
                    <th className="text-left p-4 font-semibold">Best For</th>
                    <th className="text-left p-4 font-semibold">Key Benefit</th>
                    <th className="text-left p-4 font-semibold">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {modules.map((module) => (
                    <tr key={module.id} className="border-b hover:bg-muted/50">
                      <td className="p-4">
                        <div className="flex items-center space-x-2">
                          <div className={`p-1 rounded ${module.color} text-white`}>
                            <module.icon className="h-4 w-4" />
                          </div>
                          <span className="font-medium">{module.title}</span>
                        </div>
                      </td>
                      <td className="p-4 text-sm text-muted-foreground">{module.targetAudience}</td>
                      <td className="p-4 text-sm">{module.features[0]}</td>
                      <td className="p-4">
                        <Badge variant={module.statusVariant} className="text-xs">
                          {module.status}
                        </Badge>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-muted/30 py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              Ready to Transform Your Business?
            </h2>
            <p className="mb-8 text-lg text-muted-foreground">
              Start with one module or implement a complete solution. Our team is here to help you succeed.
            </p>
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button size="lg" asChild>
                <Link href="/for-businesses">
                  Get Started
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/about">
                  Contact Us
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
