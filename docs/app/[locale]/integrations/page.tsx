import { Metada<PERSON> } from "next";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowRight,
  Code,
  Database,
  Webhook,
  Key,
  Globe,
  Smartphone,
  Zap,
  Book,
  ExternalLink,
} from "lucide-react";

export const metadata: Metadata = {
  title: "Integrations & API Documentation",
  description: "Learn how to integrate SME Analytica's APIs, webhooks, and SDKs into your applications and business systems.",
};

export default function IntegrationsPage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-background via-background to-muted/20 py-20 md:py-32">
        <div className="container relative">
          <div className="mx-auto max-w-4xl text-center">
            <Badge variant="outline" className="mb-4">
              Developer Resources
            </Badge>
            <h1 className="mb-6 text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl">
              Integrations & APIs
            </h1>
            <p className="mb-8 text-xl text-muted-foreground sm:text-2xl">
              Powerful APIs, webhooks, and SDKs to integrate SME Analytica's
              AI-powered business intelligence into your applications.
            </p>
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button size="lg" asChild>
                <Link href="https://api.smeanalytica.dev/docs" target="_blank">
                  Live API Docs
                  <ExternalLink className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="#quickstart">
                  Quick Start
                </Link>
              </Button>
              <Button variant="ghost" size="lg" asChild>
                <Link href="#examples">
                  Code Examples
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* API Overview */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-4xl">
            <h2 className="mb-8 text-3xl font-bold tracking-tight sm:text-4xl">
              API Overview
            </h2>
            <div className="mb-8 text-lg text-muted-foreground">
              <p>
                SME Analytica provides a comprehensive RESTful API that allows you to access
                all platform features programmatically. Our API is designed with developers
                in mind, featuring clear documentation, consistent responses, and robust error handling.
              </p>
            </div>
            <div className="grid gap-6 md:grid-cols-3">
              <Card className="text-center">
                <CardHeader>
                  <Globe className="mx-auto h-12 w-12 text-primary" />
                  <CardTitle>RESTful API</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Standard HTTP methods with JSON responses and comprehensive error handling
                  </CardDescription>
                </CardContent>
              </Card>
              <Card className="text-center">
                <CardHeader>
                  <Key className="mx-auto h-12 w-12 text-primary" />
                  <CardTitle>Secure Authentication</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    JWT-based authentication with API keys and role-based access control
                  </CardDescription>
                </CardContent>
              </Card>
              <Card className="text-center">
                <CardHeader>
                  <Book className="mx-auto h-12 w-12 text-primary" />
                  <CardTitle>Interactive Docs</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Swagger/OpenAPI documentation with live testing capabilities
                  </CardDescription>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* API Endpoints */}
      <section className="bg-muted/30 py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              Core API Endpoints
            </h2>
            <p className="mb-12 text-lg text-muted-foreground">
              Access all SME Analytica features through our comprehensive API
            </p>
          </div>
          <div className="grid gap-8 md:grid-cols-2">
            <Card>
              <CardHeader>
                <Code className="h-8 w-8 text-primary" />
                <CardTitle>Authentication API</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-semibold">POST /api/v1/auth/login</h4>
                  <p className="text-sm text-muted-foreground">User authentication and token generation</p>
                </div>
                <div>
                  <h4 className="font-semibold">POST /api/v1/auth/register</h4>
                  <p className="text-sm text-muted-foreground">New user registration</p>
                </div>
                <div>
                  <h4 className="font-semibold">POST /api/v1/auth/refresh-token</h4>
                  <p className="text-sm text-muted-foreground">Token refresh for extended sessions</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <Database className="h-8 w-8 text-primary" />
                <CardTitle>Analysis API</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-semibold">POST /api/v1/analysis/new</h4>
                  <p className="text-sm text-muted-foreground">Create new business analysis</p>
                </div>
                <div>
                  <h4 className="font-semibold">GET /api/v1/analysis/history</h4>
                  <p className="text-sm text-muted-foreground">Retrieve analysis history</p>
                </div>
                <div>
                  <h4 className="font-semibold">GET /api/v1/analysis/{id}</h4>
                  <p className="text-sm text-muted-foreground">Get specific analysis results</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <Zap className="h-8 w-8 text-primary" />
                <CardTitle>Dynamic Pricing API</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-semibold">GET /api/v1/analysis/pricing</h4>
                  <p className="text-sm text-muted-foreground">Get current pricing recommendations</p>
                </div>
                <div>
                  <h4 className="font-semibold">POST /api/v1/analysis/pricing</h4>
                  <p className="text-sm text-muted-foreground">Request new pricing analysis</p>
                </div>
                <div>
                  <h4 className="font-semibold">GET /api/v1/analysis/pricing/history</h4>
                  <p className="text-sm text-muted-foreground">Historical pricing data</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <Smartphone className="h-8 w-8 text-primary" />
                <CardTitle>Profile & Subscription API</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-semibold">GET /api/v1/profile/me</h4>
                  <p className="text-sm text-muted-foreground">Get user profile information</p>
                </div>
                <div>
                  <h4 className="font-semibold">PUT /api/v1/profile/me</h4>
                  <p className="text-sm text-muted-foreground">Update user profile</p>
                </div>
                <div>
                  <h4 className="font-semibold">GET /api/v1/subscription</h4>
                  <p className="text-sm text-muted-foreground">Subscription status and usage</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Quick Start */}
      <section id="quickstart" className="py-20">
        <div className="container">
          <div className="mx-auto max-w-4xl">
            <h2 className="mb-8 text-3xl font-bold tracking-tight sm:text-4xl">
              Quick Start Guide
            </h2>
            <div className="space-y-8">
              <Card>
                <CardHeader>
                  <CardTitle>1. Authentication</CardTitle>
                  <CardDescription>Get your API access token</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="bg-muted p-4 rounded-lg">
                    <pre className="text-sm overflow-x-auto">
{`curl -X POST https://api.smeanalytica.dev/api/v1/auth/login \\
  -H "Content-Type: application/json" \\
  -d '{
    "email": "<EMAIL>",
    "password": "your-password"
  }'`}
                    </pre>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>2. Create Analysis</CardTitle>
                  <CardDescription>Request a business analysis</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="bg-muted p-4 rounded-lg">
                    <pre className="text-sm overflow-x-auto">
{`curl -X POST https://api.smeanalytica.dev/api/v1/analysis/new \\
  -H "Authorization: Bearer YOUR_TOKEN" \\
  -H "Content-Type: application/json" \\
  -d '{
    "type": "sentiment",
    "data": {
      "business_name": "Your Business",
      "location": "Your Location"
    }
  }'`}
                    </pre>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>3. Get Results</CardTitle>
                  <CardDescription>Retrieve analysis results</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="bg-muted p-4 rounded-lg">
                    <pre className="text-sm overflow-x-auto">
{`curl -X GET https://api.smeanalytica.dev/api/v1/analysis/ANALYSIS_ID \\
  -H "Authorization: Bearer YOUR_TOKEN"`}
                    </pre>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Webhooks */}
      <section className="bg-muted/30 py-20">
        <div className="container">
          <div className="mx-auto max-w-4xl">
            <h2 className="mb-8 text-3xl font-bold tracking-tight sm:text-4xl">
              Webhooks
            </h2>
            <Card>
              <CardHeader>
                <Webhook className="h-8 w-8 text-primary" />
                <CardTitle>Real-time Notifications</CardTitle>
                <CardDescription>
                  Get notified when analysis completes, pricing updates, or other events occur
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h4 className="font-semibold mb-2">Supported Events</h4>
                  <ul className="space-y-1 text-sm text-muted-foreground">
                    <li>• <code>analysis.completed</code> - Analysis processing finished</li>
                    <li>• <code>pricing.updated</code> - Dynamic pricing recommendations updated</li>
                    <li>• <code>subscription.changed</code> - Subscription status changed</li>
                    <li>• <code>usage.limit_reached</code> - API usage limit reached</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Webhook Payload Example</h4>
                  <div className="bg-muted p-4 rounded-lg">
                    <pre className="text-sm overflow-x-auto">
{`{
  "event": "analysis.completed",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "analysis_id": "analysis_123",
    "type": "sentiment",
    "status": "completed",
    "confidence_score": 0.85
  }
}`}
                    </pre>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* SDKs and Libraries */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              SDKs & Libraries
            </h2>
            <p className="mb-12 text-lg text-muted-foreground">
              Official SDKs and community libraries for popular programming languages
            </p>
          </div>
          <div className="grid gap-6 md:grid-cols-3">
            {[
              {
                title: "JavaScript/Node.js",
                description: "Official SDK for JavaScript and Node.js applications",
                status: "Available",
                command: "npm install @smeanalytica/sdk",
              },
              {
                title: "Python",
                description: "Python SDK for data science and backend applications",
                status: "Available",
                command: "pip install smeanalytica-python",
              },
              {
                title: "React Native",
                description: "Mobile SDK for React Native applications",
                status: "Coming Soon",
                command: "npm install @smeanalytica/react-native",
              },
            ].map((sdk, index) => (
              <Card key={index}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{sdk.title}</CardTitle>
                    <Badge variant={sdk.status === "Available" ? "default" : "outline"}>
                      {sdk.status}
                    </Badge>
                  </div>
                  <CardDescription>{sdk.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="bg-muted p-2 rounded text-sm font-mono">
                    {sdk.command}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-muted/30 py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              Ready to Start Building?
            </h2>
            <p className="mb-8 text-lg text-muted-foreground">
              Explore our comprehensive API documentation and start integrating today
            </p>
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button size="lg" asChild>
                <Link href="https://api.smeanalytica.dev/docs" target="_blank">
                  Explore API Docs
                  <ExternalLink className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/about">
                  Contact Support
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
