/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/layout",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fapp%2Fglobals.css&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fcomponents%2Fnavigation.tsx&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fcomponents%2Ftheme-provider.tsx&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fnavigation%2Fshared%2FBaseLink.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fnavigation%2Fshared%2FLegacyBaseLink.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2F%5Blocale%5D%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=false!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fapp%2Fglobals.css&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fcomponents%2Fnavigation.tsx&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fcomponents%2Ftheme-provider.tsx&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fnavigation%2Fshared%2FBaseLink.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fnavigation%2Fshared%2FLegacyBaseLink.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2F%5Blocale%5D%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=false! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/globals.css */ \"(app-pages-browser)/./app/globals.css\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/navigation.tsx */ \"(app-pages-browser)/./components/navigation.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(app-pages-browser)/./components/theme-provider.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js */ \"(app-pages-browser)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js */ \"(app-pages-browser)/./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(app-pages-browser)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"app/[locale]/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/[locale]/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fapp%2Fglobals.css&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fcomponents%2Fnavigation.tsx&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fcomponents%2Ftheme-provider.tsx&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fnavigation%2Fshared%2FBaseLink.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fnavigation%2Fshared%2FLegacyBaseLink.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2F%5Blocale%5D%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=false!\n"));

/***/ })

});