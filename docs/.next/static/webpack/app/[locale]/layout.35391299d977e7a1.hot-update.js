"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"d77c9ead38c3\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vYXBwL2dsb2JhbHMuY3NzP2I1MjEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJkNzdjOWVhZDM4YzNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/navigation.tsx":
/*!***********************************!*\
  !*** ./components/navigation.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navigation: function() { return /* binding */ Navigation; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _i18n_routing__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/i18n/routing */ \"(app-pages-browser)/./i18n/routing.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_theme_toggle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/theme-toggle */ \"(app-pages-browser)/./components/theme-toggle.tsx\");\n/* harmony import */ var _components_language_toggle__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/language-toggle */ \"(app-pages-browser)/./components/language-toggle.tsx\");\n/* harmony import */ var _components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/navigation-menu */ \"(app-pages-browser)/./components/ui/navigation-menu.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./components/ui/sheet.tsx\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Building_Code_Menu_Settings_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Building,Code,Menu,Settings,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Building_Code_Menu_Settings_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Building,Code,Menu,Settings,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Building_Code_Menu_Settings_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Building,Code,Menu,Settings,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Building_Code_Menu_Settings_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Building,Code,Menu,Settings,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Building_Code_Menu_Settings_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Building,Code,Menu,Settings,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Building_Code_Menu_Settings_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Building,Code,Menu,Settings,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Building_Code_Menu_Settings_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Building,Code,Menu,Settings,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ Navigation auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst navigationItems = [\n    {\n        title: \"Platform\",\n        href: \"/platform\",\n        description: \"Overview of SME Analytica platform\",\n        icon: _barrel_optimize_names_BookOpen_Building_Code_Menu_Settings_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        title: \"Modules\",\n        href: \"/modules\",\n        description: \"Explore our business modules\",\n        icon: _barrel_optimize_names_BookOpen_Building_Code_Menu_Settings_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        items: [\n            {\n                title: \"MenuFlow\",\n                href: \"/modules/menuflow\",\n                description: \"Dynamic pricing for restaurants\"\n            },\n            {\n                title: \"SME App\",\n                href: \"/modules/sme-app\",\n                description: \"Mobile business analytics\"\n            },\n            {\n                title: \"Connecto\",\n                href: \"/modules/connecto\",\n                description: \"AI voice receptionist\"\n            },\n            {\n                title: \"Event Ticketing\",\n                href: \"/modules/ticketing\",\n                description: \"Event management platform\"\n            }\n        ]\n    },\n    {\n        title: \"AI & Automation\",\n        href: \"/ai-automation\",\n        description: \"AI capabilities and automation\",\n        icon: _barrel_optimize_names_BookOpen_Building_Code_Menu_Settings_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        title: \"Integrations\",\n        href: \"/integrations\",\n        description: \"API docs and integrations\",\n        icon: _barrel_optimize_names_BookOpen_Building_Code_Menu_Settings_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    },\n    {\n        title: \"For Businesses\",\n        href: \"/for-businesses\",\n        description: \"Use cases and benefits\",\n        icon: _barrel_optimize_names_BookOpen_Building_Code_Menu_Settings_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n    },\n    {\n        title: \"About\",\n        href: \"/about\",\n        description: \"About SME Analytica\",\n        icon: _barrel_optimize_names_BookOpen_Building_Code_Menu_Settings_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    }\n];\nfunction Navigation() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_15__.useTranslations)(\"navigation\");\n    const pathname = (0,_i18n_routing__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container flex h-14 items-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mr-4 hidden md:flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_2__.Link, {\n                            href: \"/\",\n                            className: \"mr-6 flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-6 w-6 rounded bg-primary\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"hidden font-bold sm:inline-block\",\n                                    children: \"SME Analytica\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenu, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuList, {\n                                children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuItem, {\n                                        children: item.items ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuTrigger, {\n                                                    children: item.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuContent, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]\",\n                                                        children: item.items.map((subItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ListItem, {\n                                                                title: subItem.title,\n                                                                href: subItem.href,\n                                                                children: subItem.description\n                                                            }, subItem.href, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                                                lineNumber: 113,\n                                                                columnNumber: 29\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuLink, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_2__.Link, {\n                                                href: item.href,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\", pathname === item.href && \"bg-accent\"),\n                                                children: item.title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, item.href, false, {\n                                        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__.Sheet, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__.SheetTrigger, {\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                className: \"mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Building_Code_Menu_Settings_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Toggle Menu\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__.SheetContent, {\n                            side: \"left\",\n                            className: \"pr-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__.SheetHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__.SheetTitle, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_2__.Link, {\n                                                href: \"/\",\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-6 w-6 rounded bg-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-bold\",\n                                                        children: \"SME Analytica\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__.SheetDescription, {\n                                            children: \"AI Business Intelligence Platform\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-4 h-[calc(100vh-8rem)] pb-10 pl-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col space-y-3\",\n                                        children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_2__.Link, {\n                                                        href: item.href,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex items-center space-x-2 text-sm font-medium\", pathname === item.href && \"text-primary\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                                                lineNumber: 175,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: item.title\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                                                lineNumber: 176,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    item.items && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-6 mt-2 space-y-2\",\n                                                        children: item.items.map((subItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_2__.Link, {\n                                                                href: subItem.href,\n                                                                className: \"block text-sm text-muted-foreground hover:text-foreground\",\n                                                                children: subItem.title\n                                                            }, subItem.href, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                                                lineNumber: 181,\n                                                                columnNumber: 27\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, item.href, true, {\n                                                fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-1 items-center justify-between space-x-2 md:justify-end\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full flex-1 md:w-auto md:flex-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_2__.Link, {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2 md:hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 w-6 rounded bg-primary\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-bold\",\n                                        children: \"SME Analytica\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_language_toggle__WEBPACK_IMPORTED_MODULE_6__.LanguageToggle, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_toggle__WEBPACK_IMPORTED_MODULE_5__.ThemeToggle, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    asChild: true,\n                                    size: \"sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_2__.Link, {\n                                        href: \"https://api.smeanalytica.dev/docs\",\n                                        target: \"_blank\",\n                                        children: \"API Docs\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n            lineNumber: 95,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n_s(Navigation, \"+KcrlTvLOUYo5s7HuulzDK2y3S8=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_15__.useTranslations,\n        _i18n_routing__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = Navigation;\nconst ListItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef((param, ref)=>{\n    let { className, title, children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuLink, {\n            asChild: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_2__.Link, {\n                ref: ref,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground\", className),\n                ...props,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-medium leading-none\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"line-clamp-2 text-sm leading-snug text-muted-foreground\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                lineNumber: 229,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n            lineNumber: 228,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n        lineNumber: 227,\n        columnNumber: 5\n    }, undefined);\n});\n_c1 = ListItem;\nListItem.displayName = \"ListItem\";\nvar _c, _c1;\n$RefreshReg$(_c, \"Navigation\");\n$RefreshReg$(_c1, \"ListItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/navigation.tsx\n"));

/***/ })

});