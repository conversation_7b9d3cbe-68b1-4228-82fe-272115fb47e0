globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/[locale]/about/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/navigation.tsx":{"*":{"id":"(ssr)/./components/navigation.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/theme-provider.tsx":{"*":{"id":"(ssr)/./components/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js":{"*":{"id":"(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js":{"*":{"id":"(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js":{"*":{"id":"(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/Analytica/docs/node_modules/next/dist/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Analytica/docs/node_modules/next/dist/esm/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Analytica/docs/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Analytica/docs/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Analytica/docs/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Analytica/docs/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Analytica/docs/node_modules/next/dist/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Analytica/docs/node_modules/next/dist/esm/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Analytica/docs/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Analytica/docs/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Analytica/docs/node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Analytica/docs/node_modules/next/dist/esm/client/components/static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Analytica/docs/app/globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"/Users/<USER>/Analytica/docs/components/navigation.tsx":{"id":"(app-pages-browser)/./components/navigation.tsx","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"/Users/<USER>/Analytica/docs/components/theme-provider.tsx":{"id":"(app-pages-browser)/./components/theme-provider.tsx","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"/Users/<USER>/Analytica/docs/node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js":{"id":"(app-pages-browser)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js","name":"*","chunks":["app/[locale]/about/page","static/chunks/app/%5Blocale%5D/about/page.js"],"async":false},"/Users/<USER>/Analytica/docs/node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js":{"id":"(app-pages-browser)/./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js","name":"*","chunks":["app/[locale]/about/page","static/chunks/app/%5Blocale%5D/about/page.js"],"async":false},"/Users/<USER>/Analytica/docs/node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js":{"id":"(app-pages-browser)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js","name":"*","chunks":["app/[locale]/page","static/chunks/app/%5Blocale%5D/page.js"],"async":false},"/Users/<USER>/Analytica/docs/node_modules/next/font/google/target.css?{\"path\":\"app/[locale]/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app/[locale]/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false}},"entryCSSFiles":{"/Users/<USER>/Analytica/docs/app/[locale]/layout":["static/css/app/[locale]/layout.css"],"/Users/<USER>/Analytica/docs/app/[locale]/page":[],"/Users/<USER>/Analytica/docs/app/[locale]/about/page":[]}}