/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[locale]/ai-automation/page";
exports.ids = ["app/[locale]/ai-automation/page"];
exports.modules = {

/***/ "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$":
/*!********************************************************!*\
  !*** ./messages/ lazy ^\.\/.*\.json$ namespace object ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./en.json": [
		"(rsc)/./messages/en.json",
		"_rsc_messages_en_json"
	],
	"./es.json": [
		"(rsc)/./messages/es.json",
		"_rsc_messages_es_json"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__.t(id, 3 | 16);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fai-automation%2Fpage&page=%2F%5Blocale%5D%2Fai-automation%2Fpage&appPaths=%2F%5Blocale%5D%2Fai-automation%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fai-automation%2Fpage.tsx&appDir=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Folayinka%2FAnalytica%2Fdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fai-automation%2Fpage&page=%2F%5Blocale%5D%2Fai-automation%2Fpage&appPaths=%2F%5Blocale%5D%2Fai-automation%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fai-automation%2Fpage.tsx&appDir=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Folayinka%2FAnalytica%2Fdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[locale]',\n        {\n        children: [\n        'ai-automation',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/ai-automation/page.tsx */ \"(rsc)/./app/[locale]/ai-automation/page.tsx\")), \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/layout.tsx */ \"(rsc)/./app/[locale]/layout.tsx\")), \"/Users/<USER>/Analytica/docs/app/[locale]/layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/[locale]/ai-automation/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[locale]/ai-automation/page\",\n        pathname: \"/[locale]/ai-automation\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fai-automation%2Fpage&page=%2F%5Blocale%5D%2Fai-automation%2Fpage&appPaths=%2F%5Blocale%5D%2Fai-automation%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fai-automation%2Fpage.tsx&appDir=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Folayinka%2FAnalytica%2Fdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fapp%2Fglobals.css&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fcomponents%2Fnavigation.tsx&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fcomponents%2Ftheme-provider.tsx&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fnavigation%2Fshared%2FBaseLink.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fnavigation%2Fshared%2FLegacyBaseLink.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2F%5Blocale%5D%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fapp%2Fglobals.css&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fcomponents%2Fnavigation.tsx&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fcomponents%2Ftheme-provider.tsx&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fnavigation%2Fshared%2FBaseLink.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fnavigation%2Fshared%2FLegacyBaseLink.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2F%5Blocale%5D%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/navigation.tsx */ \"(ssr)/./components/navigation.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(ssr)/./components/theme-provider.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fapp%2Fglobals.css&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fcomponents%2Fnavigation.tsx&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fcomponents%2Ftheme-provider.tsx&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fnavigation%2Fshared%2FBaseLink.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fnavigation%2Fshared%2FLegacyBaseLink.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2F%5Blocale%5D%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fnavigation%2Fshared%2FBaseLink.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fnavigation%2Fshared%2FLegacyBaseLink.js&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fnavigation%2Fshared%2FBaseLink.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fnavigation%2Fshared%2FLegacyBaseLink.js&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZvbGF5aW5rYSUyRkFuYWx5dGljYSUyRmRvY3MlMkZub2RlX21vZHVsZXMlMkZuZXh0LWludGwlMkZkaXN0JTJGZXNtJTJGbmF2aWdhdGlvbiUyRnNoYXJlZCUyRkJhc2VMaW5rLmpzJm1vZHVsZXM9JTJGVXNlcnMlMkZvbGF5aW5rYSUyRkFuYWx5dGljYSUyRmRvY3MlMkZub2RlX21vZHVsZXMlMkZuZXh0LWludGwlMkZkaXN0JTJGZXNtJTJGbmF2aWdhdGlvbiUyRnNoYXJlZCUyRkxlZ2FjeUJhc2VMaW5rLmpzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwT0FBaUk7QUFDakkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zbWUtYW5hbHl0aWNhLWRvY3MvPzQ1OTIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvb2xheWlua2EvQW5hbHl0aWNhL2RvY3Mvbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9uYXZpZ2F0aW9uL3NoYXJlZC9CYXNlTGluay5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL29sYXlpbmthL0FuYWx5dGljYS9kb2NzL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vbmF2aWdhdGlvbi9zaGFyZWQvTGVnYWN5QmFzZUxpbmsuanNcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fnavigation%2Fshared%2FBaseLink.js&modules=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fnavigation%2Fshared%2FLegacyBaseLink.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/language-toggle.tsx":
/*!****************************************!*\
  !*** ./components/language-toggle.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageToggle: () => (/* binding */ LanguageToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _i18n_routing__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/i18n/routing */ \"(ssr)/./i18n/routing.ts\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _barrel_optimize_names_Languages_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Languages!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/languages.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(ssr)/./components/ui/dropdown-menu.tsx\");\n/* __next_internal_client_entry_do_not_use__ LanguageToggle auto */ \n\n\n\n\n\n\nconst languages = [\n    {\n        code: \"en\",\n        name: \"English\",\n        flag: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\"\n    },\n    {\n        code: \"es\",\n        name: \"Espa\\xf1ol\",\n        flag: \"\\uD83C\\uDDEA\\uD83C\\uDDF8\"\n    }\n];\nfunction LanguageToggle() {\n    const router = (0,_i18n_routing__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,_i18n_routing__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const currentLocale = (0,next_intl__WEBPACK_IMPORTED_MODULE_5__.useLocale)();\n    const switchLanguage = (locale)=>{\n        router.replace(pathname, {\n            locale\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenu, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuTrigger, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    variant: \"ghost\",\n                    size: \"icon\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Languages_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-[1.2rem] w-[1.2rem]\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Analytica/docs/components/language-toggle.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"sr-only\",\n                            children: \"Toggle language\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Analytica/docs/components/language-toggle.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Analytica/docs/components/language-toggle.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Analytica/docs/components/language-toggle.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuContent, {\n                align: \"end\",\n                children: languages.map((language)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                        onClick: ()=>switchLanguage(language.code),\n                        className: currentLocale === language.code ? \"bg-accent\" : \"\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-2\",\n                                children: language.flag\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Analytica/docs/components/language-toggle.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, this),\n                            language.name\n                        ]\n                    }, language.code, true, {\n                        fileName: \"/Users/<USER>/Analytica/docs/components/language-toggle.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Analytica/docs/components/language-toggle.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/language-toggle.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/language-toggle.tsx\n");

/***/ }),

/***/ "(ssr)/./components/navigation.tsx":
/*!***********************************!*\
  !*** ./components/navigation.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navigation: () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _i18n_routing__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/i18n/routing */ \"(ssr)/./i18n/routing.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_theme_toggle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/theme-toggle */ \"(ssr)/./components/theme-toggle.tsx\");\n/* harmony import */ var _components_language_toggle__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/language-toggle */ \"(ssr)/./components/language-toggle.tsx\");\n/* harmony import */ var _components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/navigation-menu */ \"(ssr)/./components/ui/navigation-menu.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/sheet */ \"(ssr)/./components/ui/sheet.tsx\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Building_Code_Menu_Settings_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Building,Code,Menu,Settings,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Building_Code_Menu_Settings_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Building,Code,Menu,Settings,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Building_Code_Menu_Settings_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Building,Code,Menu,Settings,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Building_Code_Menu_Settings_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Building,Code,Menu,Settings,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Building_Code_Menu_Settings_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Building,Code,Menu,Settings,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Building_Code_Menu_Settings_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Building,Code,Menu,Settings,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Building_Code_Menu_Settings_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Building,Code,Menu,Settings,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ Navigation auto */ \n\n\n\n\n\n\n\n\n\n\nconst navigationItems = [\n    {\n        title: \"Platform\",\n        href: \"/platform\",\n        description: \"Overview of SME Analytica platform\",\n        icon: _barrel_optimize_names_BookOpen_Building_Code_Menu_Settings_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        title: \"Modules\",\n        href: \"/modules\",\n        description: \"Explore our business modules\",\n        icon: _barrel_optimize_names_BookOpen_Building_Code_Menu_Settings_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        items: [\n            {\n                title: \"MenuFlow\",\n                href: \"/modules/menuflow\",\n                description: \"Dynamic pricing for restaurants\"\n            },\n            {\n                title: \"SME App\",\n                href: \"/modules/sme-app\",\n                description: \"Mobile business analytics\"\n            },\n            {\n                title: \"Connecto\",\n                href: \"/modules/connecto\",\n                description: \"AI voice receptionist\"\n            },\n            {\n                title: \"Event Ticketing\",\n                href: \"/modules/ticketing\",\n                description: \"Event management platform\"\n            }\n        ]\n    },\n    {\n        title: \"AI & Automation\",\n        href: \"/ai-automation\",\n        description: \"AI capabilities and automation\",\n        icon: _barrel_optimize_names_BookOpen_Building_Code_Menu_Settings_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        title: \"Integrations\",\n        href: \"/integrations\",\n        description: \"API docs and integrations\",\n        icon: _barrel_optimize_names_BookOpen_Building_Code_Menu_Settings_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    },\n    {\n        title: \"For Businesses\",\n        href: \"/for-businesses\",\n        description: \"Use cases and benefits\",\n        icon: _barrel_optimize_names_BookOpen_Building_Code_Menu_Settings_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n    },\n    {\n        title: \"About\",\n        href: \"/about\",\n        description: \"About SME Analytica\",\n        icon: _barrel_optimize_names_BookOpen_Building_Code_Menu_Settings_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    }\n];\nfunction Navigation() {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_15__.useTranslations)(\"navigation\");\n    const pathname = (0,_i18n_routing__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container flex h-14 items-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mr-4 hidden md:flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_2__.Link, {\n                            href: \"/\",\n                            className: \"mr-6 flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-6 w-6 rounded bg-primary\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"hidden font-bold sm:inline-block\",\n                                    children: \"SME Analytica\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenu, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuList, {\n                                children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuItem, {\n                                        children: item.items ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuTrigger, {\n                                                    children: item.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuContent, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]\",\n                                                        children: item.items.map((subItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ListItem, {\n                                                                title: subItem.title,\n                                                                href: subItem.href,\n                                                                children: subItem.description\n                                                            }, subItem.href, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                                                lineNumber: 113,\n                                                                columnNumber: 29\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuLink, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_2__.Link, {\n                                                href: item.href,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\", pathname === item.href && \"bg-accent\"),\n                                                children: item.title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, item.href, false, {\n                                        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__.Sheet, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__.SheetTrigger, {\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                className: \"mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Building_Code_Menu_Settings_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Toggle Menu\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__.SheetContent, {\n                            side: \"left\",\n                            className: \"pr-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__.SheetHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__.SheetTitle, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_2__.Link, {\n                                                href: \"/\",\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-6 w-6 rounded bg-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-bold\",\n                                                        children: \"SME Analytica\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__.SheetDescription, {\n                                            children: \"AI Business Intelligence Platform\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-4 h-[calc(100vh-8rem)] pb-10 pl-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col space-y-3\",\n                                        children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_2__.Link, {\n                                                        href: item.href,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex items-center space-x-2 text-sm font-medium\", pathname === item.href && \"text-primary\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                                                lineNumber: 175,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: item.title\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                                                lineNumber: 176,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    item.items && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-6 mt-2 space-y-2\",\n                                                        children: item.items.map((subItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_2__.Link, {\n                                                                href: subItem.href,\n                                                                className: \"block text-sm text-muted-foreground hover:text-foreground\",\n                                                                children: subItem.title\n                                                            }, subItem.href, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                                                lineNumber: 181,\n                                                                columnNumber: 27\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, item.href, true, {\n                                                fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-1 items-center justify-between space-x-2 md:justify-end\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full flex-1 md:w-auto md:flex-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_2__.Link, {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2 md:hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 w-6 rounded bg-primary\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-bold\",\n                                        children: \"SME Analytica\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_language_toggle__WEBPACK_IMPORTED_MODULE_6__.LanguageToggle, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_toggle__WEBPACK_IMPORTED_MODULE_5__.ThemeToggle, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    asChild: true,\n                                    size: \"sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_2__.Link, {\n                                        href: \"https://api.smeanalytica.dev/docs\",\n                                        target: \"_blank\",\n                                        children: \"API Docs\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n            lineNumber: 95,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\nconst ListItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, title, children, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuLink, {\n            asChild: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                ref: ref,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground\", className),\n                ...props,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-medium leading-none\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"line-clamp-2 text-sm leading-snug text-muted-foreground\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n                lineNumber: 226,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n            lineNumber: 225,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/navigation.tsx\",\n        lineNumber: 224,\n        columnNumber: 5\n    }, undefined);\n});\nListItem.displayName = \"ListItem\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/navigation.tsx\n");

/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.module.js\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/theme-provider.tsx\",\n        lineNumber: 15,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThCO0FBQ21DO0FBVTFELFNBQVNDLGNBQWMsRUFBRUUsUUFBUSxFQUFFLEdBQUdDLE9BQTJCO0lBQ3RFLHFCQUFPLDhEQUFDRixzREFBa0JBO1FBQUUsR0FBR0UsS0FBSztrQkFBR0Q7Ozs7OztBQUN6QyIsInNvdXJjZXMiOlsid2VicGFjazovL3NtZS1hbmFseXRpY2EtZG9jcy8uL2NvbXBvbmVudHMvdGhlbWUtcHJvdmlkZXIudHN4PzkyODkiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgYXMgTmV4dFRoZW1lc1Byb3ZpZGVyIH0gZnJvbSBcIm5leHQtdGhlbWVzXCJcblxuaW50ZXJmYWNlIFRoZW1lUHJvdmlkZXJQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbiAgYXR0cmlidXRlPzogc3RyaW5nXG4gIGRlZmF1bHRUaGVtZT86IHN0cmluZ1xuICBlbmFibGVTeXN0ZW0/OiBib29sZWFuXG4gIGRpc2FibGVUcmFuc2l0aW9uT25DaGFuZ2U/OiBib29sZWFuXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBUaGVtZVByb3ZpZGVyKHsgY2hpbGRyZW4sIC4uLnByb3BzIH06IFRoZW1lUHJvdmlkZXJQcm9wcykge1xuICByZXR1cm4gPE5leHRUaGVtZXNQcm92aWRlciB7Li4ucHJvcHN9PntjaGlsZHJlbn08L05leHRUaGVtZXNQcm92aWRlcj5cbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlRoZW1lUHJvdmlkZXIiLCJOZXh0VGhlbWVzUHJvdmlkZXIiLCJjaGlsZHJlbiIsInByb3BzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/theme-toggle.tsx":
/*!*************************************!*\
  !*** ./components/theme-toggle.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeToggle: () => (/* binding */ ThemeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.module.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ ThemeToggle auto */ \n\n\n\n\nfunction ThemeToggle() {\n    const { setTheme, theme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n        variant: \"ghost\",\n        size: \"icon\",\n        onClick: ()=>setTheme(theme === \"light\" ? \"dark\" : \"light\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Analytica/docs/components/theme-toggle.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Analytica/docs/components/theme-toggle.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"sr-only\",\n                children: \"Toggle theme\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Analytica/docs/components/theme-toggle.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/theme-toggle.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXRvZ2dsZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUU4QjtBQUNVO0FBQ0Y7QUFFUztBQUV4QyxTQUFTSztJQUNkLE1BQU0sRUFBRUMsUUFBUSxFQUFFQyxLQUFLLEVBQUUsR0FBR0oscURBQVFBO0lBRXBDLHFCQUNFLDhEQUFDQyx5REFBTUE7UUFDTEksU0FBUTtRQUNSQyxNQUFLO1FBQ0xDLFNBQVMsSUFBTUosU0FBU0MsVUFBVSxVQUFVLFNBQVM7OzBCQUVyRCw4REFBQ0wsb0ZBQUdBO2dCQUFDUyxXQUFVOzs7Ozs7MEJBQ2YsOERBQUNWLG9GQUFJQTtnQkFBQ1UsV0FBVTs7Ozs7OzBCQUNoQiw4REFBQ0M7Z0JBQUtELFdBQVU7MEJBQVU7Ozs7Ozs7Ozs7OztBQUdoQyIsInNvdXJjZXMiOlsid2VicGFjazovL3NtZS1hbmFseXRpY2EtZG9jcy8uL2NvbXBvbmVudHMvdGhlbWUtdG9nZ2xlLnRzeD82Y2UxIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgeyBNb29uLCBTdW4gfSBmcm9tIFwibHVjaWRlLXJlYWN0XCJcbmltcG9ydCB7IHVzZVRoZW1lIH0gZnJvbSBcIm5leHQtdGhlbWVzXCJcblxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9idXR0b25cIlxuXG5leHBvcnQgZnVuY3Rpb24gVGhlbWVUb2dnbGUoKSB7XG4gIGNvbnN0IHsgc2V0VGhlbWUsIHRoZW1lIH0gPSB1c2VUaGVtZSgpXG5cbiAgcmV0dXJuIChcbiAgICA8QnV0dG9uXG4gICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgc2l6ZT1cImljb25cIlxuICAgICAgb25DbGljaz17KCkgPT4gc2V0VGhlbWUodGhlbWUgPT09IFwibGlnaHRcIiA/IFwiZGFya1wiIDogXCJsaWdodFwiKX1cbiAgICA+XG4gICAgICA8U3VuIGNsYXNzTmFtZT1cImgtWzEuMnJlbV0gdy1bMS4ycmVtXSByb3RhdGUtMCBzY2FsZS0xMDAgdHJhbnNpdGlvbi1hbGwgZGFyazotcm90YXRlLTkwIGRhcms6c2NhbGUtMFwiIC8+XG4gICAgICA8TW9vbiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBoLVsxLjJyZW1dIHctWzEuMnJlbV0gcm90YXRlLTkwIHNjYWxlLTAgdHJhbnNpdGlvbi1hbGwgZGFyazpyb3RhdGUtMCBkYXJrOnNjYWxlLTEwMFwiIC8+XG4gICAgICA8c3BhbiBjbGFzc05hbWU9XCJzci1vbmx5XCI+VG9nZ2xlIHRoZW1lPC9zcGFuPlxuICAgIDwvQnV0dG9uPlxuICApXG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJNb29uIiwiU3VuIiwidXNlVGhlbWUiLCJCdXR0b24iLCJUaGVtZVRvZ2dsZSIsInNldFRoZW1lIiwidGhlbWUiLCJ2YXJpYW50Iiwic2l6ZSIsIm9uQ2xpY2siLCJjbGFzc05hbWUiLCJzcGFuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-toggle.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/ui/button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/dropdown-menu.tsx":
/*!*****************************************!*\
  !*** ./components/ui/dropdown-menu.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DropdownMenu: () => (/* binding */ DropdownMenu),\n/* harmony export */   DropdownMenuCheckboxItem: () => (/* binding */ DropdownMenuCheckboxItem),\n/* harmony export */   DropdownMenuContent: () => (/* binding */ DropdownMenuContent),\n/* harmony export */   DropdownMenuGroup: () => (/* binding */ DropdownMenuGroup),\n/* harmony export */   DropdownMenuItem: () => (/* binding */ DropdownMenuItem),\n/* harmony export */   DropdownMenuLabel: () => (/* binding */ DropdownMenuLabel),\n/* harmony export */   DropdownMenuPortal: () => (/* binding */ DropdownMenuPortal),\n/* harmony export */   DropdownMenuRadioGroup: () => (/* binding */ DropdownMenuRadioGroup),\n/* harmony export */   DropdownMenuRadioItem: () => (/* binding */ DropdownMenuRadioItem),\n/* harmony export */   DropdownMenuSeparator: () => (/* binding */ DropdownMenuSeparator),\n/* harmony export */   DropdownMenuShortcut: () => (/* binding */ DropdownMenuShortcut),\n/* harmony export */   DropdownMenuSub: () => (/* binding */ DropdownMenuSub),\n/* harmony export */   DropdownMenuSubContent: () => (/* binding */ DropdownMenuSubContent),\n/* harmony export */   DropdownMenuSubTrigger: () => (/* binding */ DropdownMenuSubTrigger),\n/* harmony export */   DropdownMenuTrigger: () => (/* binding */ DropdownMenuTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dropdown-menu */ \"(ssr)/./node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst DropdownMenu = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst DropdownMenuTrigger = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst DropdownMenuGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Group;\nconst DropdownMenuPortal = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal;\nconst DropdownMenuSub = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Sub;\nconst DropdownMenuRadioGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioGroup;\nconst DropdownMenuSubTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\", inset && \"pl-8\", className),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"ml-auto h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Analytica/docs/components/ui/dropdown-menu.tsx\",\n                lineNumber: 35,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/ui/dropdown-menu.tsx\",\n        lineNumber: 25,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubTrigger.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger.displayName;\nconst DropdownMenuSubContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/ui/dropdown-menu.tsx\",\n        lineNumber: 45,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent.displayName;\nconst DropdownMenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, sideOffset = 4, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            ref: ref,\n            sideOffset: sideOffset,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Analytica/docs/components/ui/dropdown-menu.tsx\",\n            lineNumber: 62,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/ui/dropdown-menu.tsx\",\n        lineNumber: 61,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst DropdownMenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/ui/dropdown-menu.tsx\",\n        lineNumber: 81,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item.displayName;\nconst DropdownMenuCheckboxItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, checked, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        checked: checked,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Analytica/docs/components/ui/dropdown-menu.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Analytica/docs/components/ui/dropdown-menu.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Analytica/docs/components/ui/dropdown-menu.tsx\",\n                lineNumber: 106,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/ui/dropdown-menu.tsx\",\n        lineNumber: 97,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuCheckboxItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem.displayName;\nconst DropdownMenuRadioItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-2 w-2 fill-current\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Analytica/docs/components/ui/dropdown-menu.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Analytica/docs/components/ui/dropdown-menu.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Analytica/docs/components/ui/dropdown-menu.tsx\",\n                lineNumber: 129,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/ui/dropdown-menu.tsx\",\n        lineNumber: 121,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuRadioItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem.displayName;\nconst DropdownMenuLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-2 py-1.5 text-sm font-semibold\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/ui/dropdown-menu.tsx\",\n        lineNumber: 145,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuLabel.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label.displayName;\nconst DropdownMenuSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"-mx-1 my-1 h-px bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/ui/dropdown-menu.tsx\",\n        lineNumber: 161,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSeparator.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator.displayName;\nconst DropdownMenuShortcut = ({ className, ...props })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"ml-auto text-xs tracking-widest opacity-60\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/ui/dropdown-menu.tsx\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, undefined);\n};\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/dropdown-menu.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/navigation-menu.tsx":
/*!*******************************************!*\
  !*** ./components/ui/navigation-menu.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NavigationMenu: () => (/* binding */ NavigationMenu),\n/* harmony export */   NavigationMenuContent: () => (/* binding */ NavigationMenuContent),\n/* harmony export */   NavigationMenuIndicator: () => (/* binding */ NavigationMenuIndicator),\n/* harmony export */   NavigationMenuItem: () => (/* binding */ NavigationMenuItem),\n/* harmony export */   NavigationMenuLink: () => (/* binding */ NavigationMenuLink),\n/* harmony export */   NavigationMenuList: () => (/* binding */ NavigationMenuList),\n/* harmony export */   NavigationMenuTrigger: () => (/* binding */ NavigationMenuTrigger),\n/* harmony export */   NavigationMenuViewport: () => (/* binding */ NavigationMenuViewport),\n/* harmony export */   navigationMenuTriggerStyle: () => (/* binding */ navigationMenuTriggerStyle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-navigation-menu */ \"(ssr)/./node_modules/@radix-ui/react-navigation-menu/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\n\nconst NavigationMenu = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"relative z-10 flex max-w-max flex-1 items-center justify-center\", className),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavigationMenuViewport, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Analytica/docs/components/ui/navigation-menu.tsx\",\n                lineNumber: 21,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/ui/navigation-menu.tsx\",\n        lineNumber: 12,\n        columnNumber: 3\n    }, undefined));\nNavigationMenu.displayName = _radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\nconst NavigationMenuList = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.List, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"group flex flex-1 list-none items-center justify-center space-x-1\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/ui/navigation-menu.tsx\",\n        lineNumber: 30,\n        columnNumber: 3\n    }, undefined));\nNavigationMenuList.displayName = _radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.List.displayName;\nconst NavigationMenuItem = _radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.Item;\nconst navigationMenuTriggerStyle = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\");\nconst NavigationMenuTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.Trigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(navigationMenuTriggerStyle(), \"group\", className),\n        ...props,\n        children: [\n            children,\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180\",\n                \"aria-hidden\": \"true\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Analytica/docs/components/ui/navigation-menu.tsx\",\n                lineNumber: 57,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/ui/navigation-menu.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nNavigationMenuTrigger.displayName = _radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.Trigger.displayName;\nconst NavigationMenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.Content, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"left-0 top-0 w-full data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 md:absolute md:w-auto \", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/ui/navigation-menu.tsx\",\n        lineNumber: 69,\n        columnNumber: 3\n    }, undefined));\nNavigationMenuContent.displayName = _radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.Content.displayName;\nconst NavigationMenuLink = _radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.Link;\nconst NavigationMenuViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute left-0 top-full flex justify-center\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.Viewport, {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"origin-top-center relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 md:w-[var(--radix-navigation-menu-viewport-width)]\", className),\n            ref: ref,\n            ...props\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Analytica/docs/components/ui/navigation-menu.tsx\",\n            lineNumber: 87,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/ui/navigation-menu.tsx\",\n        lineNumber: 86,\n        columnNumber: 3\n    }, undefined));\nNavigationMenuViewport.displayName = _radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.Viewport.displayName;\nconst NavigationMenuIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.Indicator, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm bg-border shadow-md\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Analytica/docs/components/ui/navigation-menu.tsx\",\n            lineNumber: 112,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/ui/navigation-menu.tsx\",\n        lineNumber: 104,\n        columnNumber: 3\n    }, undefined));\nNavigationMenuIndicator.displayName = _radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.Indicator.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/navigation-menu.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/sheet.tsx":
/*!*********************************!*\
  !*** ./components/ui/sheet.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sheet: () => (/* binding */ Sheet),\n/* harmony export */   SheetClose: () => (/* binding */ SheetClose),\n/* harmony export */   SheetContent: () => (/* binding */ SheetContent),\n/* harmony export */   SheetDescription: () => (/* binding */ SheetDescription),\n/* harmony export */   SheetFooter: () => (/* binding */ SheetFooter),\n/* harmony export */   SheetHeader: () => (/* binding */ SheetHeader),\n/* harmony export */   SheetOverlay: () => (/* binding */ SheetOverlay),\n/* harmony export */   SheetPortal: () => (/* binding */ SheetPortal),\n/* harmony export */   SheetTitle: () => (/* binding */ SheetTitle),\n/* harmony export */   SheetTrigger: () => (/* binding */ SheetTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-dialog */ \"(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\n\nconst Sheet = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Root;\nconst SheetTrigger = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Trigger;\nconst SheetClose = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Close;\nconst SheetPortal = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Portal;\nconst SheetOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Overlay, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\", className),\n        ...props,\n        ref: ref\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/ui/sheet.tsx\",\n        lineNumber: 20,\n        columnNumber: 3\n    }, undefined));\nSheetOverlay.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Overlay.displayName;\nconst sheetVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500\", {\n    variants: {\n        side: {\n            top: \"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top\",\n            bottom: \"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom\",\n            left: \"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm\",\n            right: \"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm\"\n        }\n    },\n    defaultVariants: {\n        side: \"right\"\n    }\n});\nconst SheetContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ side = \"right\", className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SheetPortal, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SheetOverlay, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Analytica/docs/components/ui/sheet.tsx\",\n                lineNumber: 59,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Content, {\n                ref: ref,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(sheetVariants({\n                    side\n                }), className),\n                ...props,\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Close, {\n                        className: \"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Analytica/docs/components/ui/sheet.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"Close\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Analytica/docs/components/ui/sheet.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Analytica/docs/components/ui/sheet.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Analytica/docs/components/ui/sheet.tsx\",\n                lineNumber: 60,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/ui/sheet.tsx\",\n        lineNumber: 58,\n        columnNumber: 3\n    }, undefined));\nSheetContent.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Content.displayName;\nconst SheetHeader = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex flex-col space-y-2 text-center sm:text-left\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/ui/sheet.tsx\",\n        lineNumber: 79,\n        columnNumber: 3\n    }, undefined);\nSheetHeader.displayName = \"SheetHeader\";\nconst SheetFooter = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/ui/sheet.tsx\",\n        lineNumber: 93,\n        columnNumber: 3\n    }, undefined);\nSheetFooter.displayName = \"SheetFooter\";\nconst SheetTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-lg font-semibold text-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/ui/sheet.tsx\",\n        lineNumber: 107,\n        columnNumber: 3\n    }, undefined));\nSheetTitle.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Title.displayName;\nconst SheetDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/ui/sheet.tsx\",\n        lineNumber: 119,\n        columnNumber: 3\n    }, undefined));\nSheetDescription.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/sheet.tsx\n");

/***/ }),

/***/ "(ssr)/./i18n/routing.ts":
/*!*************************!*\
  !*** ./i18n/routing.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Link: () => (/* binding */ Link),\n/* harmony export */   redirect: () => (/* binding */ redirect),\n/* harmony export */   routing: () => (/* binding */ routing),\n/* harmony export */   usePathname: () => (/* binding */ usePathname),\n/* harmony export */   useRouter: () => (/* binding */ useRouter)\n/* harmony export */ });\n/* harmony import */ var next_intl_routing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-intl/routing */ \"(ssr)/./node_modules/next-intl/dist/development/routing.js\");\n/* harmony import */ var next_intl_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/navigation */ \"(ssr)/./node_modules/next-intl/dist/development/navigation.react-client.js\");\n\n\nconst routing = (0,next_intl_routing__WEBPACK_IMPORTED_MODULE_0__.defineRouting)({\n    // A list of all locales that are supported\n    locales: [\n        \"en\",\n        \"es\"\n    ],\n    // Used when no locale matches\n    defaultLocale: \"en\",\n    // Always use locale prefix\n    localePrefix: \"always\"\n});\n// Lightweight wrappers around Next.js' navigation APIs\n// that will consider the routing configuration\nconst { Link, redirect, usePathname, useRouter } = (0,next_intl_navigation__WEBPACK_IMPORTED_MODULE_1__.createNavigation)(routing);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9pMThuL3JvdXRpbmcudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFnRDtBQUNNO0FBRS9DLE1BQU1FLFVBQVVGLGdFQUFhQSxDQUFDO0lBQ25DLDJDQUEyQztJQUMzQ0csU0FBUztRQUFDO1FBQU07S0FBSztJQUVyQiw4QkFBOEI7SUFDOUJDLGVBQWU7SUFFZiwyQkFBMkI7SUFDM0JDLGNBQWM7QUFDaEIsR0FBRztBQUVILHVEQUF1RDtBQUN2RCwrQ0FBK0M7QUFDeEMsTUFBTSxFQUFDQyxJQUFJLEVBQUVDLFFBQVEsRUFBRUMsV0FBVyxFQUFFQyxTQUFTLEVBQUMsR0FDbkRSLHNFQUFnQkEsQ0FBQ0MsU0FBUyIsInNvdXJjZXMiOlsid2VicGFjazovL3NtZS1hbmFseXRpY2EtZG9jcy8uL2kxOG4vcm91dGluZy50cz84NzlhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7ZGVmaW5lUm91dGluZ30gZnJvbSAnbmV4dC1pbnRsL3JvdXRpbmcnO1xuaW1wb3J0IHtjcmVhdGVOYXZpZ2F0aW9ufSBmcm9tICduZXh0LWludGwvbmF2aWdhdGlvbic7XG5cbmV4cG9ydCBjb25zdCByb3V0aW5nID0gZGVmaW5lUm91dGluZyh7XG4gIC8vIEEgbGlzdCBvZiBhbGwgbG9jYWxlcyB0aGF0IGFyZSBzdXBwb3J0ZWRcbiAgbG9jYWxlczogWydlbicsICdlcyddLFxuXG4gIC8vIFVzZWQgd2hlbiBubyBsb2NhbGUgbWF0Y2hlc1xuICBkZWZhdWx0TG9jYWxlOiAnZW4nLFxuXG4gIC8vIEFsd2F5cyB1c2UgbG9jYWxlIHByZWZpeFxuICBsb2NhbGVQcmVmaXg6ICdhbHdheXMnXG59KTtcblxuLy8gTGlnaHR3ZWlnaHQgd3JhcHBlcnMgYXJvdW5kIE5leHQuanMnIG5hdmlnYXRpb24gQVBJc1xuLy8gdGhhdCB3aWxsIGNvbnNpZGVyIHRoZSByb3V0aW5nIGNvbmZpZ3VyYXRpb25cbmV4cG9ydCBjb25zdCB7TGluaywgcmVkaXJlY3QsIHVzZVBhdGhuYW1lLCB1c2VSb3V0ZXJ9ID1cbiAgY3JlYXRlTmF2aWdhdGlvbihyb3V0aW5nKTtcbiJdLCJuYW1lcyI6WyJkZWZpbmVSb3V0aW5nIiwiY3JlYXRlTmF2aWdhdGlvbiIsInJvdXRpbmciLCJsb2NhbGVzIiwiZGVmYXVsdExvY2FsZSIsImxvY2FsZVByZWZpeCIsIkxpbmsiLCJyZWRpcmVjdCIsInVzZVBhdGhuYW1lIiwidXNlUm91dGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./i18n/routing.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc21lLWFuYWx5dGljYS1kb2NzLy4vbGliL3V0aWxzLnRzP2Y3NDUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"85e96bbdf75c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zbWUtYW5hbHl0aWNhLWRvY3MvLi9hcHAvZ2xvYmFscy5jc3M/ZDAxOSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjg1ZTk2YmJkZjc1Y1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/[locale]/ai-automation/page.tsx":
/*!*********************************************!*\
  !*** ./app/[locale]/ai-automation/page.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AIAutomationPage),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _i18n_routing__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/i18n/routing */ \"(rsc)/./i18n/routing.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(rsc)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(rsc)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(rsc)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Brain_Clock_Database_Globe_Settings_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Brain,Clock,Database,Globe,Settings,Shield,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Brain_Clock_Database_Globe_Settings_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Brain,Clock,Database,Globe,Settings,Shield,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Brain_Clock_Database_Globe_Settings_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Brain,Clock,Database,Globe,Settings,Shield,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Brain_Clock_Database_Globe_Settings_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Brain,Clock,Database,Globe,Settings,Shield,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Brain_Clock_Database_Globe_Settings_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Brain,Clock,Database,Globe,Settings,Shield,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Brain_Clock_Database_Globe_Settings_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Brain,Clock,Database,Globe,Settings,Shield,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Brain_Clock_Database_Globe_Settings_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Brain,Clock,Database,Globe,Settings,Shield,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Brain_Clock_Database_Globe_Settings_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Brain,Clock,Database,Globe,Settings,Shield,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Brain_Clock_Database_Globe_Settings_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Brain,Clock,Database,Globe,Settings,Shield,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Brain_Clock_Database_Globe_Settings_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Brain,Clock,Database,Globe,Settings,Shield,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Brain_Clock_Database_Globe_Settings_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Brain,Clock,Database,Globe,Settings,Shield,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"AI & Automation\",\n    description: \"Learn about SME Analytica's AI capabilities, automation features, and the technology powering intelligent business insights.\"\n};\nfunction AIAutomationPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative overflow-hidden bg-gradient-to-br from-background via-background to-muted/20 py-20 md:py-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container relative\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto max-w-4xl text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                variant: \"outline\",\n                                className: \"mb-4\",\n                                children: \"AI & Automation\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"mb-6 text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl\",\n                                children: \"Intelligent Business Automation\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-8 text-xl text-muted-foreground sm:text-2xl\",\n                                children: \"Discover how SME Analytica leverages cutting-edge AI models and automation to deliver actionable business insights and streamline operations.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4 sm:flex-row sm:justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"lg\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                            href: \"/modules\",\n                                            children: [\n                                                \"Explore AI Features\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Brain_Clock_Database_Globe_Settings_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"ml-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                    lineNumber: 46,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"lg\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                            href: \"/integrations\",\n                                            children: \"API Documentation\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto max-w-4xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"mb-8 text-3xl font-bold tracking-tight sm:text-4xl\",\n                                children: \"AI Models & Capabilities\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8 space-y-4 text-lg text-muted-foreground\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"SME Analytica integrates with multiple state-of-the-art AI models through OpenRouter, providing businesses with access to the most advanced language models and AI capabilities available today.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-6 md:grid-cols-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Brain_Clock_Database_Globe_Settings_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-8 w-8 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                        lineNumber: 76,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        children: \"Language Models\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                        lineNumber: 77,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold\",\n                                                                children: \"GPT-4 & GPT-3.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 81,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Advanced reasoning and analysis capabilities\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 82,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                        lineNumber: 80,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold\",\n                                                                children: \"Claude (Anthropic)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 85,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Constitutional AI for safe and helpful responses\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 86,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                        lineNumber: 84,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold\",\n                                                                children: \"DeepSeek\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 89,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Specialized models for technical analysis\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 90,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                        lineNumber: 88,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold\",\n                                                                children: \"Perplexity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 93,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Real-time web search and information synthesis\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 94,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                        lineNumber: 92,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Brain_Clock_Database_Globe_Settings_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-8 w-8 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                        lineNumber: 101,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        children: \"Analysis Capabilities\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold\",\n                                                                children: \"Sentiment Analysis\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 106,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Customer feedback and review sentiment tracking\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 107,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold\",\n                                                                children: \"Market Intelligence\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 110,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Competitive landscape and market trend analysis\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 111,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                        lineNumber: 109,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold\",\n                                                                children: \"Dynamic Pricing\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 114,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Real-time price optimization based on demand\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 115,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold\",\n                                                                children: \"Sales Forecasting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 118,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Predictive analytics for revenue planning\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 119,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                        lineNumber: 117,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-muted/30 py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-3xl text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"mb-4 text-3xl font-bold tracking-tight sm:text-4xl\",\n                                    children: \"Automation Features\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mb-12 text-lg text-muted-foreground\",\n                                    children: \"Streamline your business operations with intelligent automation\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-6 md:grid-cols-3\",\n                            children: [\n                                {\n                                    icon: _barrel_optimize_names_ArrowRight_BarChart3_Brain_Clock_Database_Globe_Settings_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                                    title: \"Real-time Processing\",\n                                    description: \"Instant analysis and insights as data becomes available\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_ArrowRight_BarChart3_Brain_Clock_Database_Globe_Settings_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                    title: \"Dynamic Pricing\",\n                                    description: \"Automatic price adjustments based on market conditions\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_ArrowRight_BarChart3_Brain_Clock_Database_Globe_Settings_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                                    title: \"Multi-source Data\",\n                                    description: \"Automated data collection from Google Places, TripAdvisor, Yelp\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_ArrowRight_BarChart3_Brain_Clock_Database_Globe_Settings_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                    title: \"Customer Insights\",\n                                    description: \"Automated sentiment tracking and customer behavior analysis\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_ArrowRight_BarChart3_Brain_Clock_Database_Globe_Settings_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                    title: \"Security & Privacy\",\n                                    description: \"Automated data protection and compliance monitoring\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_ArrowRight_BarChart3_Brain_Clock_Database_Globe_Settings_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                    title: \"Scheduled Reports\",\n                                    description: \"Automated generation and delivery of business reports\"\n                                }\n                            ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                    className: \"mx-auto h-12 w-12 text-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-lg\",\n                                                    children: feature.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                className: \"text-base\",\n                                                children: feature.description\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto max-w-4xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"mb-8 text-3xl font-bold tracking-tight sm:text-4xl text-center\",\n                                children: \"Data Sources & Integration\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-8 md:grid-cols-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Brain_Clock_Database_Globe_Settings_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-8 w-8 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        children: \"External Data Sources\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold\",\n                                                                children: \"Google Places API\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Business information, reviews, and location data\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 202,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold\",\n                                                                children: \"TripAdvisor API\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 205,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Travel and hospitality industry insights\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 206,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold\",\n                                                                children: \"Yelp API\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 209,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Local business reviews and ratings\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold\",\n                                                                children: \"Real-time Market Data\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 213,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Live market conditions and competitor pricing\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 214,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Brain_Clock_Database_Globe_Settings_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-8 w-8 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        children: \"Processing Pipeline\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold\",\n                                                                children: \"Data Ingestion\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Automated collection and normalization\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 227,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold\",\n                                                                children: \"AI Analysis\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Multi-model processing for comprehensive insights\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 231,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold\",\n                                                                children: \"Quality Assurance\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Automated validation and confidence scoring\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 235,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold\",\n                                                                children: \"Delivery\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 238,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Real-time updates via API and mobile app\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                                lineNumber: 239,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-muted/30 py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto max-w-3xl text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"mb-4 text-3xl font-bold tracking-tight sm:text-4xl\",\n                                children: \"Ready to Harness AI for Your Business?\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-8 text-lg text-muted-foreground\",\n                                children: \"Discover how SME Analytica's AI and automation can transform your business operations\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4 sm:flex-row sm:justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"lg\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                            href: \"/modules\",\n                                            children: [\n                                                \"Explore AI Features\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Brain_Clock_Database_Globe_Settings_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"ml-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"lg\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                            href: \"/for-businesses\",\n                                            children: \"For Businesses\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/ai-automation/page.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/[locale]/ai-automation/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/[locale]/layout.tsx":
/*!*********************************!*\
  !*** ./app/[locale]/layout.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LocaleLayout),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata),\n/* harmony export */   generateStaticParams: () => (/* binding */ generateStaticParams)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/[locale]/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/[locale]/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./components/theme-provider.tsx\");\n/* harmony import */ var _components_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/navigation */ \"(rsc)/./components/navigation.tsx\");\n/* harmony import */ var _components_footer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/footer */ \"(rsc)/./components/footer.tsx\");\n\n\n\n\n\n\n\n\nasync function getMessages(locale) {\n    try {\n        return (await __webpack_require__(\"(rsc)/./messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${locale}.json`)).default;\n    } catch (error) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.notFound)();\n    }\n}\nfunction generateStaticParams() {\n    return [\n        {\n            locale: \"en\"\n        },\n        {\n            locale: \"es\"\n        }\n    ];\n}\nasync function generateMetadata({ params: { locale } }) {\n    const messages = await getMessages(locale);\n    return {\n        title: {\n            template: \"%s | SME Analytica Docs\",\n            default: \"SME Analytica - AI Business Intelligence Platform Documentation\"\n        },\n        description: messages.meta?.description || \"Comprehensive documentation for SME Analytica - AI-powered business intelligence platform for small and medium enterprises.\",\n        keywords: [\n            \"SME Analytica\",\n            \"Business Intelligence\",\n            \"AI Analytics\",\n            \"Documentation\",\n            \"API\"\n        ],\n        authors: [\n            {\n                name: \"SME Analytica Team\"\n            }\n        ],\n        creator: \"SME Analytica\",\n        publisher: \"SME Analytica\",\n        formatDetection: {\n            email: false,\n            address: false,\n            telephone: false\n        },\n        metadataBase: new URL(\"https://docs.smeanalytica.dev\"),\n        alternates: {\n            canonical: \"/\",\n            languages: {\n                \"en-US\": \"/en\",\n                \"es-ES\": \"/es\"\n            }\n        },\n        openGraph: {\n            title: \"SME Analytica Documentation\",\n            description: \"AI-powered business intelligence platform for SMEs\",\n            url: \"https://docs.smeanalytica.dev\",\n            siteName: \"SME Analytica Docs\",\n            images: [\n                {\n                    url: \"/og-image.png\",\n                    width: 1200,\n                    height: 630,\n                    alt: \"SME Analytica Documentation\"\n                }\n            ],\n            locale: locale,\n            type: \"website\"\n        },\n        twitter: {\n            card: \"summary_large_image\",\n            title: \"SME Analytica Documentation\",\n            description: \"AI-powered business intelligence platform for SMEs\",\n            images: [\n                \"/og-image.png\"\n            ]\n        },\n        robots: {\n            index: true,\n            follow: true,\n            googleBot: {\n                index: true,\n                follow: true,\n                \"max-video-preview\": -1,\n                \"max-image-preview\": \"large\",\n                \"max-snippet\": -1\n            }\n        }\n    };\n}\nasync function LocaleLayout({ children, params: { locale } }) {\n    const messages = await getMessages(locale);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: locale,\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_intl__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                locale: locale,\n                messages: messages,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_3__.ThemeProvider, {\n                    attribute: \"class\",\n                    defaultTheme: \"system\",\n                    enableSystem: true,\n                    disableTransitionOnChange: true,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex min-h-screen flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation__WEBPACK_IMPORTED_MODULE_4__.Navigation, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/layout.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                className: \"flex-1\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/layout.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_footer__WEBPACK_IMPORTED_MODULE_5__.Footer, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/layout.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/layout.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/layout.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/layout.tsx\",\n                lineNumber: 97,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/layout.tsx\",\n            lineNumber: 96,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Analytica/docs/app/[locale]/layout.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/[locale]/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/footer.tsx":
/*!*******************************!*\
  !*** ./components/footer.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/useTranslations.js\");\n/* harmony import */ var _i18n_routing__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/i18n/routing */ \"(rsc)/./i18n/routing.ts\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Mail,Twitter!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Mail,Twitter!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Mail,Twitter!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Mail,Twitter!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n\n\n\n\nfunction Footer() {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(\"footer\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"border-t bg-background\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container py-8 md:py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 gap-8 md:grid-cols-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-6 w-6 rounded bg-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                            lineNumber: 15,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-bold\",\n                                            children: \"SME Analytica\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                            lineNumber: 16,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                    lineNumber: 14,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"AI-powered business intelligence platform for small and medium enterprises.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                            href: \"https://github.com/sme-analytica\",\n                                            className: \"text-muted-foreground hover:text-foreground\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                                    lineNumber: 26,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"GitHub\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                                    lineNumber: 27,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                            lineNumber: 22,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                            href: \"https://twitter.com/smeanalytica\",\n                                            className: \"text-muted-foreground hover:text-foreground\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                                    lineNumber: 33,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"Twitter\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                                    lineNumber: 34,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                            lineNumber: 29,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                            href: \"https://linkedin.com/company/sme-analytica\",\n                                            className: \"text-muted-foreground hover:text-foreground\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                                    lineNumber: 40,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"LinkedIn\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                                    lineNumber: 41,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                            href: \"mailto:<EMAIL>\",\n                                            className: \"text-muted-foreground hover:text-foreground\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                                    lineNumber: 47,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"Email\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                                    lineNumber: 48,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-semibold\",\n                                    children: \"Platform\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                                href: \"/platform\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"Overview\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                                href: \"/platform#features\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"Features\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                                href: \"/platform#tech-stack\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"Tech Stack\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                                href: \"/ai-automation\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"AI & Automation\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-semibold\",\n                                    children: \"Modules\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                                href: \"/modules/menuflow\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"MenuFlow\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                                href: \"/modules/sme-app\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"SME App\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                                href: \"/modules/connecto\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"Connecto\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                                href: \"/modules/ticketing\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"Event Ticketing\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-semibold\",\n                                    children: \"Resources\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                                href: \"/integrations\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"API Documentation\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                                href: \"/for-businesses\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"For Businesses\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                                href: \"/about\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"About Us\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                                href: \"https://api.smeanalytica.dev/docs\",\n                                                target: \"_blank\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"Live API Docs\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                    lineNumber: 11,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 border-t pt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center justify-between space-y-4 md:flex-row md:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"\\xa9 2024 SME Analytica. All rights reserved.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-6 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                        href: \"/privacy\",\n                                        className: \"text-muted-foreground hover:text-foreground\",\n                                        children: \"Privacy Policy\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                        href: \"/terms\",\n                                        className: \"text-muted-foreground hover:text-foreground\",\n                                        children: \"Terms of Service\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_routing__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                        href: \"/cookies\",\n                                        className: \"text-muted-foreground hover:text-foreground\",\n                                        children: \"Cookie Policy\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/footer.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/footer.tsx\n");

/***/ }),

/***/ "(rsc)/./components/navigation.tsx":
/*!***********************************!*\
  !*** ./components/navigation.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Navigation: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Analytica/docs/components/navigation.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Analytica/docs/components/navigation.tsx#Navigation`);


/***/ }),

/***/ "(rsc)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Analytica/docs/components/theme-provider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Analytica/docs/components/theme-provider.tsx#ThemeProvider`);


/***/ }),

/***/ "(rsc)/./components/ui/badge.tsx":
/*!*********************************!*\
  !*** ./components/ui/badge.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(rsc)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/ui/badge.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/ui/badge.tsx\n");

/***/ }),

/***/ "(rsc)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(rsc)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(rsc)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/ui/button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(rsc)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/ui/card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/ui/card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/ui/card.tsx\",\n        lineNumber: 32,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/ui/card.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/ui/card.tsx\",\n        lineNumber: 59,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Analytica/docs/components/ui/card.tsx\",\n        lineNumber: 67,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(rsc)/./i18n/request.ts":
/*!*************************!*\
  !*** ./i18n/request.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js\");\n/* harmony import */ var _routing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./routing */ \"(rsc)/./i18n/routing.ts\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_intl_server__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async ({ requestLocale })=>{\n    // This typically corresponds to the `[locale]` segment\n    let locale = await requestLocale;\n    // Ensure that the incoming locale is valid\n    if (!locale || !_routing__WEBPACK_IMPORTED_MODULE_0__.routing.locales.includes(locale)) {\n        locale = _routing__WEBPACK_IMPORTED_MODULE_0__.routing.defaultLocale;\n    }\n    return {\n        locale,\n        messages: (await __webpack_require__(\"(rsc)/./messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${locale}.json`)).default\n    };\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9pMThuL3JlcXVlc3QudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQ2tEO0FBQ2hCO0FBRWxDLGlFQUFlQSw0REFBZ0JBLENBQUMsT0FBTyxFQUFDRSxhQUFhLEVBQUM7SUFDcEQsdURBQXVEO0lBQ3ZELElBQUlDLFNBQVMsTUFBTUQ7SUFFbkIsMkNBQTJDO0lBQzNDLElBQUksQ0FBQ0MsVUFBVSxDQUFDRiw2Q0FBT0EsQ0FBQ0csT0FBTyxDQUFDQyxRQUFRLENBQUNGLFNBQWdCO1FBQ3ZEQSxTQUFTRiw2Q0FBT0EsQ0FBQ0ssYUFBYTtJQUNoQztJQUVBLE9BQU87UUFDTEg7UUFDQUksVUFBVSxDQUFDLE1BQU0seUVBQU8sR0FBYSxFQUFFSixPQUFPLE1BQU0sR0FBR0ssT0FBTztJQUNoRTtBQUNGLEVBQUUsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3NtZS1hbmFseXRpY2EtZG9jcy8uL2kxOG4vcmVxdWVzdC50cz81Mzk2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7bm90Rm91bmR9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5pbXBvcnQge2dldFJlcXVlc3RDb25maWd9IGZyb20gJ25leHQtaW50bC9zZXJ2ZXInO1xuaW1wb3J0IHtyb3V0aW5nfSBmcm9tICcuL3JvdXRpbmcnO1xuXG5leHBvcnQgZGVmYXVsdCBnZXRSZXF1ZXN0Q29uZmlnKGFzeW5jICh7cmVxdWVzdExvY2FsZX0pID0+IHtcbiAgLy8gVGhpcyB0eXBpY2FsbHkgY29ycmVzcG9uZHMgdG8gdGhlIGBbbG9jYWxlXWAgc2VnbWVudFxuICBsZXQgbG9jYWxlID0gYXdhaXQgcmVxdWVzdExvY2FsZTtcblxuICAvLyBFbnN1cmUgdGhhdCB0aGUgaW5jb21pbmcgbG9jYWxlIGlzIHZhbGlkXG4gIGlmICghbG9jYWxlIHx8ICFyb3V0aW5nLmxvY2FsZXMuaW5jbHVkZXMobG9jYWxlIGFzIGFueSkpIHtcbiAgICBsb2NhbGUgPSByb3V0aW5nLmRlZmF1bHRMb2NhbGU7XG4gIH1cblxuICByZXR1cm4ge1xuICAgIGxvY2FsZSxcbiAgICBtZXNzYWdlczogKGF3YWl0IGltcG9ydChgLi4vbWVzc2FnZXMvJHtsb2NhbGV9Lmpzb25gKSkuZGVmYXVsdFxuICB9O1xufSk7XG4iXSwibmFtZXMiOlsiZ2V0UmVxdWVzdENvbmZpZyIsInJvdXRpbmciLCJyZXF1ZXN0TG9jYWxlIiwibG9jYWxlIiwibG9jYWxlcyIsImluY2x1ZGVzIiwiZGVmYXVsdExvY2FsZSIsIm1lc3NhZ2VzIiwiZGVmYXVsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./i18n/request.ts\n");

/***/ }),

/***/ "(rsc)/./i18n/routing.ts":
/*!*************************!*\
  !*** ./i18n/routing.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Link: () => (/* binding */ Link),\n/* harmony export */   redirect: () => (/* binding */ redirect),\n/* harmony export */   routing: () => (/* binding */ routing),\n/* harmony export */   usePathname: () => (/* binding */ usePathname),\n/* harmony export */   useRouter: () => (/* binding */ useRouter)\n/* harmony export */ });\n/* harmony import */ var next_intl_routing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-intl/routing */ \"(rsc)/./node_modules/next-intl/dist/development/routing.js\");\n/* harmony import */ var next_intl_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/navigation */ \"(rsc)/./node_modules/next-intl/dist/esm/navigation/react-server/createNavigation.js\");\n\n\nconst routing = (0,next_intl_routing__WEBPACK_IMPORTED_MODULE_0__.defineRouting)({\n    // A list of all locales that are supported\n    locales: [\n        \"en\",\n        \"es\"\n    ],\n    // Used when no locale matches\n    defaultLocale: \"en\",\n    // Always use locale prefix\n    localePrefix: \"always\"\n});\n// Lightweight wrappers around Next.js' navigation APIs\n// that will consider the routing configuration\nconst { Link, redirect, usePathname, useRouter } = (0,next_intl_navigation__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(routing);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9pMThuL3JvdXRpbmcudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFnRDtBQUNNO0FBRS9DLE1BQU1FLFVBQVVGLGdFQUFhQSxDQUFDO0lBQ25DLDJDQUEyQztJQUMzQ0csU0FBUztRQUFDO1FBQU07S0FBSztJQUVyQiw4QkFBOEI7SUFDOUJDLGVBQWU7SUFFZiwyQkFBMkI7SUFDM0JDLGNBQWM7QUFDaEIsR0FBRztBQUVILHVEQUF1RDtBQUN2RCwrQ0FBK0M7QUFDeEMsTUFBTSxFQUFDQyxJQUFJLEVBQUVDLFFBQVEsRUFBRUMsV0FBVyxFQUFFQyxTQUFTLEVBQUMsR0FDbkRSLGdFQUFnQkEsQ0FBQ0MsU0FBUyIsInNvdXJjZXMiOlsid2VicGFjazovL3NtZS1hbmFseXRpY2EtZG9jcy8uL2kxOG4vcm91dGluZy50cz84NzlhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7ZGVmaW5lUm91dGluZ30gZnJvbSAnbmV4dC1pbnRsL3JvdXRpbmcnO1xuaW1wb3J0IHtjcmVhdGVOYXZpZ2F0aW9ufSBmcm9tICduZXh0LWludGwvbmF2aWdhdGlvbic7XG5cbmV4cG9ydCBjb25zdCByb3V0aW5nID0gZGVmaW5lUm91dGluZyh7XG4gIC8vIEEgbGlzdCBvZiBhbGwgbG9jYWxlcyB0aGF0IGFyZSBzdXBwb3J0ZWRcbiAgbG9jYWxlczogWydlbicsICdlcyddLFxuXG4gIC8vIFVzZWQgd2hlbiBubyBsb2NhbGUgbWF0Y2hlc1xuICBkZWZhdWx0TG9jYWxlOiAnZW4nLFxuXG4gIC8vIEFsd2F5cyB1c2UgbG9jYWxlIHByZWZpeFxuICBsb2NhbGVQcmVmaXg6ICdhbHdheXMnXG59KTtcblxuLy8gTGlnaHR3ZWlnaHQgd3JhcHBlcnMgYXJvdW5kIE5leHQuanMnIG5hdmlnYXRpb24gQVBJc1xuLy8gdGhhdCB3aWxsIGNvbnNpZGVyIHRoZSByb3V0aW5nIGNvbmZpZ3VyYXRpb25cbmV4cG9ydCBjb25zdCB7TGluaywgcmVkaXJlY3QsIHVzZVBhdGhuYW1lLCB1c2VSb3V0ZXJ9ID1cbiAgY3JlYXRlTmF2aWdhdGlvbihyb3V0aW5nKTtcbiJdLCJuYW1lcyI6WyJkZWZpbmVSb3V0aW5nIiwiY3JlYXRlTmF2aWdhdGlvbiIsInJvdXRpbmciLCJsb2NhbGVzIiwiZGVmYXVsdExvY2FsZSIsImxvY2FsZVByZWZpeCIsIkxpbmsiLCJyZWRpcmVjdCIsInVzZVBhdGhuYW1lIiwidXNlUm91dGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./i18n/routing.ts\n");

/***/ }),

/***/ "(rsc)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc21lLWFuYWx5dGljYS1kb2NzLy4vbGliL3V0aWxzLnRzP2Y3NDUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/utils.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/tailwind-merge","vendor-chunks/@radix-ui","vendor-chunks/@formatjs","vendor-chunks/@floating-ui","vendor-chunks/next-intl","vendor-chunks/use-intl","vendor-chunks/tslib","vendor-chunks/intl-messageformat","vendor-chunks/lucide-react","vendor-chunks/react-remove-scroll","vendor-chunks/aria-hidden","vendor-chunks/class-variance-authority","vendor-chunks/react-remove-scroll-bar","vendor-chunks/next-themes","vendor-chunks/use-callback-ref","vendor-chunks/use-sidecar","vendor-chunks/react-style-singleton","vendor-chunks/clsx","vendor-chunks/get-nonce"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fai-automation%2Fpage&page=%2F%5Blocale%5D%2Fai-automation%2Fpage&appPaths=%2F%5Blocale%5D%2Fai-automation%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fai-automation%2Fpage.tsx&appDir=%2FUsers%2Folayinka%2FAnalytica%2Fdocs%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Folayinka%2FAnalytica%2Fdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();