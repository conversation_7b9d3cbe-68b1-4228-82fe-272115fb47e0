"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@floating-ui";
exports.ids = ["vendor-chunks/@floating-ui"];
exports.modules = {

/***/ "(ssr)/./node_modules/@floating-ui/core/dist/floating-ui.core.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@floating-ui/core/dist/floating-ui.core.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   arrow: () => (/* binding */ arrow),\n/* harmony export */   autoPlacement: () => (/* binding */ autoPlacement),\n/* harmony export */   computePosition: () => (/* binding */ computePosition),\n/* harmony export */   detectOverflow: () => (/* binding */ detectOverflow),\n/* harmony export */   flip: () => (/* binding */ flip),\n/* harmony export */   hide: () => (/* binding */ hide),\n/* harmony export */   inline: () => (/* binding */ inline),\n/* harmony export */   limitShift: () => (/* binding */ limitShift),\n/* harmony export */   offset: () => (/* binding */ offset),\n/* harmony export */   rectToClientRect: () => (/* reexport safe */ _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect),\n/* harmony export */   shift: () => (/* binding */ shift),\n/* harmony export */   size: () => (/* binding */ size)\n/* harmony export */ });\n/* harmony import */ var _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @floating-ui/utils */ \"(ssr)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs\");\n\n\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n    let { reference, floating } = _ref;\n    const sideAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement);\n    const alignmentAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignmentAxis)(placement);\n    const alignLength = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAxisLength)(alignmentAxis);\n    const side = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement);\n    const isVertical = sideAxis === \"y\";\n    const commonX = reference.x + reference.width / 2 - floating.width / 2;\n    const commonY = reference.y + reference.height / 2 - floating.height / 2;\n    const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n    let coords;\n    switch(side){\n        case \"top\":\n            coords = {\n                x: commonX,\n                y: reference.y - floating.height\n            };\n            break;\n        case \"bottom\":\n            coords = {\n                x: commonX,\n                y: reference.y + reference.height\n            };\n            break;\n        case \"right\":\n            coords = {\n                x: reference.x + reference.width,\n                y: commonY\n            };\n            break;\n        case \"left\":\n            coords = {\n                x: reference.x - floating.width,\n                y: commonY\n            };\n            break;\n        default:\n            coords = {\n                x: reference.x,\n                y: reference.y\n            };\n    }\n    switch((0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement)){\n        case \"start\":\n            coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n            break;\n        case \"end\":\n            coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n            break;\n    }\n    return coords;\n}\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */ const computePosition = async (reference, floating, config)=>{\n    const { placement = \"bottom\", strategy = \"absolute\", middleware = [], platform } = config;\n    const validMiddleware = middleware.filter(Boolean);\n    const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n    let rects = await platform.getElementRects({\n        reference,\n        floating,\n        strategy\n    });\n    let { x, y } = computeCoordsFromPlacement(rects, placement, rtl);\n    let statefulPlacement = placement;\n    let middlewareData = {};\n    let resetCount = 0;\n    for(let i = 0; i < validMiddleware.length; i++){\n        const { name, fn } = validMiddleware[i];\n        const { x: nextX, y: nextY, data, reset } = await fn({\n            x,\n            y,\n            initialPlacement: placement,\n            placement: statefulPlacement,\n            strategy,\n            middlewareData,\n            rects,\n            platform,\n            elements: {\n                reference,\n                floating\n            }\n        });\n        x = nextX != null ? nextX : x;\n        y = nextY != null ? nextY : y;\n        middlewareData = {\n            ...middlewareData,\n            [name]: {\n                ...middlewareData[name],\n                ...data\n            }\n        };\n        if (reset && resetCount <= 50) {\n            resetCount++;\n            if (typeof reset === \"object\") {\n                if (reset.placement) {\n                    statefulPlacement = reset.placement;\n                }\n                if (reset.rects) {\n                    rects = reset.rects === true ? await platform.getElementRects({\n                        reference,\n                        floating,\n                        strategy\n                    }) : reset.rects;\n                }\n                ({ x, y } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n            }\n            i = -1;\n        }\n    }\n    return {\n        x,\n        y,\n        placement: statefulPlacement,\n        strategy,\n        middlewareData\n    };\n};\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */ async function detectOverflow(state, options) {\n    var _await$platform$isEle;\n    if (options === void 0) {\n        options = {};\n    }\n    const { x, y, platform, rects, elements, strategy } = state;\n    const { boundary = \"clippingAncestors\", rootBoundary = \"viewport\", elementContext = \"floating\", altBoundary = false, padding = 0 } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n    const paddingObject = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getPaddingObject)(padding);\n    const altContext = elementContext === \"floating\" ? \"reference\" : \"floating\";\n    const element = elements[altBoundary ? altContext : elementContext];\n    const clippingClientRect = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect)(await platform.getClippingRect({\n        element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating)),\n        boundary,\n        rootBoundary,\n        strategy\n    }));\n    const rect = elementContext === \"floating\" ? {\n        x,\n        y,\n        width: rects.floating.width,\n        height: rects.floating.height\n    } : rects.reference;\n    const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n    const offsetScale = await (platform.isElement == null ? void 0 : platform.isElement(offsetParent)) ? await (platform.getScale == null ? void 0 : platform.getScale(offsetParent)) || {\n        x: 1,\n        y: 1\n    } : {\n        x: 1,\n        y: 1\n    };\n    const elementClientRect = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect)(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n        elements,\n        rect,\n        offsetParent,\n        strategy\n    }) : rect);\n    return {\n        top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n        bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n        left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n        right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n    };\n}\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */ const arrow = (options)=>({\n        name: \"arrow\",\n        options,\n        async fn (state) {\n            const { x, y, placement, rects, platform, elements, middlewareData } = state;\n            // Since `element` is required, we don't Partial<> the type.\n            const { element, padding = 0 } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state) || {};\n            if (element == null) {\n                return {};\n            }\n            const paddingObject = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getPaddingObject)(padding);\n            const coords = {\n                x,\n                y\n            };\n            const axis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignmentAxis)(placement);\n            const length = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAxisLength)(axis);\n            const arrowDimensions = await platform.getDimensions(element);\n            const isYAxis = axis === \"y\";\n            const minProp = isYAxis ? \"top\" : \"left\";\n            const maxProp = isYAxis ? \"bottom\" : \"right\";\n            const clientProp = isYAxis ? \"clientHeight\" : \"clientWidth\";\n            const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n            const startDiff = coords[axis] - rects.reference[axis];\n            const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n            let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n            // DOM platform can return `window` as the `offsetParent`.\n            if (!clientSize || !await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent))) {\n                clientSize = elements.floating[clientProp] || rects.floating[length];\n            }\n            const centerToReference = endDiff / 2 - startDiff / 2;\n            // If the padding is large enough that it causes the arrow to no longer be\n            // centered, modify the padding so that it is centered.\n            const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n            const minPadding = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(paddingObject[minProp], largestPossiblePadding);\n            const maxPadding = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(paddingObject[maxProp], largestPossiblePadding);\n            // Make sure the arrow doesn't overflow the floating element if the center\n            // point is outside the floating element's bounds.\n            const min$1 = minPadding;\n            const max = clientSize - arrowDimensions[length] - maxPadding;\n            const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n            const offset = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.clamp)(min$1, center, max);\n            // If the reference is small enough that the arrow's padding causes it to\n            // to point to nothing for an aligned placement, adjust the offset of the\n            // floating element itself. To ensure `shift()` continues to take action,\n            // a single reset is performed when this is true.\n            const shouldAddOffset = !middlewareData.arrow && (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement) != null && center !== offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n            const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\n            return {\n                [axis]: coords[axis] + alignmentOffset,\n                data: {\n                    [axis]: offset,\n                    centerOffset: center - offset - alignmentOffset,\n                    ...shouldAddOffset && {\n                        alignmentOffset\n                    }\n                },\n                reset: shouldAddOffset\n            };\n        }\n    });\nfunction getPlacementList(alignment, autoAlignment, allowedPlacements) {\n    const allowedPlacementsSortedByAlignment = alignment ? [\n        ...allowedPlacements.filter((placement)=>(0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement) === alignment),\n        ...allowedPlacements.filter((placement)=>(0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement) !== alignment)\n    ] : allowedPlacements.filter((placement)=>(0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement) === placement);\n    return allowedPlacementsSortedByAlignment.filter((placement)=>{\n        if (alignment) {\n            return (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement) === alignment || (autoAlignment ? (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositeAlignmentPlacement)(placement) !== placement : false);\n        }\n        return true;\n    });\n}\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */ const autoPlacement = function(options) {\n    if (options === void 0) {\n        options = {};\n    }\n    return {\n        name: \"autoPlacement\",\n        options,\n        async fn (state) {\n            var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;\n            const { rects, middlewareData, placement, platform, elements } = state;\n            const { crossAxis = false, alignment, allowedPlacements = _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.placements, autoAlignment = true, ...detectOverflowOptions } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n            const placements$1 = alignment !== undefined || allowedPlacements === _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;\n            const overflow = await detectOverflow(state, detectOverflowOptions);\n            const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;\n            const currentPlacement = placements$1[currentIndex];\n            if (currentPlacement == null) {\n                return {};\n            }\n            const alignmentSides = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignmentSides)(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));\n            // Make `computeCoords` start from the right place.\n            if (placement !== currentPlacement) {\n                return {\n                    reset: {\n                        placement: placements$1[0]\n                    }\n                };\n            }\n            const currentOverflows = [\n                overflow[(0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(currentPlacement)],\n                overflow[alignmentSides[0]],\n                overflow[alignmentSides[1]]\n            ];\n            const allOverflows = [\n                ...((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || [],\n                {\n                    placement: currentPlacement,\n                    overflows: currentOverflows\n                }\n            ];\n            const nextPlacement = placements$1[currentIndex + 1];\n            // There are more placements to check.\n            if (nextPlacement) {\n                return {\n                    data: {\n                        index: currentIndex + 1,\n                        overflows: allOverflows\n                    },\n                    reset: {\n                        placement: nextPlacement\n                    }\n                };\n            }\n            const placementsSortedByMostSpace = allOverflows.map((d)=>{\n                const alignment = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(d.placement);\n                return [\n                    d.placement,\n                    alignment && crossAxis ? // Check along the mainAxis and main crossAxis side.\n                    d.overflows.slice(0, 2).reduce((acc, v)=>acc + v, 0) : // Check only the mainAxis.\n                    d.overflows[0],\n                    d.overflows\n                ];\n            }).sort((a, b)=>a[1] - b[1]);\n            const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter((d)=>d[2].slice(0, // Aligned placements should not check their opposite crossAxis\n                // side.\n                (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(d[0]) ? 2 : 3).every((v)=>v <= 0));\n            const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];\n            if (resetPlacement !== placement) {\n                return {\n                    data: {\n                        index: currentIndex + 1,\n                        overflows: allOverflows\n                    },\n                    reset: {\n                        placement: resetPlacement\n                    }\n                };\n            }\n            return {};\n        }\n    };\n};\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */ const flip = function(options) {\n    if (options === void 0) {\n        options = {};\n    }\n    return {\n        name: \"flip\",\n        options,\n        async fn (state) {\n            var _middlewareData$arrow, _middlewareData$flip;\n            const { placement, middlewareData, rects, initialPlacement, platform, elements } = state;\n            const { mainAxis: checkMainAxis = true, crossAxis: checkCrossAxis = true, fallbackPlacements: specifiedFallbackPlacements, fallbackStrategy = \"bestFit\", fallbackAxisSideDirection = \"none\", flipAlignment = true, ...detectOverflowOptions } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n            // If a reset by the arrow was caused due to an alignment offset being\n            // added, we should skip any logic now since `flip()` has already done its\n            // work.\n            // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\n            if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n                return {};\n            }\n            const side = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement);\n            const initialSideAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(initialPlacement);\n            const isBasePlacement = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(initialPlacement) === initialPlacement;\n            const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n            const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [\n                (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositePlacement)(initialPlacement)\n            ] : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getExpandedPlacements)(initialPlacement));\n            const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== \"none\";\n            if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {\n                fallbackPlacements.push(...(0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositeAxisPlacements)(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n            }\n            const placements = [\n                initialPlacement,\n                ...fallbackPlacements\n            ];\n            const overflow = await detectOverflow(state, detectOverflowOptions);\n            const overflows = [];\n            let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n            if (checkMainAxis) {\n                overflows.push(overflow[side]);\n            }\n            if (checkCrossAxis) {\n                const sides = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignmentSides)(placement, rects, rtl);\n                overflows.push(overflow[sides[0]], overflow[sides[1]]);\n            }\n            overflowsData = [\n                ...overflowsData,\n                {\n                    placement,\n                    overflows\n                }\n            ];\n            // One or more sides is overflowing.\n            if (!overflows.every((side)=>side <= 0)) {\n                var _middlewareData$flip2, _overflowsData$filter;\n                const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n                const nextPlacement = placements[nextIndex];\n                if (nextPlacement) {\n                    var _overflowsData$;\n                    const ignoreCrossAxisOverflow = checkCrossAxis === \"alignment\" ? initialSideAxis !== (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(nextPlacement) : false;\n                    const hasInitialMainAxisOverflow = ((_overflowsData$ = overflowsData[0]) == null ? void 0 : _overflowsData$.overflows[0]) > 0;\n                    if (!ignoreCrossAxisOverflow || hasInitialMainAxisOverflow) {\n                        // Try next placement and re-run the lifecycle.\n                        return {\n                            data: {\n                                index: nextIndex,\n                                overflows: overflowsData\n                            },\n                            reset: {\n                                placement: nextPlacement\n                            }\n                        };\n                    }\n                }\n                // First, find the candidates that fit on the mainAxis side of overflow,\n                // then find the placement that fits the best on the main crossAxis side.\n                let resetPlacement = (_overflowsData$filter = overflowsData.filter((d)=>d.overflows[0] <= 0).sort((a, b)=>a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n                // Otherwise fallback.\n                if (!resetPlacement) {\n                    switch(fallbackStrategy){\n                        case \"bestFit\":\n                            {\n                                var _overflowsData$filter2;\n                                const placement = (_overflowsData$filter2 = overflowsData.filter((d)=>{\n                                    if (hasFallbackAxisSideDirection) {\n                                        const currentSideAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(d.placement);\n                                        return currentSideAxis === initialSideAxis || // Create a bias to the `y` side axis due to horizontal\n                                        // reading directions favoring greater width.\n                                        currentSideAxis === \"y\";\n                                    }\n                                    return true;\n                                }).map((d)=>[\n                                        d.placement,\n                                        d.overflows.filter((overflow)=>overflow > 0).reduce((acc, overflow)=>acc + overflow, 0)\n                                    ]).sort((a, b)=>a[1] - b[1])[0]) == null ? void 0 : _overflowsData$filter2[0];\n                                if (placement) {\n                                    resetPlacement = placement;\n                                }\n                                break;\n                            }\n                        case \"initialPlacement\":\n                            resetPlacement = initialPlacement;\n                            break;\n                    }\n                }\n                if (placement !== resetPlacement) {\n                    return {\n                        reset: {\n                            placement: resetPlacement\n                        }\n                    };\n                }\n            }\n            return {};\n        }\n    };\n};\nfunction getSideOffsets(overflow, rect) {\n    return {\n        top: overflow.top - rect.height,\n        right: overflow.right - rect.width,\n        bottom: overflow.bottom - rect.height,\n        left: overflow.left - rect.width\n    };\n}\nfunction isAnySideFullyClipped(overflow) {\n    return _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.sides.some((side)=>overflow[side] >= 0);\n}\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */ const hide = function(options) {\n    if (options === void 0) {\n        options = {};\n    }\n    return {\n        name: \"hide\",\n        options,\n        async fn (state) {\n            const { rects } = state;\n            const { strategy = \"referenceHidden\", ...detectOverflowOptions } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n            switch(strategy){\n                case \"referenceHidden\":\n                    {\n                        const overflow = await detectOverflow(state, {\n                            ...detectOverflowOptions,\n                            elementContext: \"reference\"\n                        });\n                        const offsets = getSideOffsets(overflow, rects.reference);\n                        return {\n                            data: {\n                                referenceHiddenOffsets: offsets,\n                                referenceHidden: isAnySideFullyClipped(offsets)\n                            }\n                        };\n                    }\n                case \"escaped\":\n                    {\n                        const overflow = await detectOverflow(state, {\n                            ...detectOverflowOptions,\n                            altBoundary: true\n                        });\n                        const offsets = getSideOffsets(overflow, rects.floating);\n                        return {\n                            data: {\n                                escapedOffsets: offsets,\n                                escaped: isAnySideFullyClipped(offsets)\n                            }\n                        };\n                    }\n                default:\n                    {\n                        return {};\n                    }\n            }\n        }\n    };\n};\nfunction getBoundingRect(rects) {\n    const minX = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(...rects.map((rect)=>rect.left));\n    const minY = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(...rects.map((rect)=>rect.top));\n    const maxX = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(...rects.map((rect)=>rect.right));\n    const maxY = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(...rects.map((rect)=>rect.bottom));\n    return {\n        x: minX,\n        y: minY,\n        width: maxX - minX,\n        height: maxY - minY\n    };\n}\nfunction getRectsByLine(rects) {\n    const sortedRects = rects.slice().sort((a, b)=>a.y - b.y);\n    const groups = [];\n    let prevRect = null;\n    for(let i = 0; i < sortedRects.length; i++){\n        const rect = sortedRects[i];\n        if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {\n            groups.push([\n                rect\n            ]);\n        } else {\n            groups[groups.length - 1].push(rect);\n        }\n        prevRect = rect;\n    }\n    return groups.map((rect)=>(0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect)(getBoundingRect(rect)));\n}\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */ const inline = function(options) {\n    if (options === void 0) {\n        options = {};\n    }\n    return {\n        name: \"inline\",\n        options,\n        async fn (state) {\n            const { placement, elements, rects, platform, strategy } = state;\n            // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a\n            // ClientRect's bounds, despite the event listener being triggered. A\n            // padding of 2 seems to handle this issue.\n            const { padding = 2, x, y } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n            const nativeClientRects = Array.from(await (platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference)) || []);\n            const clientRects = getRectsByLine(nativeClientRects);\n            const fallback = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect)(getBoundingRect(nativeClientRects));\n            const paddingObject = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getPaddingObject)(padding);\n            function getBoundingClientRect() {\n                // There are two rects and they are disjoined.\n                if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {\n                    // Find the first rect in which the point is fully inside.\n                    return clientRects.find((rect)=>x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom) || fallback;\n                }\n                // There are 2 or more connected rects.\n                if (clientRects.length >= 2) {\n                    if ((0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement) === \"y\") {\n                        const firstRect = clientRects[0];\n                        const lastRect = clientRects[clientRects.length - 1];\n                        const isTop = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement) === \"top\";\n                        const top = firstRect.top;\n                        const bottom = lastRect.bottom;\n                        const left = isTop ? firstRect.left : lastRect.left;\n                        const right = isTop ? firstRect.right : lastRect.right;\n                        const width = right - left;\n                        const height = bottom - top;\n                        return {\n                            top,\n                            bottom,\n                            left,\n                            right,\n                            width,\n                            height,\n                            x: left,\n                            y: top\n                        };\n                    }\n                    const isLeftSide = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement) === \"left\";\n                    const maxRight = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(...clientRects.map((rect)=>rect.right));\n                    const minLeft = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(...clientRects.map((rect)=>rect.left));\n                    const measureRects = clientRects.filter((rect)=>isLeftSide ? rect.left === minLeft : rect.right === maxRight);\n                    const top = measureRects[0].top;\n                    const bottom = measureRects[measureRects.length - 1].bottom;\n                    const left = minLeft;\n                    const right = maxRight;\n                    const width = right - left;\n                    const height = bottom - top;\n                    return {\n                        top,\n                        bottom,\n                        left,\n                        right,\n                        width,\n                        height,\n                        x: left,\n                        y: top\n                    };\n                }\n                return fallback;\n            }\n            const resetRects = await platform.getElementRects({\n                reference: {\n                    getBoundingClientRect\n                },\n                floating: elements.floating,\n                strategy\n            });\n            if (rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height) {\n                return {\n                    reset: {\n                        rects: resetRects\n                    }\n                };\n            }\n            return {};\n        }\n    };\n};\n// For type backwards-compatibility, the `OffsetOptions` type was also\n// Derivable.\nasync function convertValueToCoords(state, options) {\n    const { placement, platform, elements } = state;\n    const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n    const side = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement);\n    const alignment = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement);\n    const isVertical = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement) === \"y\";\n    const mainAxisMulti = [\n        \"left\",\n        \"top\"\n    ].includes(side) ? -1 : 1;\n    const crossAxisMulti = rtl && isVertical ? -1 : 1;\n    const rawValue = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n    // eslint-disable-next-line prefer-const\n    let { mainAxis, crossAxis, alignmentAxis } = typeof rawValue === \"number\" ? {\n        mainAxis: rawValue,\n        crossAxis: 0,\n        alignmentAxis: null\n    } : {\n        mainAxis: rawValue.mainAxis || 0,\n        crossAxis: rawValue.crossAxis || 0,\n        alignmentAxis: rawValue.alignmentAxis\n    };\n    if (alignment && typeof alignmentAxis === \"number\") {\n        crossAxis = alignment === \"end\" ? alignmentAxis * -1 : alignmentAxis;\n    }\n    return isVertical ? {\n        x: crossAxis * crossAxisMulti,\n        y: mainAxis * mainAxisMulti\n    } : {\n        x: mainAxis * mainAxisMulti,\n        y: crossAxis * crossAxisMulti\n    };\n}\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */ const offset = function(options) {\n    if (options === void 0) {\n        options = 0;\n    }\n    return {\n        name: \"offset\",\n        options,\n        async fn (state) {\n            var _middlewareData$offse, _middlewareData$arrow;\n            const { x, y, placement, middlewareData } = state;\n            const diffCoords = await convertValueToCoords(state, options);\n            // If the placement is the same and the arrow caused an alignment offset\n            // then we don't need to change the positioning coordinates.\n            if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n                return {};\n            }\n            return {\n                x: x + diffCoords.x,\n                y: y + diffCoords.y,\n                data: {\n                    ...diffCoords,\n                    placement\n                }\n            };\n        }\n    };\n};\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */ const shift = function(options) {\n    if (options === void 0) {\n        options = {};\n    }\n    return {\n        name: \"shift\",\n        options,\n        async fn (state) {\n            const { x, y, placement } = state;\n            const { mainAxis: checkMainAxis = true, crossAxis: checkCrossAxis = false, limiter = {\n                fn: (_ref)=>{\n                    let { x, y } = _ref;\n                    return {\n                        x,\n                        y\n                    };\n                }\n            }, ...detectOverflowOptions } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n            const coords = {\n                x,\n                y\n            };\n            const overflow = await detectOverflow(state, detectOverflowOptions);\n            const crossAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)((0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement));\n            const mainAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositeAxis)(crossAxis);\n            let mainAxisCoord = coords[mainAxis];\n            let crossAxisCoord = coords[crossAxis];\n            if (checkMainAxis) {\n                const minSide = mainAxis === \"y\" ? \"top\" : \"left\";\n                const maxSide = mainAxis === \"y\" ? \"bottom\" : \"right\";\n                const min = mainAxisCoord + overflow[minSide];\n                const max = mainAxisCoord - overflow[maxSide];\n                mainAxisCoord = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.clamp)(min, mainAxisCoord, max);\n            }\n            if (checkCrossAxis) {\n                const minSide = crossAxis === \"y\" ? \"top\" : \"left\";\n                const maxSide = crossAxis === \"y\" ? \"bottom\" : \"right\";\n                const min = crossAxisCoord + overflow[minSide];\n                const max = crossAxisCoord - overflow[maxSide];\n                crossAxisCoord = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.clamp)(min, crossAxisCoord, max);\n            }\n            const limitedCoords = limiter.fn({\n                ...state,\n                [mainAxis]: mainAxisCoord,\n                [crossAxis]: crossAxisCoord\n            });\n            return {\n                ...limitedCoords,\n                data: {\n                    x: limitedCoords.x - x,\n                    y: limitedCoords.y - y,\n                    enabled: {\n                        [mainAxis]: checkMainAxis,\n                        [crossAxis]: checkCrossAxis\n                    }\n                }\n            };\n        }\n    };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */ const limitShift = function(options) {\n    if (options === void 0) {\n        options = {};\n    }\n    return {\n        options,\n        fn (state) {\n            const { x, y, placement, rects, middlewareData } = state;\n            const { offset = 0, mainAxis: checkMainAxis = true, crossAxis: checkCrossAxis = true } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n            const coords = {\n                x,\n                y\n            };\n            const crossAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement);\n            const mainAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositeAxis)(crossAxis);\n            let mainAxisCoord = coords[mainAxis];\n            let crossAxisCoord = coords[crossAxis];\n            const rawOffset = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(offset, state);\n            const computedOffset = typeof rawOffset === \"number\" ? {\n                mainAxis: rawOffset,\n                crossAxis: 0\n            } : {\n                mainAxis: 0,\n                crossAxis: 0,\n                ...rawOffset\n            };\n            if (checkMainAxis) {\n                const len = mainAxis === \"y\" ? \"height\" : \"width\";\n                const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n                const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n                if (mainAxisCoord < limitMin) {\n                    mainAxisCoord = limitMin;\n                } else if (mainAxisCoord > limitMax) {\n                    mainAxisCoord = limitMax;\n                }\n            }\n            if (checkCrossAxis) {\n                var _middlewareData$offse, _middlewareData$offse2;\n                const len = mainAxis === \"y\" ? \"width\" : \"height\";\n                const isOriginSide = [\n                    \"top\",\n                    \"left\"\n                ].includes((0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement));\n                const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n                const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n                if (crossAxisCoord < limitMin) {\n                    crossAxisCoord = limitMin;\n                } else if (crossAxisCoord > limitMax) {\n                    crossAxisCoord = limitMax;\n                }\n            }\n            return {\n                [mainAxis]: mainAxisCoord,\n                [crossAxis]: crossAxisCoord\n            };\n        }\n    };\n};\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */ const size = function(options) {\n    if (options === void 0) {\n        options = {};\n    }\n    return {\n        name: \"size\",\n        options,\n        async fn (state) {\n            var _state$middlewareData, _state$middlewareData2;\n            const { placement, rects, platform, elements } = state;\n            const { apply = ()=>{}, ...detectOverflowOptions } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n            const overflow = await detectOverflow(state, detectOverflowOptions);\n            const side = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement);\n            const alignment = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement);\n            const isYAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement) === \"y\";\n            const { width, height } = rects.floating;\n            let heightSide;\n            let widthSide;\n            if (side === \"top\" || side === \"bottom\") {\n                heightSide = side;\n                widthSide = alignment === (await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)) ? \"start\" : \"end\") ? \"left\" : \"right\";\n            } else {\n                widthSide = side;\n                heightSide = alignment === \"end\" ? \"top\" : \"bottom\";\n            }\n            const maximumClippingHeight = height - overflow.top - overflow.bottom;\n            const maximumClippingWidth = width - overflow.left - overflow.right;\n            const overflowAvailableHeight = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(height - overflow[heightSide], maximumClippingHeight);\n            const overflowAvailableWidth = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(width - overflow[widthSide], maximumClippingWidth);\n            const noShift = !state.middlewareData.shift;\n            let availableHeight = overflowAvailableHeight;\n            let availableWidth = overflowAvailableWidth;\n            if ((_state$middlewareData = state.middlewareData.shift) != null && _state$middlewareData.enabled.x) {\n                availableWidth = maximumClippingWidth;\n            }\n            if ((_state$middlewareData2 = state.middlewareData.shift) != null && _state$middlewareData2.enabled.y) {\n                availableHeight = maximumClippingHeight;\n            }\n            if (noShift && !alignment) {\n                const xMin = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.left, 0);\n                const xMax = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.right, 0);\n                const yMin = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.top, 0);\n                const yMax = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.bottom, 0);\n                if (isYAxis) {\n                    availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.left, overflow.right));\n                } else {\n                    availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.top, overflow.bottom));\n                }\n            }\n            await apply({\n                ...state,\n                availableWidth,\n                availableHeight\n            });\n            const nextDimensions = await platform.getDimensions(elements.floating);\n            if (width !== nextDimensions.width || height !== nextDimensions.height) {\n                return {\n                    reset: {\n                        rects: true\n                    }\n                };\n            }\n            return {};\n        }\n    };\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@floating-ui/core/dist/floating-ui.core.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   arrow: () => (/* binding */ arrow),\n/* harmony export */   autoPlacement: () => (/* binding */ autoPlacement),\n/* harmony export */   autoUpdate: () => (/* binding */ autoUpdate),\n/* harmony export */   computePosition: () => (/* binding */ computePosition),\n/* harmony export */   detectOverflow: () => (/* binding */ detectOverflow),\n/* harmony export */   flip: () => (/* binding */ flip),\n/* harmony export */   getOverflowAncestors: () => (/* reexport safe */ _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getOverflowAncestors),\n/* harmony export */   hide: () => (/* binding */ hide),\n/* harmony export */   inline: () => (/* binding */ inline),\n/* harmony export */   limitShift: () => (/* binding */ limitShift),\n/* harmony export */   offset: () => (/* binding */ offset),\n/* harmony export */   platform: () => (/* binding */ platform),\n/* harmony export */   shift: () => (/* binding */ shift),\n/* harmony export */   size: () => (/* binding */ size)\n/* harmony export */ });\n/* harmony import */ var _floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @floating-ui/utils */ \"(ssr)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs\");\n/* harmony import */ var _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @floating-ui/core */ \"(ssr)/./node_modules/@floating-ui/core/dist/floating-ui.core.mjs\");\n/* harmony import */ var _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @floating-ui/utils/dom */ \"(ssr)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs\");\n\n\n\n\nfunction getCssDimensions(element) {\n    const css = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(element);\n    // In testing environments, the `width` and `height` properties are empty\n    // strings for SVG elements, returning NaN. Fallback to `0` in this case.\n    let width = parseFloat(css.width) || 0;\n    let height = parseFloat(css.height) || 0;\n    const hasOffset = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element);\n    const offsetWidth = hasOffset ? element.offsetWidth : width;\n    const offsetHeight = hasOffset ? element.offsetHeight : height;\n    const shouldFallback = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.round)(width) !== offsetWidth || (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.round)(height) !== offsetHeight;\n    if (shouldFallback) {\n        width = offsetWidth;\n        height = offsetHeight;\n    }\n    return {\n        width,\n        height,\n        $: shouldFallback\n    };\n}\nfunction unwrapElement(element) {\n    return !(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(element) ? element.contextElement : element;\n}\nfunction getScale(element) {\n    const domElement = unwrapElement(element);\n    if (!(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(domElement)) {\n        return (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(1);\n    }\n    const rect = domElement.getBoundingClientRect();\n    const { width, height, $ } = getCssDimensions(domElement);\n    let x = ($ ? (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.round)(rect.width) : rect.width) / width;\n    let y = ($ ? (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.round)(rect.height) : rect.height) / height;\n    // 0, NaN, or Infinity should always fallback to 1.\n    if (!x || !Number.isFinite(x)) {\n        x = 1;\n    }\n    if (!y || !Number.isFinite(y)) {\n        y = 1;\n    }\n    return {\n        x,\n        y\n    };\n}\nconst noOffsets = /*#__PURE__*/ (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\nfunction getVisualOffsets(element) {\n    const win = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(element);\n    if (!(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isWebKit)() || !win.visualViewport) {\n        return noOffsets;\n    }\n    return {\n        x: win.visualViewport.offsetLeft,\n        y: win.visualViewport.offsetTop\n    };\n}\nfunction shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {\n    if (isFixed === void 0) {\n        isFixed = false;\n    }\n    if (!floatingOffsetParent || isFixed && floatingOffsetParent !== (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(element)) {\n        return false;\n    }\n    return isFixed;\n}\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\n    if (includeScale === void 0) {\n        includeScale = false;\n    }\n    if (isFixedStrategy === void 0) {\n        isFixedStrategy = false;\n    }\n    const clientRect = element.getBoundingClientRect();\n    const domElement = unwrapElement(element);\n    let scale = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(1);\n    if (includeScale) {\n        if (offsetParent) {\n            if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(offsetParent)) {\n                scale = getScale(offsetParent);\n            }\n        } else {\n            scale = getScale(element);\n        }\n    }\n    const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\n    let x = (clientRect.left + visualOffsets.x) / scale.x;\n    let y = (clientRect.top + visualOffsets.y) / scale.y;\n    let width = clientRect.width / scale.x;\n    let height = clientRect.height / scale.y;\n    if (domElement) {\n        const win = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(domElement);\n        const offsetWin = offsetParent && (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(offsetParent) ? (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(offsetParent) : offsetParent;\n        let currentWin = win;\n        let currentIFrame = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getFrameElement)(currentWin);\n        while(currentIFrame && offsetParent && offsetWin !== currentWin){\n            const iframeScale = getScale(currentIFrame);\n            const iframeRect = currentIFrame.getBoundingClientRect();\n            const css = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(currentIFrame);\n            const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;\n            const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;\n            x *= iframeScale.x;\n            y *= iframeScale.y;\n            width *= iframeScale.x;\n            height *= iframeScale.y;\n            x += left;\n            y += top;\n            currentWin = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(currentIFrame);\n            currentIFrame = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getFrameElement)(currentWin);\n        }\n    }\n    return (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.rectToClientRect)({\n        width,\n        height,\n        x,\n        y\n    });\n}\n// If <html> has a CSS width greater than the viewport, then this will be\n// incorrect for RTL.\nfunction getWindowScrollBarX(element, rect) {\n    const leftScroll = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeScroll)(element).scrollLeft;\n    if (!rect) {\n        return getBoundingClientRect((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element)).left + leftScroll;\n    }\n    return rect.left + leftScroll;\n}\nfunction getHTMLOffset(documentElement, scroll, ignoreScrollbarX) {\n    if (ignoreScrollbarX === void 0) {\n        ignoreScrollbarX = false;\n    }\n    const htmlRect = documentElement.getBoundingClientRect();\n    const x = htmlRect.left + scroll.scrollLeft - (ignoreScrollbarX ? 0 : // RTL <body> scrollbar.\n    getWindowScrollBarX(documentElement, htmlRect));\n    const y = htmlRect.top + scroll.scrollTop;\n    return {\n        x,\n        y\n    };\n}\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\n    let { elements, rect, offsetParent, strategy } = _ref;\n    const isFixed = strategy === \"fixed\";\n    const documentElement = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(offsetParent);\n    const topLayer = elements ? (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isTopLayer)(elements.floating) : false;\n    if (offsetParent === documentElement || topLayer && isFixed) {\n        return rect;\n    }\n    let scroll = {\n        scrollLeft: 0,\n        scrollTop: 0\n    };\n    let scale = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(1);\n    const offsets = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\n    const isOffsetParentAnElement = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(offsetParent);\n    if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n        if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeName)(offsetParent) !== \"body\" || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isOverflowElement)(documentElement)) {\n            scroll = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeScroll)(offsetParent);\n        }\n        if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(offsetParent)) {\n            const offsetRect = getBoundingClientRect(offsetParent);\n            scale = getScale(offsetParent);\n            offsets.x = offsetRect.x + offsetParent.clientLeft;\n            offsets.y = offsetRect.y + offsetParent.clientTop;\n        }\n    }\n    const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll, true) : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\n    return {\n        width: rect.width * scale.x,\n        height: rect.height * scale.y,\n        x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x + htmlOffset.x,\n        y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y + htmlOffset.y\n    };\n}\nfunction getClientRects(element) {\n    return Array.from(element.getClientRects());\n}\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable.\nfunction getDocumentRect(element) {\n    const html = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element);\n    const scroll = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeScroll)(element);\n    const body = element.ownerDocument.body;\n    const width = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);\n    const height = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);\n    let x = -scroll.scrollLeft + getWindowScrollBarX(element);\n    const y = -scroll.scrollTop;\n    if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(body).direction === \"rtl\") {\n        x += (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(html.clientWidth, body.clientWidth) - width;\n    }\n    return {\n        width,\n        height,\n        x,\n        y\n    };\n}\nfunction getViewportRect(element, strategy) {\n    const win = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(element);\n    const html = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element);\n    const visualViewport = win.visualViewport;\n    let width = html.clientWidth;\n    let height = html.clientHeight;\n    let x = 0;\n    let y = 0;\n    if (visualViewport) {\n        width = visualViewport.width;\n        height = visualViewport.height;\n        const visualViewportBased = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isWebKit)();\n        if (!visualViewportBased || visualViewportBased && strategy === \"fixed\") {\n            x = visualViewport.offsetLeft;\n            y = visualViewport.offsetTop;\n        }\n    }\n    return {\n        width,\n        height,\n        x,\n        y\n    };\n}\n// Returns the inner client rect, subtracting scrollbars if present.\nfunction getInnerBoundingClientRect(element, strategy) {\n    const clientRect = getBoundingClientRect(element, true, strategy === \"fixed\");\n    const top = clientRect.top + element.clientTop;\n    const left = clientRect.left + element.clientLeft;\n    const scale = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element) ? getScale(element) : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(1);\n    const width = element.clientWidth * scale.x;\n    const height = element.clientHeight * scale.y;\n    const x = left * scale.x;\n    const y = top * scale.y;\n    return {\n        width,\n        height,\n        x,\n        y\n    };\n}\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\n    let rect;\n    if (clippingAncestor === \"viewport\") {\n        rect = getViewportRect(element, strategy);\n    } else if (clippingAncestor === \"document\") {\n        rect = getDocumentRect((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element));\n    } else if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(clippingAncestor)) {\n        rect = getInnerBoundingClientRect(clippingAncestor, strategy);\n    } else {\n        const visualOffsets = getVisualOffsets(element);\n        rect = {\n            x: clippingAncestor.x - visualOffsets.x,\n            y: clippingAncestor.y - visualOffsets.y,\n            width: clippingAncestor.width,\n            height: clippingAncestor.height\n        };\n    }\n    return (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.rectToClientRect)(rect);\n}\nfunction hasFixedPositionAncestor(element, stopNode) {\n    const parentNode = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getParentNode)(element);\n    if (parentNode === stopNode || !(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(parentNode) || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isLastTraversableNode)(parentNode)) {\n        return false;\n    }\n    return (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(parentNode).position === \"fixed\" || hasFixedPositionAncestor(parentNode, stopNode);\n}\n// A \"clipping ancestor\" is an `overflow` element with the characteristic of\n// clipping (or hiding) child elements. This returns all clipping ancestors\n// of the given element up the tree.\nfunction getClippingElementAncestors(element, cache) {\n    const cachedResult = cache.get(element);\n    if (cachedResult) {\n        return cachedResult;\n    }\n    let result = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getOverflowAncestors)(element, [], false).filter((el)=>(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(el) && (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeName)(el) !== \"body\");\n    let currentContainingBlockComputedStyle = null;\n    const elementIsFixed = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(element).position === \"fixed\";\n    let currentNode = elementIsFixed ? (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getParentNode)(element) : element;\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n    while((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(currentNode) && !(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isLastTraversableNode)(currentNode)){\n        const computedStyle = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(currentNode);\n        const currentNodeIsContaining = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isContainingBlock)(currentNode);\n        if (!currentNodeIsContaining && computedStyle.position === \"fixed\") {\n            currentContainingBlockComputedStyle = null;\n        }\n        const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === \"static\" && !!currentContainingBlockComputedStyle && [\n            \"absolute\",\n            \"fixed\"\n        ].includes(currentContainingBlockComputedStyle.position) || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isOverflowElement)(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);\n        if (shouldDropCurrentNode) {\n            // Drop non-containing blocks.\n            result = result.filter((ancestor)=>ancestor !== currentNode);\n        } else {\n            // Record last containing block for next iteration.\n            currentContainingBlockComputedStyle = computedStyle;\n        }\n        currentNode = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getParentNode)(currentNode);\n    }\n    cache.set(element, result);\n    return result;\n}\n// Gets the maximum area that the element is visible in due to any number of\n// clipping ancestors.\nfunction getClippingRect(_ref) {\n    let { element, boundary, rootBoundary, strategy } = _ref;\n    const elementClippingAncestors = boundary === \"clippingAncestors\" ? (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isTopLayer)(element) ? [] : getClippingElementAncestors(element, this._c) : [].concat(boundary);\n    const clippingAncestors = [\n        ...elementClippingAncestors,\n        rootBoundary\n    ];\n    const firstClippingAncestor = clippingAncestors[0];\n    const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor)=>{\n        const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\n        accRect.top = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(rect.top, accRect.top);\n        accRect.right = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.min)(rect.right, accRect.right);\n        accRect.bottom = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.min)(rect.bottom, accRect.bottom);\n        accRect.left = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(rect.left, accRect.left);\n        return accRect;\n    }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\n    return {\n        width: clippingRect.right - clippingRect.left,\n        height: clippingRect.bottom - clippingRect.top,\n        x: clippingRect.left,\n        y: clippingRect.top\n    };\n}\nfunction getDimensions(element) {\n    const { width, height } = getCssDimensions(element);\n    return {\n        width,\n        height\n    };\n}\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\n    const isOffsetParentAnElement = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(offsetParent);\n    const documentElement = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(offsetParent);\n    const isFixed = strategy === \"fixed\";\n    const rect = getBoundingClientRect(element, true, isFixed, offsetParent);\n    let scroll = {\n        scrollLeft: 0,\n        scrollTop: 0\n    };\n    const offsets = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\n    // If the <body> scrollbar appears on the left (e.g. RTL systems). Use\n    // Firefox with layout.scrollbar.side = 3 in about:config to test this.\n    function setLeftRTLScrollbarOffset() {\n        offsets.x = getWindowScrollBarX(documentElement);\n    }\n    if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n        if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeName)(offsetParent) !== \"body\" || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isOverflowElement)(documentElement)) {\n            scroll = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeScroll)(offsetParent);\n        }\n        if (isOffsetParentAnElement) {\n            const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);\n            offsets.x = offsetRect.x + offsetParent.clientLeft;\n            offsets.y = offsetRect.y + offsetParent.clientTop;\n        } else if (documentElement) {\n            setLeftRTLScrollbarOffset();\n        }\n    }\n    if (isFixed && !isOffsetParentAnElement && documentElement) {\n        setLeftRTLScrollbarOffset();\n    }\n    const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll) : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\n    const x = rect.left + scroll.scrollLeft - offsets.x - htmlOffset.x;\n    const y = rect.top + scroll.scrollTop - offsets.y - htmlOffset.y;\n    return {\n        x,\n        y,\n        width: rect.width,\n        height: rect.height\n    };\n}\nfunction isStaticPositioned(element) {\n    return (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(element).position === \"static\";\n}\nfunction getTrueOffsetParent(element, polyfill) {\n    if (!(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element) || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(element).position === \"fixed\") {\n        return null;\n    }\n    if (polyfill) {\n        return polyfill(element);\n    }\n    let rawOffsetParent = element.offsetParent;\n    // Firefox returns the <html> element as the offsetParent if it's non-static,\n    // while Chrome and Safari return the <body> element. The <body> element must\n    // be used to perform the correct calculations even if the <html> element is\n    // non-static.\n    if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element) === rawOffsetParent) {\n        rawOffsetParent = rawOffsetParent.ownerDocument.body;\n    }\n    return rawOffsetParent;\n}\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nfunction getOffsetParent(element, polyfill) {\n    const win = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(element);\n    if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isTopLayer)(element)) {\n        return win;\n    }\n    if (!(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element)) {\n        let svgOffsetParent = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getParentNode)(element);\n        while(svgOffsetParent && !(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isLastTraversableNode)(svgOffsetParent)){\n            if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(svgOffsetParent) && !isStaticPositioned(svgOffsetParent)) {\n                return svgOffsetParent;\n            }\n            svgOffsetParent = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getParentNode)(svgOffsetParent);\n        }\n        return win;\n    }\n    let offsetParent = getTrueOffsetParent(element, polyfill);\n    while(offsetParent && (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isTableElement)(offsetParent) && isStaticPositioned(offsetParent)){\n        offsetParent = getTrueOffsetParent(offsetParent, polyfill);\n    }\n    if (offsetParent && (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isLastTraversableNode)(offsetParent) && isStaticPositioned(offsetParent) && !(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isContainingBlock)(offsetParent)) {\n        return win;\n    }\n    return offsetParent || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getContainingBlock)(element) || win;\n}\nconst getElementRects = async function(data) {\n    const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\n    const getDimensionsFn = this.getDimensions;\n    const floatingDimensions = await getDimensionsFn(data.floating);\n    return {\n        reference: getRectRelativeToOffsetParent(data.reference, await getOffsetParentFn(data.floating), data.strategy),\n        floating: {\n            x: 0,\n            y: 0,\n            width: floatingDimensions.width,\n            height: floatingDimensions.height\n        }\n    };\n};\nfunction isRTL(element) {\n    return (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(element).direction === \"rtl\";\n}\nconst platform = {\n    convertOffsetParentRelativeRectToViewportRelativeRect,\n    getDocumentElement: _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement,\n    getClippingRect,\n    getOffsetParent,\n    getElementRects,\n    getClientRects,\n    getDimensions,\n    getScale,\n    isElement: _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement,\n    isRTL\n};\nfunction rectsAreEqual(a, b) {\n    return a.x === b.x && a.y === b.y && a.width === b.width && a.height === b.height;\n}\n// https://samthor.au/2021/observing-dom/\nfunction observeMove(element, onMove) {\n    let io = null;\n    let timeoutId;\n    const root = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element);\n    function cleanup() {\n        var _io;\n        clearTimeout(timeoutId);\n        (_io = io) == null || _io.disconnect();\n        io = null;\n    }\n    function refresh(skip, threshold) {\n        if (skip === void 0) {\n            skip = false;\n        }\n        if (threshold === void 0) {\n            threshold = 1;\n        }\n        cleanup();\n        const elementRectForRootMargin = element.getBoundingClientRect();\n        const { left, top, width, height } = elementRectForRootMargin;\n        if (!skip) {\n            onMove();\n        }\n        if (!width || !height) {\n            return;\n        }\n        const insetTop = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.floor)(top);\n        const insetRight = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.floor)(root.clientWidth - (left + width));\n        const insetBottom = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.floor)(root.clientHeight - (top + height));\n        const insetLeft = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.floor)(left);\n        const rootMargin = -insetTop + \"px \" + -insetRight + \"px \" + -insetBottom + \"px \" + -insetLeft + \"px\";\n        const options = {\n            rootMargin,\n            threshold: (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(0, (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.min)(1, threshold)) || 1\n        };\n        let isFirstUpdate = true;\n        function handleObserve(entries) {\n            const ratio = entries[0].intersectionRatio;\n            if (ratio !== threshold) {\n                if (!isFirstUpdate) {\n                    return refresh();\n                }\n                if (!ratio) {\n                    // If the reference is clipped, the ratio is 0. Throttle the refresh\n                    // to prevent an infinite loop of updates.\n                    timeoutId = setTimeout(()=>{\n                        refresh(false, 1e-7);\n                    }, 1000);\n                } else {\n                    refresh(false, ratio);\n                }\n            }\n            if (ratio === 1 && !rectsAreEqual(elementRectForRootMargin, element.getBoundingClientRect())) {\n                // It's possible that even though the ratio is reported as 1, the\n                // element is not actually fully within the IntersectionObserver's root\n                // area anymore. This can happen under performance constraints. This may\n                // be a bug in the browser's IntersectionObserver implementation. To\n                // work around this, we compare the element's bounding rect now with\n                // what it was at the time we created the IntersectionObserver. If they\n                // are not equal then the element moved, so we refresh.\n                refresh();\n            }\n            isFirstUpdate = false;\n        }\n        // Older browsers don't support a `document` as the root and will throw an\n        // error.\n        try {\n            io = new IntersectionObserver(handleObserve, {\n                ...options,\n                // Handle <iframe>s\n                root: root.ownerDocument\n            });\n        } catch (_e) {\n            io = new IntersectionObserver(handleObserve, options);\n        }\n        io.observe(element);\n    }\n    refresh(true);\n    return cleanup;\n}\n/**\n * Automatically updates the position of the floating element when necessary.\n * Should only be called when the floating element is mounted on the DOM or\n * visible on the screen.\n * @returns cleanup function that should be invoked when the floating element is\n * removed from the DOM or hidden from the screen.\n * @see https://floating-ui.com/docs/autoUpdate\n */ function autoUpdate(reference, floating, update, options) {\n    if (options === void 0) {\n        options = {};\n    }\n    const { ancestorScroll = true, ancestorResize = true, elementResize = typeof ResizeObserver === \"function\", layoutShift = typeof IntersectionObserver === \"function\", animationFrame = false } = options;\n    const referenceEl = unwrapElement(reference);\n    const ancestors = ancestorScroll || ancestorResize ? [\n        ...referenceEl ? (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getOverflowAncestors)(referenceEl) : [],\n        ...(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getOverflowAncestors)(floating)\n    ] : [];\n    ancestors.forEach((ancestor)=>{\n        ancestorScroll && ancestor.addEventListener(\"scroll\", update, {\n            passive: true\n        });\n        ancestorResize && ancestor.addEventListener(\"resize\", update);\n    });\n    const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;\n    let reobserveFrame = -1;\n    let resizeObserver = null;\n    if (elementResize) {\n        resizeObserver = new ResizeObserver((_ref)=>{\n            let [firstEntry] = _ref;\n            if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {\n                // Prevent update loops when using the `size` middleware.\n                // https://github.com/floating-ui/floating-ui/issues/1740\n                resizeObserver.unobserve(floating);\n                cancelAnimationFrame(reobserveFrame);\n                reobserveFrame = requestAnimationFrame(()=>{\n                    var _resizeObserver;\n                    (_resizeObserver = resizeObserver) == null || _resizeObserver.observe(floating);\n                });\n            }\n            update();\n        });\n        if (referenceEl && !animationFrame) {\n            resizeObserver.observe(referenceEl);\n        }\n        resizeObserver.observe(floating);\n    }\n    let frameId;\n    let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\n    if (animationFrame) {\n        frameLoop();\n    }\n    function frameLoop() {\n        const nextRefRect = getBoundingClientRect(reference);\n        if (prevRefRect && !rectsAreEqual(prevRefRect, nextRefRect)) {\n            update();\n        }\n        prevRefRect = nextRefRect;\n        frameId = requestAnimationFrame(frameLoop);\n    }\n    update();\n    return ()=>{\n        var _resizeObserver2;\n        ancestors.forEach((ancestor)=>{\n            ancestorScroll && ancestor.removeEventListener(\"scroll\", update);\n            ancestorResize && ancestor.removeEventListener(\"resize\", update);\n        });\n        cleanupIo == null || cleanupIo();\n        (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.disconnect();\n        resizeObserver = null;\n        if (animationFrame) {\n            cancelAnimationFrame(frameId);\n        }\n    };\n}\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */ const detectOverflow = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.detectOverflow;\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */ const offset = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.offset;\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */ const autoPlacement = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.autoPlacement;\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */ const shift = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.shift;\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */ const flip = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.flip;\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */ const size = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.size;\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */ const hide = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.hide;\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */ const arrow = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.arrow;\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */ const inline = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.inline;\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */ const limitShift = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.limitShift;\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n */ const computePosition = (reference, floating, options)=>{\n    // This caches the expensive `getClippingElementAncestors` function so that\n    // multiple lifecycle resets re-use the same result. It only lives for a\n    // single call. If other functions become expensive, we can add them as well.\n    const cache = new Map();\n    const mergedOptions = {\n        platform,\n        ...options\n    };\n    const platformWithCache = {\n        ...mergedOptions.platform,\n        _c: cache\n    };\n    return (0,_floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.computePosition)(reference, floating, {\n        ...mergedOptions,\n        platform: platformWithCache\n    });\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   arrow: () => (/* binding */ arrow),\n/* harmony export */   autoPlacement: () => (/* binding */ autoPlacement),\n/* harmony export */   autoUpdate: () => (/* reexport safe */ _floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.autoUpdate),\n/* harmony export */   computePosition: () => (/* reexport safe */ _floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.computePosition),\n/* harmony export */   detectOverflow: () => (/* reexport safe */ _floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.detectOverflow),\n/* harmony export */   flip: () => (/* binding */ flip),\n/* harmony export */   getOverflowAncestors: () => (/* reexport safe */ _floating_ui_dom__WEBPACK_IMPORTED_MODULE_1__.getOverflowAncestors),\n/* harmony export */   hide: () => (/* binding */ hide),\n/* harmony export */   inline: () => (/* binding */ inline),\n/* harmony export */   limitShift: () => (/* binding */ limitShift),\n/* harmony export */   offset: () => (/* binding */ offset),\n/* harmony export */   platform: () => (/* reexport safe */ _floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.platform),\n/* harmony export */   shift: () => (/* binding */ shift),\n/* harmony export */   size: () => (/* binding */ size),\n/* harmony export */   useFloating: () => (/* binding */ useFloating)\n/* harmony export */ });\n/* harmony import */ var _floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @floating-ui/dom */ \"(ssr)/./node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs\");\n/* harmony import */ var _floating_ui_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @floating-ui/dom */ \"(ssr)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n\n\n\n\n\nvar index = typeof document !== \"undefined\" ? react__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_2__.useEffect;\n// Fork of `fast-deep-equal` that only does the comparisons we need and compares\n// functions\nfunction deepEqual(a, b) {\n    if (a === b) {\n        return true;\n    }\n    if (typeof a !== typeof b) {\n        return false;\n    }\n    if (typeof a === \"function\" && a.toString() === b.toString()) {\n        return true;\n    }\n    let length;\n    let i;\n    let keys;\n    if (a && b && typeof a === \"object\") {\n        if (Array.isArray(a)) {\n            length = a.length;\n            if (length !== b.length) return false;\n            for(i = length; i-- !== 0;){\n                if (!deepEqual(a[i], b[i])) {\n                    return false;\n                }\n            }\n            return true;\n        }\n        keys = Object.keys(a);\n        length = keys.length;\n        if (length !== Object.keys(b).length) {\n            return false;\n        }\n        for(i = length; i-- !== 0;){\n            if (!({}).hasOwnProperty.call(b, keys[i])) {\n                return false;\n            }\n        }\n        for(i = length; i-- !== 0;){\n            const key = keys[i];\n            if (key === \"_owner\" && a.$$typeof) {\n                continue;\n            }\n            if (!deepEqual(a[key], b[key])) {\n                return false;\n            }\n        }\n        return true;\n    }\n    return a !== a && b !== b;\n}\nfunction getDPR(element) {\n    if (true) {\n        return 1;\n    }\n    const win = element.ownerDocument.defaultView || window;\n    return win.devicePixelRatio || 1;\n}\nfunction roundByDPR(element, value) {\n    const dpr = getDPR(element);\n    return Math.round(value * dpr) / dpr;\n}\nfunction useLatestRef(value) {\n    const ref = react__WEBPACK_IMPORTED_MODULE_2__.useRef(value);\n    index(()=>{\n        ref.current = value;\n    });\n    return ref;\n}\n/**\n * Provides data to position a floating element.\n * @see https://floating-ui.com/docs/useFloating\n */ function useFloating(options) {\n    if (options === void 0) {\n        options = {};\n    }\n    const { placement = \"bottom\", strategy = \"absolute\", middleware = [], platform, elements: { reference: externalReference, floating: externalFloating } = {}, transform = true, whileElementsMounted, open } = options;\n    const [data, setData] = react__WEBPACK_IMPORTED_MODULE_2__.useState({\n        x: 0,\n        y: 0,\n        strategy,\n        placement,\n        middlewareData: {},\n        isPositioned: false\n    });\n    const [latestMiddleware, setLatestMiddleware] = react__WEBPACK_IMPORTED_MODULE_2__.useState(middleware);\n    if (!deepEqual(latestMiddleware, middleware)) {\n        setLatestMiddleware(middleware);\n    }\n    const [_reference, _setReference] = react__WEBPACK_IMPORTED_MODULE_2__.useState(null);\n    const [_floating, _setFloating] = react__WEBPACK_IMPORTED_MODULE_2__.useState(null);\n    const setReference = react__WEBPACK_IMPORTED_MODULE_2__.useCallback((node)=>{\n        if (node !== referenceRef.current) {\n            referenceRef.current = node;\n            _setReference(node);\n        }\n    }, []);\n    const setFloating = react__WEBPACK_IMPORTED_MODULE_2__.useCallback((node)=>{\n        if (node !== floatingRef.current) {\n            floatingRef.current = node;\n            _setFloating(node);\n        }\n    }, []);\n    const referenceEl = externalReference || _reference;\n    const floatingEl = externalFloating || _floating;\n    const referenceRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(null);\n    const floatingRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(null);\n    const dataRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(data);\n    const hasWhileElementsMounted = whileElementsMounted != null;\n    const whileElementsMountedRef = useLatestRef(whileElementsMounted);\n    const platformRef = useLatestRef(platform);\n    const openRef = useLatestRef(open);\n    const update = react__WEBPACK_IMPORTED_MODULE_2__.useCallback(()=>{\n        if (!referenceRef.current || !floatingRef.current) {\n            return;\n        }\n        const config = {\n            placement,\n            strategy,\n            middleware: latestMiddleware\n        };\n        if (platformRef.current) {\n            config.platform = platformRef.current;\n        }\n        (0,_floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.computePosition)(referenceRef.current, floatingRef.current, config).then((data)=>{\n            const fullData = {\n                ...data,\n                // The floating element's position may be recomputed while it's closed\n                // but still mounted (such as when transitioning out). To ensure\n                // `isPositioned` will be `false` initially on the next open, avoid\n                // setting it to `true` when `open === false` (must be specified).\n                isPositioned: openRef.current !== false\n            };\n            if (isMountedRef.current && !deepEqual(dataRef.current, fullData)) {\n                dataRef.current = fullData;\n                react_dom__WEBPACK_IMPORTED_MODULE_3__.flushSync(()=>{\n                    setData(fullData);\n                });\n            }\n        });\n    }, [\n        latestMiddleware,\n        placement,\n        strategy,\n        platformRef,\n        openRef\n    ]);\n    index(()=>{\n        if (open === false && dataRef.current.isPositioned) {\n            dataRef.current.isPositioned = false;\n            setData((data)=>({\n                    ...data,\n                    isPositioned: false\n                }));\n        }\n    }, [\n        open\n    ]);\n    const isMountedRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(false);\n    index(()=>{\n        isMountedRef.current = true;\n        return ()=>{\n            isMountedRef.current = false;\n        };\n    }, []);\n    index(()=>{\n        if (referenceEl) referenceRef.current = referenceEl;\n        if (floatingEl) floatingRef.current = floatingEl;\n        if (referenceEl && floatingEl) {\n            if (whileElementsMountedRef.current) {\n                return whileElementsMountedRef.current(referenceEl, floatingEl, update);\n            }\n            update();\n        }\n    }, [\n        referenceEl,\n        floatingEl,\n        update,\n        whileElementsMountedRef,\n        hasWhileElementsMounted\n    ]);\n    const refs = react__WEBPACK_IMPORTED_MODULE_2__.useMemo(()=>({\n            reference: referenceRef,\n            floating: floatingRef,\n            setReference,\n            setFloating\n        }), [\n        setReference,\n        setFloating\n    ]);\n    const elements = react__WEBPACK_IMPORTED_MODULE_2__.useMemo(()=>({\n            reference: referenceEl,\n            floating: floatingEl\n        }), [\n        referenceEl,\n        floatingEl\n    ]);\n    const floatingStyles = react__WEBPACK_IMPORTED_MODULE_2__.useMemo(()=>{\n        const initialStyles = {\n            position: strategy,\n            left: 0,\n            top: 0\n        };\n        if (!elements.floating) {\n            return initialStyles;\n        }\n        const x = roundByDPR(elements.floating, data.x);\n        const y = roundByDPR(elements.floating, data.y);\n        if (transform) {\n            return {\n                ...initialStyles,\n                transform: \"translate(\" + x + \"px, \" + y + \"px)\",\n                ...getDPR(elements.floating) >= 1.5 && {\n                    willChange: \"transform\"\n                }\n            };\n        }\n        return {\n            position: strategy,\n            left: x,\n            top: y\n        };\n    }, [\n        strategy,\n        transform,\n        elements.floating,\n        data.x,\n        data.y\n    ]);\n    return react__WEBPACK_IMPORTED_MODULE_2__.useMemo(()=>({\n            ...data,\n            update,\n            refs,\n            elements,\n            floatingStyles\n        }), [\n        data,\n        update,\n        refs,\n        elements,\n        floatingStyles\n    ]);\n}\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * This wraps the core `arrow` middleware to allow React refs as the element.\n * @see https://floating-ui.com/docs/arrow\n */ const arrow$1 = (options)=>{\n    function isRef(value) {\n        return ({}).hasOwnProperty.call(value, \"current\");\n    }\n    return {\n        name: \"arrow\",\n        options,\n        fn (state) {\n            const { element, padding } = typeof options === \"function\" ? options(state) : options;\n            if (element && isRef(element)) {\n                if (element.current != null) {\n                    return (0,_floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.arrow)({\n                        element: element.current,\n                        padding\n                    }).fn(state);\n                }\n                return {};\n            }\n            if (element) {\n                return (0,_floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.arrow)({\n                    element,\n                    padding\n                }).fn(state);\n            }\n            return {};\n        }\n    };\n};\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */ const offset = (options, deps)=>({\n        ...(0,_floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.offset)(options),\n        options: [\n            options,\n            deps\n        ]\n    });\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */ const shift = (options, deps)=>({\n        ...(0,_floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.shift)(options),\n        options: [\n            options,\n            deps\n        ]\n    });\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */ const limitShift = (options, deps)=>({\n        ...(0,_floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.limitShift)(options),\n        options: [\n            options,\n            deps\n        ]\n    });\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */ const flip = (options, deps)=>({\n        ...(0,_floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.flip)(options),\n        options: [\n            options,\n            deps\n        ]\n    });\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */ const size = (options, deps)=>({\n        ...(0,_floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.size)(options),\n        options: [\n            options,\n            deps\n        ]\n    });\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */ const autoPlacement = (options, deps)=>({\n        ...(0,_floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.autoPlacement)(options),\n        options: [\n            options,\n            deps\n        ]\n    });\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */ const hide = (options, deps)=>({\n        ...(0,_floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.hide)(options),\n        options: [\n            options,\n            deps\n        ]\n    });\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */ const inline = (options, deps)=>({\n        ...(0,_floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.inline)(options),\n        options: [\n            options,\n            deps\n        ]\n    });\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * This wraps the core `arrow` middleware to allow React refs as the element.\n * @see https://floating-ui.com/docs/arrow\n */ const arrow = (options, deps)=>({\n        ...arrow$1(options),\n        options: [\n            options,\n            deps\n        ]\n    });\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getComputedStyle: () => (/* binding */ getComputedStyle),\n/* harmony export */   getContainingBlock: () => (/* binding */ getContainingBlock),\n/* harmony export */   getDocumentElement: () => (/* binding */ getDocumentElement),\n/* harmony export */   getFrameElement: () => (/* binding */ getFrameElement),\n/* harmony export */   getNearestOverflowAncestor: () => (/* binding */ getNearestOverflowAncestor),\n/* harmony export */   getNodeName: () => (/* binding */ getNodeName),\n/* harmony export */   getNodeScroll: () => (/* binding */ getNodeScroll),\n/* harmony export */   getOverflowAncestors: () => (/* binding */ getOverflowAncestors),\n/* harmony export */   getParentNode: () => (/* binding */ getParentNode),\n/* harmony export */   getWindow: () => (/* binding */ getWindow),\n/* harmony export */   isContainingBlock: () => (/* binding */ isContainingBlock),\n/* harmony export */   isElement: () => (/* binding */ isElement),\n/* harmony export */   isHTMLElement: () => (/* binding */ isHTMLElement),\n/* harmony export */   isLastTraversableNode: () => (/* binding */ isLastTraversableNode),\n/* harmony export */   isNode: () => (/* binding */ isNode),\n/* harmony export */   isOverflowElement: () => (/* binding */ isOverflowElement),\n/* harmony export */   isShadowRoot: () => (/* binding */ isShadowRoot),\n/* harmony export */   isTableElement: () => (/* binding */ isTableElement),\n/* harmony export */   isTopLayer: () => (/* binding */ isTopLayer),\n/* harmony export */   isWebKit: () => (/* binding */ isWebKit)\n/* harmony export */ });\nfunction hasWindow() {\n    return \"undefined\" !== \"undefined\";\n}\nfunction getNodeName(node) {\n    if (isNode(node)) {\n        return (node.nodeName || \"\").toLowerCase();\n    }\n    // Mocked nodes in testing environments may not be instances of Node. By\n    // returning `#document` an infinite loop won't occur.\n    // https://github.com/floating-ui/floating-ui/issues/2317\n    return \"#document\";\n}\nfunction getWindow(node) {\n    var _node$ownerDocument;\n    return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n    var _ref;\n    return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n    if (!hasWindow()) {\n        return false;\n    }\n    return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n    if (!hasWindow()) {\n        return false;\n    }\n    return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n    if (!hasWindow()) {\n        return false;\n    }\n    return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n    if (!hasWindow() || typeof ShadowRoot === \"undefined\") {\n        return false;\n    }\n    return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nfunction isOverflowElement(element) {\n    const { overflow, overflowX, overflowY, display } = getComputedStyle(element);\n    return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && ![\n        \"inline\",\n        \"contents\"\n    ].includes(display);\n}\nfunction isTableElement(element) {\n    return [\n        \"table\",\n        \"td\",\n        \"th\"\n    ].includes(getNodeName(element));\n}\nfunction isTopLayer(element) {\n    return [\n        \":popover-open\",\n        \":modal\"\n    ].some((selector)=>{\n        try {\n            return element.matches(selector);\n        } catch (e) {\n            return false;\n        }\n    });\n}\nfunction isContainingBlock(elementOrCss) {\n    const webkit = isWebKit();\n    const css = isElement(elementOrCss) ? getComputedStyle(elementOrCss) : elementOrCss;\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n    // https://drafts.csswg.org/css-transforms-2/#individual-transforms\n    return [\n        \"transform\",\n        \"translate\",\n        \"scale\",\n        \"rotate\",\n        \"perspective\"\n    ].some((value)=>css[value] ? css[value] !== \"none\" : false) || (css.containerType ? css.containerType !== \"normal\" : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== \"none\" : false) || !webkit && (css.filter ? css.filter !== \"none\" : false) || [\n        \"transform\",\n        \"translate\",\n        \"scale\",\n        \"rotate\",\n        \"perspective\",\n        \"filter\"\n    ].some((value)=>(css.willChange || \"\").includes(value)) || [\n        \"paint\",\n        \"layout\",\n        \"strict\",\n        \"content\"\n    ].some((value)=>(css.contain || \"\").includes(value));\n}\nfunction getContainingBlock(element) {\n    let currentNode = getParentNode(element);\n    while(isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)){\n        if (isContainingBlock(currentNode)) {\n            return currentNode;\n        } else if (isTopLayer(currentNode)) {\n            return null;\n        }\n        currentNode = getParentNode(currentNode);\n    }\n    return null;\n}\nfunction isWebKit() {\n    if (typeof CSS === \"undefined\" || !CSS.supports) return false;\n    return CSS.supports(\"-webkit-backdrop-filter\", \"none\");\n}\nfunction isLastTraversableNode(node) {\n    return [\n        \"html\",\n        \"body\",\n        \"#document\"\n    ].includes(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n    return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n    if (isElement(element)) {\n        return {\n            scrollLeft: element.scrollLeft,\n            scrollTop: element.scrollTop\n        };\n    }\n    return {\n        scrollLeft: element.scrollX,\n        scrollTop: element.scrollY\n    };\n}\nfunction getParentNode(node) {\n    if (getNodeName(node) === \"html\") {\n        return node;\n    }\n    const result = // Step into the shadow DOM of the parent of a slotted node.\n    node.assignedSlot || // DOM Element detected.\n    node.parentNode || // ShadowRoot detected.\n    isShadowRoot(node) && node.host || // Fallback.\n    getDocumentElement(node);\n    return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n    const parentNode = getParentNode(node);\n    if (isLastTraversableNode(parentNode)) {\n        return node.ownerDocument ? node.ownerDocument.body : node.body;\n    }\n    if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n        return parentNode;\n    }\n    return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n    var _node$ownerDocument2;\n    if (list === void 0) {\n        list = [];\n    }\n    if (traverseIframes === void 0) {\n        traverseIframes = true;\n    }\n    const scrollableAncestor = getNearestOverflowAncestor(node);\n    const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n    const win = getWindow(scrollableAncestor);\n    if (isBody) {\n        const frameElement = getFrameElement(win);\n        return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);\n    }\n    return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\nfunction getFrameElement(win) {\n    return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs":
/*!********************************************************************!*\
  !*** ./node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alignments: () => (/* binding */ alignments),\n/* harmony export */   clamp: () => (/* binding */ clamp),\n/* harmony export */   createCoords: () => (/* binding */ createCoords),\n/* harmony export */   evaluate: () => (/* binding */ evaluate),\n/* harmony export */   expandPaddingObject: () => (/* binding */ expandPaddingObject),\n/* harmony export */   floor: () => (/* binding */ floor),\n/* harmony export */   getAlignment: () => (/* binding */ getAlignment),\n/* harmony export */   getAlignmentAxis: () => (/* binding */ getAlignmentAxis),\n/* harmony export */   getAlignmentSides: () => (/* binding */ getAlignmentSides),\n/* harmony export */   getAxisLength: () => (/* binding */ getAxisLength),\n/* harmony export */   getExpandedPlacements: () => (/* binding */ getExpandedPlacements),\n/* harmony export */   getOppositeAlignmentPlacement: () => (/* binding */ getOppositeAlignmentPlacement),\n/* harmony export */   getOppositeAxis: () => (/* binding */ getOppositeAxis),\n/* harmony export */   getOppositeAxisPlacements: () => (/* binding */ getOppositeAxisPlacements),\n/* harmony export */   getOppositePlacement: () => (/* binding */ getOppositePlacement),\n/* harmony export */   getPaddingObject: () => (/* binding */ getPaddingObject),\n/* harmony export */   getSide: () => (/* binding */ getSide),\n/* harmony export */   getSideAxis: () => (/* binding */ getSideAxis),\n/* harmony export */   max: () => (/* binding */ max),\n/* harmony export */   min: () => (/* binding */ min),\n/* harmony export */   placements: () => (/* binding */ placements),\n/* harmony export */   rectToClientRect: () => (/* binding */ rectToClientRect),\n/* harmony export */   round: () => (/* binding */ round),\n/* harmony export */   sides: () => (/* binding */ sides)\n/* harmony export */ });\n/**\n * Custom positioning reference element.\n * @see https://floating-ui.com/docs/virtual-elements\n */ const sides = [\n    \"top\",\n    \"right\",\n    \"bottom\",\n    \"left\"\n];\nconst alignments = [\n    \"start\",\n    \"end\"\n];\nconst placements = /*#__PURE__*/ sides.reduce((acc, side)=>acc.concat(side, side + \"-\" + alignments[0], side + \"-\" + alignments[1]), []);\nconst min = Math.min;\nconst max = Math.max;\nconst round = Math.round;\nconst floor = Math.floor;\nconst createCoords = (v)=>({\n        x: v,\n        y: v\n    });\nconst oppositeSideMap = {\n    left: \"right\",\n    right: \"left\",\n    bottom: \"top\",\n    top: \"bottom\"\n};\nconst oppositeAlignmentMap = {\n    start: \"end\",\n    end: \"start\"\n};\nfunction clamp(start, value, end) {\n    return max(start, min(value, end));\n}\nfunction evaluate(value, param) {\n    return typeof value === \"function\" ? value(param) : value;\n}\nfunction getSide(placement) {\n    return placement.split(\"-\")[0];\n}\nfunction getAlignment(placement) {\n    return placement.split(\"-\")[1];\n}\nfunction getOppositeAxis(axis) {\n    return axis === \"x\" ? \"y\" : \"x\";\n}\nfunction getAxisLength(axis) {\n    return axis === \"y\" ? \"height\" : \"width\";\n}\nfunction getSideAxis(placement) {\n    return [\n        \"top\",\n        \"bottom\"\n    ].includes(getSide(placement)) ? \"y\" : \"x\";\n}\nfunction getAlignmentAxis(placement) {\n    return getOppositeAxis(getSideAxis(placement));\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n    if (rtl === void 0) {\n        rtl = false;\n    }\n    const alignment = getAlignment(placement);\n    const alignmentAxis = getAlignmentAxis(placement);\n    const length = getAxisLength(alignmentAxis);\n    let mainAlignmentSide = alignmentAxis === \"x\" ? alignment === (rtl ? \"end\" : \"start\") ? \"right\" : \"left\" : alignment === \"start\" ? \"bottom\" : \"top\";\n    if (rects.reference[length] > rects.floating[length]) {\n        mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n    }\n    return [\n        mainAlignmentSide,\n        getOppositePlacement(mainAlignmentSide)\n    ];\n}\nfunction getExpandedPlacements(placement) {\n    const oppositePlacement = getOppositePlacement(placement);\n    return [\n        getOppositeAlignmentPlacement(placement),\n        oppositePlacement,\n        getOppositeAlignmentPlacement(oppositePlacement)\n    ];\n}\nfunction getOppositeAlignmentPlacement(placement) {\n    return placement.replace(/start|end/g, (alignment)=>oppositeAlignmentMap[alignment]);\n}\nfunction getSideList(side, isStart, rtl) {\n    const lr = [\n        \"left\",\n        \"right\"\n    ];\n    const rl = [\n        \"right\",\n        \"left\"\n    ];\n    const tb = [\n        \"top\",\n        \"bottom\"\n    ];\n    const bt = [\n        \"bottom\",\n        \"top\"\n    ];\n    switch(side){\n        case \"top\":\n        case \"bottom\":\n            if (rtl) return isStart ? rl : lr;\n            return isStart ? lr : rl;\n        case \"left\":\n        case \"right\":\n            return isStart ? tb : bt;\n        default:\n            return [];\n    }\n}\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\n    const alignment = getAlignment(placement);\n    let list = getSideList(getSide(placement), direction === \"start\", rtl);\n    if (alignment) {\n        list = list.map((side)=>side + \"-\" + alignment);\n        if (flipAlignment) {\n            list = list.concat(list.map(getOppositeAlignmentPlacement));\n        }\n    }\n    return list;\n}\nfunction getOppositePlacement(placement) {\n    return placement.replace(/left|right|bottom|top/g, (side)=>oppositeSideMap[side]);\n}\nfunction expandPaddingObject(padding) {\n    return {\n        top: 0,\n        right: 0,\n        bottom: 0,\n        left: 0,\n        ...padding\n    };\n}\nfunction getPaddingObject(padding) {\n    return typeof padding !== \"number\" ? expandPaddingObject(padding) : {\n        top: padding,\n        right: padding,\n        bottom: padding,\n        left: padding\n    };\n}\nfunction rectToClientRect(rect) {\n    const { x, y, width, height } = rect;\n    return {\n        width,\n        height,\n        top: y,\n        left: x,\n        right: x + width,\n        bottom: y + height,\n        x,\n        y\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs\n");

/***/ })

};
;