"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/use-intl";
exports.ids = ["vendor-chunks/use-intl"];
exports.modules = {

/***/ "(ssr)/./node_modules/use-intl/dist/_IntlProvider.js":
/*!*****************************************************!*\
  !*** ./node_modules/use-intl/dist/_IntlProvider.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nif (false) {} else {\n    module.exports = __webpack_require__(/*! ./development/_IntlProvider.js */ \"(ssr)/./node_modules/use-intl/dist/development/_IntlProvider.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9fSW50bFByb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBRUEsSUFBSUEsS0FBeUIsRUFBYyxFQUUxQyxNQUFNO0lBQ0xDLDZJQUF5QjtBQUMzQiIsInNvdXJjZXMiOlsid2VicGFjazovL3NtZS1hbmFseXRpY2EtZG9jcy8uL25vZGVfbW9kdWxlcy91c2UtaW50bC9kaXN0L19JbnRsUHJvdmlkZXIuanM/YTg2YiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9wcm9kdWN0aW9uL19JbnRsUHJvdmlkZXIuanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9kZXZlbG9wbWVudC9fSW50bFByb3ZpZGVyLmpzJyk7XG59XG4iXSwibmFtZXMiOlsicHJvY2VzcyIsIm1vZHVsZSIsImV4cG9ydHMiLCJyZXF1aXJlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/_IntlProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/_useLocale.js":
/*!**************************************************!*\
  !*** ./node_modules/use-intl/dist/_useLocale.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nif (false) {} else {\n    module.exports = __webpack_require__(/*! ./development/_useLocale.js */ \"(ssr)/./node_modules/use-intl/dist/development/_useLocale.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9fdXNlTG9jYWxlLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBRUEsSUFBSUEsS0FBeUIsRUFBYyxFQUUxQyxNQUFNO0lBQ0xDLHVJQUF5QjtBQUMzQiIsInNvdXJjZXMiOlsid2VicGFjazovL3NtZS1hbmFseXRpY2EtZG9jcy8uL25vZGVfbW9kdWxlcy91c2UtaW50bC9kaXN0L191c2VMb2NhbGUuanM/MTYxYSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9wcm9kdWN0aW9uL191c2VMb2NhbGUuanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9kZXZlbG9wbWVudC9fdXNlTG9jYWxlLmpzJyk7XG59XG4iXSwibmFtZXMiOlsicHJvY2VzcyIsIm1vZHVsZSIsImV4cG9ydHMiLCJyZXF1aXJlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/_useLocale.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js":
/*!************************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nconst IntlContext = /*#__PURE__*/ React.createContext(undefined);\nexports.IntlContext = IntlContext;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9kZXZlbG9wbWVudC9JbnRsQ29udGV4dC1CS2ZzbnpCeC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUVBLElBQUlBLFFBQVFDLG1CQUFPQSxDQUFDO0FBRXBCLE1BQU1DLGNBQWMsV0FBVyxHQUFFRixNQUFNRyxhQUFhLENBQUNDO0FBRXJEQyxtQkFBbUIsR0FBR0giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zbWUtYW5hbHl0aWNhLWRvY3MvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9kZXZlbG9wbWVudC9JbnRsQ29udGV4dC1CS2ZzbnpCeC5qcz81NzYwIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxudmFyIFJlYWN0ID0gcmVxdWlyZSgncmVhY3QnKTtcblxuY29uc3QgSW50bENvbnRleHQgPSAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlQ29udGV4dCh1bmRlZmluZWQpO1xuXG5leHBvcnRzLkludGxDb250ZXh0ID0gSW50bENvbnRleHQ7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJyZXF1aXJlIiwiSW50bENvbnRleHQiLCJjcmVhdGVDb250ZXh0IiwidW5kZWZpbmVkIiwiZXhwb3J0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/_IntlProvider.js":
/*!*****************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/_IntlProvider.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\nvar IntlContext = __webpack_require__(/*! ./IntlContext-BKfsnzBx.js */ \"(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\");\n__webpack_require__(/*! @formatjs/fast-memoize */ \"(ssr)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\nfunction _interopDefault(e) {\n    return e && e.__esModule ? e : {\n        default: e\n    };\n}\nvar React__default = /*#__PURE__*/ _interopDefault(React);\nfunction IntlProvider(_ref) {\n    let { children, defaultTranslationValues, formats, getMessageFallback, locale, messages, now, onError, timeZone } = _ref;\n    // The formatter cache is released when the locale changes. For\n    // long-running apps with a persistent `IntlProvider` at the root,\n    // this can reduce the memory footprint (e.g. in React Native).\n    const cache = React.useMemo(()=>{\n        return initializeConfig.createCache();\n    }, [\n        locale\n    ]);\n    const formatters = React.useMemo(()=>initializeConfig.createIntlFormatters(cache), [\n        cache\n    ]);\n    // Memoizing this value helps to avoid triggering a re-render of all\n    // context consumers in case the configuration didn't change. However,\n    // if some of the non-primitive values change, a re-render will still\n    // be triggered. Note that there's no need to put `memo` on `IntlProvider`\n    // itself, because the `children` typically change on every render.\n    // There's some burden on the consumer side if it's important to reduce\n    // re-renders, put that's how React works.\n    // See: https://blog.isquaredsoftware.com/2020/05/blogged-answers-a-mostly-complete-guide-to-react-rendering-behavior/#context-updates-and-render-optimizations\n    const value = React.useMemo(()=>({\n            ...initializeConfig.initializeConfig({\n                locale,\n                defaultTranslationValues,\n                formats,\n                getMessageFallback,\n                messages,\n                now,\n                onError,\n                timeZone\n            }),\n            formatters,\n            cache\n        }), [\n        cache,\n        defaultTranslationValues,\n        formats,\n        formatters,\n        getMessageFallback,\n        locale,\n        messages,\n        now,\n        onError,\n        timeZone\n    ]);\n    return /*#__PURE__*/ React__default.default.createElement(IntlContext.IntlContext.Provider, {\n        value: value\n    }, children);\n}\nexports.IntlProvider = IntlProvider;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/_IntlProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/_useLocale-BK3jOeaA.js":
/*!***********************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/_useLocale-BK3jOeaA.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar IntlContext = __webpack_require__(/*! ./IntlContext-BKfsnzBx.js */ \"(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\");\nfunction useIntlContext() {\n    const context = React.useContext(IntlContext.IntlContext);\n    if (!context) {\n        throw new Error(\"No intl context found. Have you configured the provider? See https://next-intl.dev/docs/usage/configuration#client-server-components\");\n    }\n    return context;\n}\nfunction useLocale() {\n    return useIntlContext().locale;\n}\nexports.useIntlContext = useIntlContext;\nexports.useLocale = useLocale;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9kZXZlbG9wbWVudC9fdXNlTG9jYWxlLUJLM2pPZWFBLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBRUEsSUFBSUEsUUFBUUMsbUJBQU9BLENBQUM7QUFDcEIsSUFBSUMsY0FBY0QsbUJBQU9BLENBQUM7QUFFMUIsU0FBU0U7SUFDUCxNQUFNQyxVQUFVSixNQUFNSyxVQUFVLENBQUNILFlBQVlBLFdBQVc7SUFDeEQsSUFBSSxDQUFDRSxTQUFTO1FBQ1osTUFBTSxJQUFJRSxNQUFNO0lBQ2xCO0lBQ0EsT0FBT0Y7QUFDVDtBQUVBLFNBQVNHO0lBQ1AsT0FBT0osaUJBQWlCSyxNQUFNO0FBQ2hDO0FBRUFDLHNCQUFzQixHQUFHTjtBQUN6Qk0saUJBQWlCLEdBQUdGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc21lLWFuYWx5dGljYS1kb2NzLy4vbm9kZV9tb2R1bGVzL3VzZS1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvX3VzZUxvY2FsZS1CSzNqT2VhQS5qcz84NGQ2Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxudmFyIFJlYWN0ID0gcmVxdWlyZSgncmVhY3QnKTtcbnZhciBJbnRsQ29udGV4dCA9IHJlcXVpcmUoJy4vSW50bENvbnRleHQtQktmc256QnguanMnKTtcblxuZnVuY3Rpb24gdXNlSW50bENvbnRleHQoKSB7XG4gIGNvbnN0IGNvbnRleHQgPSBSZWFjdC51c2VDb250ZXh0KEludGxDb250ZXh0LkludGxDb250ZXh0KTtcbiAgaWYgKCFjb250ZXh0KSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdObyBpbnRsIGNvbnRleHQgZm91bmQuIEhhdmUgeW91IGNvbmZpZ3VyZWQgdGhlIHByb3ZpZGVyPyBTZWUgaHR0cHM6Ly9uZXh0LWludGwuZGV2L2RvY3MvdXNhZ2UvY29uZmlndXJhdGlvbiNjbGllbnQtc2VydmVyLWNvbXBvbmVudHMnICk7XG4gIH1cbiAgcmV0dXJuIGNvbnRleHQ7XG59XG5cbmZ1bmN0aW9uIHVzZUxvY2FsZSgpIHtcbiAgcmV0dXJuIHVzZUludGxDb250ZXh0KCkubG9jYWxlO1xufVxuXG5leHBvcnRzLnVzZUludGxDb250ZXh0ID0gdXNlSW50bENvbnRleHQ7XG5leHBvcnRzLnVzZUxvY2FsZSA9IHVzZUxvY2FsZTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInJlcXVpcmUiLCJJbnRsQ29udGV4dCIsInVzZUludGxDb250ZXh0IiwiY29udGV4dCIsInVzZUNvbnRleHQiLCJFcnJvciIsInVzZUxvY2FsZSIsImxvY2FsZSIsImV4cG9ydHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/_useLocale-BK3jOeaA.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/_useLocale.js":
/*!**************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/_useLocale.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _useLocale = __webpack_require__(/*! ./_useLocale-BK3jOeaA.js */ \"(ssr)/./node_modules/use-intl/dist/development/_useLocale-BK3jOeaA.js\");\n__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n__webpack_require__(/*! ./IntlContext-BKfsnzBx.js */ \"(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\");\nexports.useLocale = _useLocale.useLocale;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9kZXZlbG9wbWVudC9fdXNlTG9jYWxlLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBRUFBLDhDQUE2QztJQUFFRyxPQUFPO0FBQUssQ0FBQyxFQUFDO0FBRTdELElBQUlDLGFBQWFDLG1CQUFPQSxDQUFDO0FBQ3pCQSxtQkFBT0EsQ0FBQztBQUNSQSxtQkFBT0EsQ0FBQztBQUlSSCxpQkFBaUIsR0FBR0UsV0FBV0UsU0FBUyIsInNvdXJjZXMiOlsid2VicGFjazovL3NtZS1hbmFseXRpY2EtZG9jcy8uL25vZGVfbW9kdWxlcy91c2UtaW50bC9kaXN0L2RldmVsb3BtZW50L191c2VMb2NhbGUuanM/NTBmMiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAnX19lc01vZHVsZScsIHsgdmFsdWU6IHRydWUgfSk7XG5cbnZhciBfdXNlTG9jYWxlID0gcmVxdWlyZSgnLi9fdXNlTG9jYWxlLUJLM2pPZWFBLmpzJyk7XG5yZXF1aXJlKCdyZWFjdCcpO1xucmVxdWlyZSgnLi9JbnRsQ29udGV4dC1CS2ZzbnpCeC5qcycpO1xuXG5cblxuZXhwb3J0cy51c2VMb2NhbGUgPSBfdXNlTG9jYWxlLnVzZUxvY2FsZTtcbiJdLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsIl91c2VMb2NhbGUiLCJyZXF1aXJlIiwidXNlTG9jYWxlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/_useLocale.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/core.js":
/*!********************************************************!*\
  !*** ./node_modules/use-intl/dist/development/core.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\nvar createFormatter = __webpack_require__(/*! ./createFormatter-QqAaZwGD.js */ \"(ssr)/./node_modules/use-intl/dist/development/createFormatter-QqAaZwGD.js\");\n__webpack_require__(/*! @formatjs/fast-memoize */ \"(ssr)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n__webpack_require__(/*! intl-messageformat */ \"(ssr)/./node_modules/intl-messageformat/lib/index.js\");\n__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nfunction createTranslatorImpl(_ref, namespacePrefix) {\n    let { messages, namespace, ...rest } = _ref;\n    // The `namespacePrefix` is part of the type system.\n    // See the comment in the function invocation.\n    messages = messages[namespacePrefix];\n    namespace = createFormatter.resolveNamespace(namespace, namespacePrefix);\n    return createFormatter.createBaseTranslator({\n        ...rest,\n        messages,\n        namespace\n    });\n}\n/**\n * Translates messages from the given namespace by using the ICU syntax.\n * See https://formatjs.io/docs/core-concepts/icu-syntax.\n *\n * If no namespace is provided, all available messages are returned.\n * The namespace can also indicate nesting by using a dot\n * (e.g. `namespace.Component`).\n */ function createTranslator(_ref) {\n    let { _cache = initializeConfig.createCache(), _formatters = initializeConfig.createIntlFormatters(_cache), getMessageFallback = initializeConfig.defaultGetMessageFallback, messages, namespace, onError = initializeConfig.defaultOnError, ...rest } = _ref;\n    // We have to wrap the actual function so the type inference for the optional\n    // namespace works correctly. See https://stackoverflow.com/a/71529575/343045\n    // The prefix (\"!\") is arbitrary.\n    return createTranslatorImpl({\n        ...rest,\n        onError,\n        cache: _cache,\n        formatters: _formatters,\n        getMessageFallback,\n        // @ts-expect-error `messages` is allowed to be `undefined` here and will be handled internally\n        messages: {\n            \"!\": messages\n        },\n        namespace: namespace ? \"!.\".concat(namespace) : \"!\"\n    }, \"!\");\n}\nexports.IntlError = initializeConfig.IntlError;\nexports.IntlErrorCode = initializeConfig.IntlErrorCode;\nexports._createCache = initializeConfig.createCache;\nexports._createIntlFormatters = initializeConfig.createIntlFormatters;\nexports.initializeConfig = initializeConfig.initializeConfig;\nexports.createFormatter = createFormatter.createFormatter;\nexports.createTranslator = createTranslator;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/core.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/createFormatter-QqAaZwGD.js":
/*!****************************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/createFormatter-QqAaZwGD.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar IntlMessageFormat = __webpack_require__(/*! intl-messageformat */ \"(ssr)/./node_modules/intl-messageformat/lib/index.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\nfunction _interopDefault(e) {\n    return e && e.__esModule ? e : {\n        default: e\n    };\n}\nvar IntlMessageFormat__default = /*#__PURE__*/ _interopDefault(IntlMessageFormat);\nfunction setTimeZoneInFormats(formats, timeZone) {\n    if (!formats) return formats;\n    // The only way to set a time zone with `intl-messageformat` is to merge it into the formats\n    // https://github.com/formatjs/formatjs/blob/8256c5271505cf2606e48e3c97ecdd16ede4f1b5/packages/intl/src/message.ts#L15\n    return Object.keys(formats).reduce((acc, key)=>{\n        acc[key] = {\n            timeZone,\n            ...formats[key]\n        };\n        return acc;\n    }, {});\n}\n/**\n * `intl-messageformat` uses separate keys for `date` and `time`, but there's\n * only one native API: `Intl.DateTimeFormat`. Additionally you might want to\n * include both a time and a date in a value, therefore the separation doesn't\n * seem so useful. We offer a single `dateTime` namespace instead, but we have\n * to convert the format before `intl-messageformat` can be used.\n */ function convertFormatsToIntlMessageFormat(formats, timeZone) {\n    const formatsWithTimeZone = timeZone ? {\n        ...formats,\n        dateTime: setTimeZoneInFormats(formats.dateTime, timeZone)\n    } : formats;\n    const mfDateDefaults = IntlMessageFormat__default.default.formats.date;\n    const defaultDateFormats = timeZone ? setTimeZoneInFormats(mfDateDefaults, timeZone) : mfDateDefaults;\n    const mfTimeDefaults = IntlMessageFormat__default.default.formats.time;\n    const defaultTimeFormats = timeZone ? setTimeZoneInFormats(mfTimeDefaults, timeZone) : mfTimeDefaults;\n    return {\n        ...formatsWithTimeZone,\n        date: {\n            ...defaultDateFormats,\n            ...formatsWithTimeZone.dateTime\n        },\n        time: {\n            ...defaultTimeFormats,\n            ...formatsWithTimeZone.dateTime\n        }\n    };\n}\n// Placed here for improved tree shaking. Somehow when this is placed in\n// `formatters.tsx`, then it can't be shaken off from `next-intl`.\nfunction createMessageFormatter(cache, intlFormatters) {\n    const getMessageFormat = initializeConfig.memoFn(function() {\n        return new IntlMessageFormat__default.default(arguments.length <= 0 ? undefined : arguments[0], arguments.length <= 1 ? undefined : arguments[1], arguments.length <= 2 ? undefined : arguments[2], {\n            formatters: intlFormatters,\n            ...arguments.length <= 3 ? undefined : arguments[3]\n        });\n    }, cache.message);\n    return getMessageFormat;\n}\nfunction resolvePath(locale, messages, key, namespace) {\n    const fullKey = initializeConfig.joinPath(namespace, key);\n    if (!messages) {\n        throw new Error(\"No messages available at `\".concat(namespace, \"`.\"));\n    }\n    let message = messages;\n    key.split(\".\").forEach((part)=>{\n        const next = message[part];\n        // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n        if (part == null || next == null) {\n            throw new Error(\"Could not resolve `\".concat(fullKey, \"` in messages for locale `\").concat(locale, \"`.\"));\n        }\n        message = next;\n    });\n    return message;\n}\nfunction prepareTranslationValues(values) {\n    if (Object.keys(values).length === 0) return undefined;\n    // Workaround for https://github.com/formatjs/formatjs/issues/1467\n    const transformedValues = {};\n    Object.keys(values).forEach((key)=>{\n        let index = 0;\n        const value = values[key];\n        let transformed;\n        if (typeof value === \"function\") {\n            transformed = (chunks)=>{\n                const result = value(chunks);\n                return /*#__PURE__*/ React.isValidElement(result) ? /*#__PURE__*/ React.cloneElement(result, {\n                    key: key + index++\n                }) : result;\n            };\n        } else {\n            transformed = value;\n        }\n        transformedValues[key] = transformed;\n    });\n    return transformedValues;\n}\nfunction getMessagesOrError(locale, messages, namespace) {\n    let onError = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : initializeConfig.defaultOnError;\n    try {\n        if (!messages) {\n            throw new Error(\"No messages were configured on the provider.\");\n        }\n        const retrievedMessages = namespace ? resolvePath(locale, messages, namespace) : messages;\n        // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n        if (!retrievedMessages) {\n            throw new Error(\"No messages for namespace `\".concat(namespace, \"` found.\"));\n        }\n        return retrievedMessages;\n    } catch (error) {\n        const intlError = new initializeConfig.IntlError(initializeConfig.IntlErrorCode.MISSING_MESSAGE, error.message);\n        onError(intlError);\n        return intlError;\n    }\n}\nfunction getPlainMessage(candidate, values) {\n    if (values) return undefined;\n    const unescapedMessage = candidate.replace(/'([{}])/gi, \"$1\");\n    // Placeholders can be in the message if there are default values,\n    // or if the user has forgotten to provide values. In the latter\n    // case we need to compile the message to receive an error.\n    const hasPlaceholders = /<|{/.test(unescapedMessage);\n    if (!hasPlaceholders) {\n        return unescapedMessage;\n    }\n    return undefined;\n}\nfunction createBaseTranslator(config) {\n    const messagesOrError = getMessagesOrError(config.locale, config.messages, config.namespace, config.onError);\n    return createBaseTranslatorImpl({\n        ...config,\n        messagesOrError\n    });\n}\nfunction createBaseTranslatorImpl(_ref) {\n    let { cache, defaultTranslationValues, formats: globalFormats, formatters, getMessageFallback = initializeConfig.defaultGetMessageFallback, locale, messagesOrError, namespace, onError, timeZone } = _ref;\n    const hasMessagesError = messagesOrError instanceof initializeConfig.IntlError;\n    function getFallbackFromErrorAndNotify(key, code, message) {\n        const error = new initializeConfig.IntlError(code, message);\n        onError(error);\n        return getMessageFallback({\n            error,\n            key,\n            namespace\n        });\n    }\n    function translateBaseFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */ key, /** Key value pairs for values to interpolate into the message. */ values, /** Provide custom formats for numbers, dates and times. */ formats) {\n        if (hasMessagesError) {\n            // We have already warned about this during render\n            return getMessageFallback({\n                error: messagesOrError,\n                key,\n                namespace\n            });\n        }\n        const messages = messagesOrError;\n        let message;\n        try {\n            message = resolvePath(locale, messages, key, namespace);\n        } catch (error) {\n            return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.MISSING_MESSAGE, error.message);\n        }\n        if (typeof message === \"object\") {\n            let code, errorMessage;\n            if (Array.isArray(message)) {\n                code = initializeConfig.IntlErrorCode.INVALID_MESSAGE;\n                {\n                    errorMessage = \"Message at `\".concat(initializeConfig.joinPath(namespace, key), \"` resolved to an array, but only strings are supported. See https://next-intl.dev/docs/usage/messages#arrays-of-messages\");\n                }\n            } else {\n                code = initializeConfig.IntlErrorCode.INSUFFICIENT_PATH;\n                {\n                    errorMessage = \"Message at `\".concat(initializeConfig.joinPath(namespace, key), \"` resolved to an object, but only strings are supported. Use a `.` to retrieve nested messages. See https://next-intl.dev/docs/usage/messages#structuring-messages\");\n                }\n            }\n            return getFallbackFromErrorAndNotify(key, code, errorMessage);\n        }\n        let messageFormat;\n        // Hot path that avoids creating an `IntlMessageFormat` instance\n        const plainMessage = getPlainMessage(message, values);\n        if (plainMessage) return plainMessage;\n        // Lazy init the message formatter for better tree\n        // shaking in case message formatting is not used.\n        if (!formatters.getMessageFormat) {\n            formatters.getMessageFormat = createMessageFormatter(cache, formatters);\n        }\n        try {\n            messageFormat = formatters.getMessageFormat(message, locale, convertFormatsToIntlMessageFormat({\n                ...globalFormats,\n                ...formats\n            }, timeZone), {\n                formatters: {\n                    ...formatters,\n                    getDateTimeFormat (locales, options) {\n                        // Workaround for https://github.com/formatjs/formatjs/issues/4279\n                        return formatters.getDateTimeFormat(locales, {\n                            timeZone,\n                            ...options\n                        });\n                    }\n                }\n            });\n        } catch (error) {\n            const thrownError = error;\n            return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.INVALID_MESSAGE, thrownError.message + (\"originalMessage\" in thrownError ? \" (\".concat(thrownError.originalMessage, \")\") : \"\"));\n        }\n        try {\n            const formattedMessage = messageFormat.format(// @ts-expect-error `intl-messageformat` expects a different format\n            // for rich text elements since a recent minor update. This\n            // needs to be evaluated in detail, possibly also in regards\n            // to be able to format to parts.\n            prepareTranslationValues({\n                ...defaultTranslationValues,\n                ...values\n            }));\n            if (formattedMessage == null) {\n                throw new Error(\"Unable to format `\".concat(key, \"` in \").concat(namespace ? \"namespace `\".concat(namespace, \"`\") : \"messages\"));\n            }\n            // Limit the function signature to return strings or React elements\n            return /*#__PURE__*/ React.isValidElement(formattedMessage) || // Arrays of React elements\n            Array.isArray(formattedMessage) || typeof formattedMessage === \"string\" ? formattedMessage : String(formattedMessage);\n        } catch (error) {\n            return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.FORMATTING_ERROR, error.message);\n        }\n    }\n    function translateFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */ key, /** Key value pairs for values to interpolate into the message. */ values, /** Provide custom formats for numbers, dates and times. */ formats) {\n        const result = translateBaseFn(key, values, formats);\n        if (typeof result !== \"string\") {\n            return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.INVALID_MESSAGE, \"The message `\".concat(key, \"` in \").concat(namespace ? \"namespace `\".concat(namespace, \"`\") : \"messages\", \" didn't resolve to a string. If you want to format rich text, use `t.rich` instead.\"));\n        }\n        return result;\n    }\n    translateFn.rich = translateBaseFn;\n    // Augment `translateBaseFn` to return plain strings\n    translateFn.markup = (key, values, formats)=>{\n        const result = translateBaseFn(key, // @ts-expect-error -- `MarkupTranslationValues` is practically a sub type\n        // of `RichTranslationValues` but TypeScript isn't smart enough here.\n        values, formats);\n        // When only string chunks are provided to the parser, only\n        // strings should be returned here. Note that we need a runtime\n        // check for this since rich text values could be accidentally\n        // inherited from `defaultTranslationValues`.\n        if (typeof result !== \"string\") {\n            const error = new initializeConfig.IntlError(initializeConfig.IntlErrorCode.FORMATTING_ERROR, \"`t.markup` only accepts functions for formatting that receive and return strings.\\n\\nE.g. t.markup('markup', {b: (chunks) => `<b>${chunks}</b>`})\");\n            onError(error);\n            return getMessageFallback({\n                error,\n                key,\n                namespace\n            });\n        }\n        return result;\n    };\n    translateFn.raw = (key)=>{\n        if (hasMessagesError) {\n            // We have already warned about this during render\n            return getMessageFallback({\n                error: messagesOrError,\n                key,\n                namespace\n            });\n        }\n        const messages = messagesOrError;\n        try {\n            return resolvePath(locale, messages, key, namespace);\n        } catch (error) {\n            return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.MISSING_MESSAGE, error.message);\n        }\n    };\n    translateFn.has = (key)=>{\n        if (hasMessagesError) {\n            return false;\n        }\n        try {\n            resolvePath(locale, messagesOrError, key, namespace);\n            return true;\n        } catch (_unused) {\n            return false;\n        }\n    };\n    return translateFn;\n}\n/**\n * For the strictly typed messages to work we have to wrap the namespace into\n * a mandatory prefix. See https://stackoverflow.com/a/71529575/343045\n */ function resolveNamespace(namespace, namespacePrefix) {\n    return namespace === namespacePrefix ? undefined : namespace.slice((namespacePrefix + \".\").length);\n}\nconst SECOND = 1;\nconst MINUTE = SECOND * 60;\nconst HOUR = MINUTE * 60;\nconst DAY = HOUR * 24;\nconst WEEK = DAY * 7;\nconst MONTH = DAY * (365 / 12); // Approximation\nconst QUARTER = MONTH * 3;\nconst YEAR = DAY * 365;\nconst UNIT_SECONDS = {\n    second: SECOND,\n    seconds: SECOND,\n    minute: MINUTE,\n    minutes: MINUTE,\n    hour: HOUR,\n    hours: HOUR,\n    day: DAY,\n    days: DAY,\n    week: WEEK,\n    weeks: WEEK,\n    month: MONTH,\n    months: MONTH,\n    quarter: QUARTER,\n    quarters: QUARTER,\n    year: YEAR,\n    years: YEAR\n};\nfunction resolveRelativeTimeUnit(seconds) {\n    const absValue = Math.abs(seconds);\n    if (absValue < MINUTE) {\n        return \"second\";\n    } else if (absValue < HOUR) {\n        return \"minute\";\n    } else if (absValue < DAY) {\n        return \"hour\";\n    } else if (absValue < WEEK) {\n        return \"day\";\n    } else if (absValue < MONTH) {\n        return \"week\";\n    } else if (absValue < YEAR) {\n        return \"month\";\n    }\n    return \"year\";\n}\nfunction calculateRelativeTimeValue(seconds, unit) {\n    // We have to round the resulting values, as `Intl.RelativeTimeFormat`\n    // will include fractions like '2.1 hours ago'.\n    return Math.round(seconds / UNIT_SECONDS[unit]);\n}\nfunction createFormatter(_ref) {\n    let { _cache: cache = initializeConfig.createCache(), _formatters: formatters = initializeConfig.createIntlFormatters(cache), formats, locale, now: globalNow, onError = initializeConfig.defaultOnError, timeZone: globalTimeZone } = _ref;\n    function applyTimeZone(options) {\n        var _options;\n        if (!((_options = options) !== null && _options !== void 0 && _options.timeZone)) {\n            if (globalTimeZone) {\n                options = {\n                    ...options,\n                    timeZone: globalTimeZone\n                };\n            } else {\n                onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.ENVIRONMENT_FALLBACK, \"The `timeZone` parameter wasn't provided and there is no global default configured. Consider adding a global default to avoid markup mismatches caused by environment differences. Learn more: https://next-intl.dev/docs/configuration#time-zone\"));\n            }\n        }\n        return options;\n    }\n    function resolveFormatOrOptions(typeFormats, formatOrOptions) {\n        let options;\n        if (typeof formatOrOptions === \"string\") {\n            const formatName = formatOrOptions;\n            options = typeFormats === null || typeFormats === void 0 ? void 0 : typeFormats[formatName];\n            if (!options) {\n                const error = new initializeConfig.IntlError(initializeConfig.IntlErrorCode.MISSING_FORMAT, \"Format `\".concat(formatName, \"` is not available. You can configure it on the provider or provide custom options.\"));\n                onError(error);\n                throw error;\n            }\n        } else {\n            options = formatOrOptions;\n        }\n        return options;\n    }\n    function getFormattedValue(formatOrOptions, typeFormats, formatter, getFallback) {\n        let options;\n        try {\n            options = resolveFormatOrOptions(typeFormats, formatOrOptions);\n        } catch (_unused) {\n            return getFallback();\n        }\n        try {\n            return formatter(options);\n        } catch (error) {\n            onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.FORMATTING_ERROR, error.message));\n            return getFallback();\n        }\n    }\n    function dateTime(/** If a number is supplied, this is interpreted as a UTC timestamp. */ value, /** If a time zone is supplied, the `value` is converted to that time zone.\n   * Otherwise the user time zone will be used. */ formatOrOptions) {\n        return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.dateTime, (options)=>{\n            options = applyTimeZone(options);\n            return formatters.getDateTimeFormat(locale, options).format(value);\n        }, ()=>String(value));\n    }\n    function dateTimeRange(/** If a number is supplied, this is interpreted as a UTC timestamp. */ start, /** If a number is supplied, this is interpreted as a UTC timestamp. */ end, /** If a time zone is supplied, the values are converted to that time zone.\n   * Otherwise the user time zone will be used. */ formatOrOptions) {\n        return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.dateTime, (options)=>{\n            options = applyTimeZone(options);\n            return formatters.getDateTimeFormat(locale, options).formatRange(start, end);\n        }, ()=>[\n                dateTime(start),\n                dateTime(end)\n            ].join(\" – \"));\n    }\n    function number(value, formatOrOptions) {\n        return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.number, (options)=>formatters.getNumberFormat(locale, options).format(value), ()=>String(value));\n    }\n    function getGlobalNow() {\n        if (globalNow) {\n            return globalNow;\n        } else {\n            onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.ENVIRONMENT_FALLBACK, \"The `now` parameter wasn't provided and there is no global default configured. Consider adding a global default to avoid markup mismatches caused by environment differences. Learn more: https://next-intl.dev/docs/configuration#now\"));\n            return new Date();\n        }\n    }\n    function relativeTime(/** The date time that needs to be formatted. */ date, /** The reference point in time to which `date` will be formatted in relation to.  */ nowOrOptions) {\n        try {\n            let nowDate, unit;\n            const opts = {};\n            if (nowOrOptions instanceof Date || typeof nowOrOptions === \"number\") {\n                nowDate = new Date(nowOrOptions);\n            } else if (nowOrOptions) {\n                if (nowOrOptions.now != null) {\n                    nowDate = new Date(nowOrOptions.now);\n                } else {\n                    nowDate = getGlobalNow();\n                }\n                unit = nowOrOptions.unit;\n                opts.style = nowOrOptions.style;\n                // @ts-expect-error -- Types are slightly outdated\n                opts.numberingSystem = nowOrOptions.numberingSystem;\n            }\n            if (!nowDate) {\n                nowDate = getGlobalNow();\n            }\n            const dateDate = new Date(date);\n            const seconds = (dateDate.getTime() - nowDate.getTime()) / 1000;\n            if (!unit) {\n                unit = resolveRelativeTimeUnit(seconds);\n            }\n            // `numeric: 'auto'` can theoretically produce output like \"yesterday\",\n            // but it only works with integers. E.g. -1 day will produce \"yesterday\",\n            // but -1.1 days will produce \"-1.1 days\". Rounding before formatting is\n            // not desired, as the given dates might cross a threshold were the\n            // output isn't correct anymore. Example: 2024-01-08T23:00:00.000Z and\n            // 2024-01-08T01:00:00.000Z would produce \"yesterday\", which is not the\n            // case. By using `always` we can ensure correct output. The only exception\n            // is the formatting of times <1 second as \"now\".\n            opts.numeric = unit === \"second\" ? \"auto\" : \"always\";\n            const value = calculateRelativeTimeValue(seconds, unit);\n            return formatters.getRelativeTimeFormat(locale, opts).format(value, unit);\n        } catch (error) {\n            onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.FORMATTING_ERROR, error.message));\n            return String(date);\n        }\n    }\n    function list(value, formatOrOptions) {\n        const serializedValue = [];\n        const richValues = new Map();\n        // `formatToParts` only accepts strings, therefore we have to temporarily\n        // replace React elements with a placeholder ID that can be used to retrieve\n        // the original value afterwards.\n        let index = 0;\n        for (const item of value){\n            let serializedItem;\n            if (typeof item === \"object\") {\n                serializedItem = String(index);\n                richValues.set(serializedItem, item);\n            } else {\n                serializedItem = String(item);\n            }\n            serializedValue.push(serializedItem);\n            index++;\n        }\n        return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.list, // @ts-expect-error -- `richValues.size` is used to determine the return type, but TypeScript can't infer the meaning of this correctly\n        (options)=>{\n            const result = formatters.getListFormat(locale, options).formatToParts(serializedValue).map((part)=>part.type === \"literal\" ? part.value : richValues.get(part.value) || part.value);\n            if (richValues.size > 0) {\n                return result;\n            } else {\n                return result.join(\"\");\n            }\n        }, ()=>String(value));\n    }\n    return {\n        dateTime,\n        number,\n        relativeTime,\n        list,\n        dateTimeRange\n    };\n}\nexports.createBaseTranslator = createBaseTranslator;\nexports.createFormatter = createFormatter;\nexports.resolveNamespace = resolveNamespace;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/createFormatter-QqAaZwGD.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/use-intl/dist/development/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\nvar core = __webpack_require__(/*! ./core.js */ \"(ssr)/./node_modules/use-intl/dist/development/core.js\");\nvar createFormatter = __webpack_require__(/*! ./createFormatter-QqAaZwGD.js */ \"(ssr)/./node_modules/use-intl/dist/development/createFormatter-QqAaZwGD.js\");\nvar _IntlProvider = __webpack_require__(/*! ./_IntlProvider.js */ \"(ssr)/./node_modules/use-intl/dist/development/_IntlProvider.js\");\nvar react = __webpack_require__(/*! ./react.js */ \"(ssr)/./node_modules/use-intl/dist/development/react.js\");\nvar _useLocale = __webpack_require__(/*! ./_useLocale-BK3jOeaA.js */ \"(ssr)/./node_modules/use-intl/dist/development/_useLocale-BK3jOeaA.js\");\n__webpack_require__(/*! @formatjs/fast-memoize */ \"(ssr)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n__webpack_require__(/*! intl-messageformat */ \"(ssr)/./node_modules/intl-messageformat/lib/index.js\");\n__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n__webpack_require__(/*! ./IntlContext-BKfsnzBx.js */ \"(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\");\nexports.IntlError = initializeConfig.IntlError;\nexports.IntlErrorCode = initializeConfig.IntlErrorCode;\nexports._createCache = initializeConfig.createCache;\nexports._createIntlFormatters = initializeConfig.createIntlFormatters;\nexports.initializeConfig = initializeConfig.initializeConfig;\nexports.createTranslator = core.createTranslator;\nexports.createFormatter = createFormatter.createFormatter;\nexports.IntlProvider = _IntlProvider.IntlProvider;\nexports.useFormatter = react.useFormatter;\nexports.useMessages = react.useMessages;\nexports.useNow = react.useNow;\nexports.useTimeZone = react.useTimeZone;\nexports.useTranslations = react.useTranslations;\nexports.useLocale = _useLocale.useLocale;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar fastMemoize = __webpack_require__(/*! @formatjs/fast-memoize */ \"(ssr)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\nfunction _defineProperty(e, r, t) {\n    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n        value: t,\n        enumerable: !0,\n        configurable: !0,\n        writable: !0\n    }) : e[r] = t, e;\n}\nfunction _toPrimitive(t, r) {\n    if (\"object\" != typeof t || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n        var i = e.call(t, r || \"default\");\n        if (\"object\" != typeof i) return i;\n        throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == typeof i ? i : i + \"\";\n}\nlet IntlErrorCode = /*#__PURE__*/ function(IntlErrorCode) {\n    IntlErrorCode[\"MISSING_MESSAGE\"] = \"MISSING_MESSAGE\";\n    IntlErrorCode[\"MISSING_FORMAT\"] = \"MISSING_FORMAT\";\n    IntlErrorCode[\"ENVIRONMENT_FALLBACK\"] = \"ENVIRONMENT_FALLBACK\";\n    IntlErrorCode[\"INSUFFICIENT_PATH\"] = \"INSUFFICIENT_PATH\";\n    IntlErrorCode[\"INVALID_MESSAGE\"] = \"INVALID_MESSAGE\";\n    IntlErrorCode[\"INVALID_KEY\"] = \"INVALID_KEY\";\n    IntlErrorCode[\"FORMATTING_ERROR\"] = \"FORMATTING_ERROR\";\n    return IntlErrorCode;\n}({});\nclass IntlError extends Error {\n    constructor(code, originalMessage){\n        let message = code;\n        if (originalMessage) {\n            message += \": \" + originalMessage;\n        }\n        super(message);\n        _defineProperty(this, \"code\", void 0);\n        _defineProperty(this, \"originalMessage\", void 0);\n        this.code = code;\n        if (originalMessage) {\n            this.originalMessage = originalMessage;\n        }\n    }\n}\nfunction joinPath() {\n    for(var _len = arguments.length, parts = new Array(_len), _key = 0; _key < _len; _key++){\n        parts[_key] = arguments[_key];\n    }\n    return parts.filter(Boolean).join(\".\");\n}\n/**\n * Contains defaults that are used for all entry points into the core.\n * See also `InitializedIntlConfiguration`.\n */ function defaultGetMessageFallback(props) {\n    return joinPath(props.namespace, props.key);\n}\nfunction defaultOnError(error) {\n    console.error(error);\n}\nfunction createCache() {\n    return {\n        dateTime: {},\n        number: {},\n        message: {},\n        relativeTime: {},\n        pluralRules: {},\n        list: {},\n        displayNames: {}\n    };\n}\nfunction createMemoCache(store) {\n    return {\n        create () {\n            return {\n                get (key) {\n                    return store[key];\n                },\n                set (key, value) {\n                    store[key] = value;\n                }\n            };\n        }\n    };\n}\nfunction memoFn(fn, cache) {\n    return fastMemoize.memoize(fn, {\n        cache: createMemoCache(cache),\n        strategy: fastMemoize.strategies.variadic\n    });\n}\nfunction memoConstructor(ConstructorFn, cache) {\n    return memoFn(function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        return new ConstructorFn(...args);\n    }, cache);\n}\nfunction createIntlFormatters(cache) {\n    const getDateTimeFormat = memoConstructor(Intl.DateTimeFormat, cache.dateTime);\n    const getNumberFormat = memoConstructor(Intl.NumberFormat, cache.number);\n    const getPluralRules = memoConstructor(Intl.PluralRules, cache.pluralRules);\n    const getRelativeTimeFormat = memoConstructor(Intl.RelativeTimeFormat, cache.relativeTime);\n    const getListFormat = memoConstructor(Intl.ListFormat, cache.list);\n    const getDisplayNames = memoConstructor(Intl.DisplayNames, cache.displayNames);\n    return {\n        getDateTimeFormat,\n        getNumberFormat,\n        getPluralRules,\n        getRelativeTimeFormat,\n        getListFormat,\n        getDisplayNames\n    };\n}\nfunction validateMessagesSegment(messages, invalidKeyLabels, parentPath) {\n    Object.entries(messages).forEach((_ref)=>{\n        let [key, messageOrMessages] = _ref;\n        if (key.includes(\".\")) {\n            let keyLabel = key;\n            if (parentPath) keyLabel += \" (at \".concat(parentPath, \")\");\n            invalidKeyLabels.push(keyLabel);\n        }\n        // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n        if (messageOrMessages != null && typeof messageOrMessages === \"object\") {\n            validateMessagesSegment(messageOrMessages, invalidKeyLabels, joinPath(parentPath, key));\n        }\n    });\n}\nfunction validateMessages(messages, onError) {\n    const invalidKeyLabels = [];\n    validateMessagesSegment(messages, invalidKeyLabels);\n    if (invalidKeyLabels.length > 0) {\n        onError(new IntlError(IntlErrorCode.INVALID_KEY, 'Namespace keys can not contain the character \".\" as this is used to express nesting. Please remove it or replace it with another character.\\n\\nInvalid '.concat(invalidKeyLabels.length === 1 ? \"key\" : \"keys\", \": \").concat(invalidKeyLabels.join(\", \"), '\\n\\nIf you\\'re migrating from a flat structure, you can convert your messages as follows:\\n\\nimport {set} from \"lodash\";\\n\\nconst input = {\\n  \"one.one\": \"1.1\",\\n  \"one.two\": \"1.2\",\\n  \"two.one.one\": \"2.1.1\"\\n};\\n\\nconst output = Object.entries(input).reduce(\\n  (acc, [key, value]) => set(acc, key, value),\\n  {}\\n);\\n\\n// Output:\\n//\\n// {\\n//   \"one\": {\\n//     \"one\": \"1.1\",\\n//     \"two\": \"1.2\"\\n//   },\\n//   \"two\": {\\n//     \"one\": {\\n//       \"one\": \"2.1.1\"\\n//     }\\n//   }\\n// }\\n')));\n    }\n}\n/**\n * Enhances the incoming props with defaults.\n */ function initializeConfig(_ref) {\n    let { getMessageFallback, messages, onError, ...rest } = _ref;\n    const finalOnError = onError || defaultOnError;\n    const finalGetMessageFallback = getMessageFallback || defaultGetMessageFallback;\n    {\n        if (messages) {\n            validateMessages(messages, finalOnError);\n        }\n    }\n    return {\n        ...rest,\n        messages,\n        onError: finalOnError,\n        getMessageFallback: finalGetMessageFallback\n    };\n}\nexports.IntlError = IntlError;\nexports.IntlErrorCode = IntlErrorCode;\nexports.createCache = createCache;\nexports.createIntlFormatters = createIntlFormatters;\nexports.defaultGetMessageFallback = defaultGetMessageFallback;\nexports.defaultOnError = defaultOnError;\nexports.initializeConfig = initializeConfig;\nexports.joinPath = joinPath;\nexports.memoFn = memoFn;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/react.js":
/*!*********************************************************!*\
  !*** ./node_modules/use-intl/dist/development/react.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _IntlProvider = __webpack_require__(/*! ./_IntlProvider.js */ \"(ssr)/./node_modules/use-intl/dist/development/_IntlProvider.js\");\nvar _useLocale = __webpack_require__(/*! ./_useLocale-BK3jOeaA.js */ \"(ssr)/./node_modules/use-intl/dist/development/_useLocale-BK3jOeaA.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar createFormatter = __webpack_require__(/*! ./createFormatter-QqAaZwGD.js */ \"(ssr)/./node_modules/use-intl/dist/development/createFormatter-QqAaZwGD.js\");\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\n__webpack_require__(/*! ./IntlContext-BKfsnzBx.js */ \"(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\");\n__webpack_require__(/*! intl-messageformat */ \"(ssr)/./node_modules/intl-messageformat/lib/index.js\");\n__webpack_require__(/*! @formatjs/fast-memoize */ \"(ssr)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\nlet hasWarnedForMissingTimezone = false;\nconst isServer = \"undefined\" === \"undefined\";\nfunction useTranslationsImpl(allMessagesPrefixed, namespacePrefixed, namespacePrefix) {\n    const { cache, defaultTranslationValues, formats: globalFormats, formatters, getMessageFallback, locale, onError, timeZone } = _useLocale.useIntlContext();\n    // The `namespacePrefix` is part of the type system.\n    // See the comment in the hook invocation.\n    const allMessages = allMessagesPrefixed[namespacePrefix];\n    const namespace = createFormatter.resolveNamespace(namespacePrefixed, namespacePrefix);\n    if (!timeZone && !hasWarnedForMissingTimezone && isServer) {\n        // eslint-disable-next-line react-compiler/react-compiler\n        hasWarnedForMissingTimezone = true;\n        onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.ENVIRONMENT_FALLBACK, \"There is no `timeZone` configured, this can lead to markup mismatches caused by environment differences. Consider adding a global default: https://next-intl.dev/docs/configuration#time-zone\"));\n    }\n    const translate = React.useMemo(()=>createFormatter.createBaseTranslator({\n            cache,\n            formatters,\n            getMessageFallback,\n            messages: allMessages,\n            defaultTranslationValues,\n            namespace,\n            onError,\n            formats: globalFormats,\n            locale,\n            timeZone\n        }), [\n        cache,\n        formatters,\n        getMessageFallback,\n        allMessages,\n        defaultTranslationValues,\n        namespace,\n        onError,\n        globalFormats,\n        locale,\n        timeZone\n    ]);\n    return translate;\n}\n/**\n * Translates messages from the given namespace by using the ICU syntax.\n * See https://formatjs.io/docs/core-concepts/icu-syntax.\n *\n * If no namespace is provided, all available messages are returned.\n * The namespace can also indicate nesting by using a dot\n * (e.g. `namespace.Component`).\n */ function useTranslations(namespace) {\n    const context = _useLocale.useIntlContext();\n    const messages = context.messages;\n    // We have to wrap the actual hook so the type inference for the optional\n    // namespace works correctly. See https://stackoverflow.com/a/71529575/343045\n    // The prefix (\"!\") is arbitrary.\n    return useTranslationsImpl({\n        \"!\": messages\n    }, // @ts-expect-error\n    namespace ? \"!.\".concat(namespace) : \"!\", \"!\");\n}\nfunction getNow() {\n    return new Date();\n}\n/**\n * Reading the current date via `new Date()` in components should be avoided, as\n * it causes components to be impure and can lead to flaky tests. Instead, this\n * hook can be used.\n *\n * By default, it returns the time when the component mounts. If `updateInterval`\n * is specified, the value will be updated based on the interval.\n *\n * You can however also return a static value from this hook, if you\n * configure the `now` parameter on the context provider. Note however,\n * that if `updateInterval` is configured in this case, the component\n * will initialize with the global value, but will afterwards update\n * continuously based on the interval.\n *\n * For unit tests, this can be mocked to a constant value. For end-to-end\n * testing, an environment parameter can be passed to the `now` parameter\n * of the provider to mock this to a static value.\n */ function useNow(options) {\n    const updateInterval = options === null || options === void 0 ? void 0 : options.updateInterval;\n    const { now: globalNow } = _useLocale.useIntlContext();\n    const [now, setNow] = React.useState(globalNow || getNow());\n    React.useEffect(()=>{\n        if (!updateInterval) return;\n        const intervalId = setInterval(()=>{\n            setNow(getNow());\n        }, updateInterval);\n        return ()=>{\n            clearInterval(intervalId);\n        };\n    }, [\n        globalNow,\n        updateInterval\n    ]);\n    return updateInterval == null && globalNow ? globalNow : now;\n}\nfunction useTimeZone() {\n    return _useLocale.useIntlContext().timeZone;\n}\nfunction useMessages() {\n    const context = _useLocale.useIntlContext();\n    if (!context.messages) {\n        throw new Error(\"No messages found. Have you configured them correctly? See https://next-intl.dev/docs/configuration#messages\");\n    }\n    return context.messages;\n}\nfunction useFormatter() {\n    const { formats, formatters, locale, now: globalNow, onError, timeZone } = _useLocale.useIntlContext();\n    return React.useMemo(()=>createFormatter.createFormatter({\n            formats,\n            locale,\n            now: globalNow,\n            onError,\n            timeZone,\n            _formatters: formatters\n        }), [\n        formats,\n        formatters,\n        globalNow,\n        locale,\n        onError,\n        timeZone\n    ]);\n}\nexports.IntlProvider = _IntlProvider.IntlProvider;\nexports.useLocale = _useLocale.useLocale;\nexports.useFormatter = useFormatter;\nexports.useMessages = useMessages;\nexports.useNow = useNow;\nexports.useTimeZone = useTimeZone;\nexports.useTranslations = useTranslations;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/react.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/index.js":
/*!*********************************************!*\
  !*** ./node_modules/use-intl/dist/index.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nif (false) {} else {\n    module.exports = __webpack_require__(/*! ./development/index.js */ \"(ssr)/./node_modules/use-intl/dist/development/index.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUVBLElBQUlBLEtBQXlCLEVBQWMsRUFFMUMsTUFBTTtJQUNMQyw2SEFBeUI7QUFDM0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zbWUtYW5hbHl0aWNhLWRvY3MvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9pbmRleC5qcz83YjExIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL3Byb2R1Y3Rpb24vaW5kZXguanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9kZXZlbG9wbWVudC9pbmRleC5qcycpO1xufVxuIl0sIm5hbWVzIjpbInByb2Nlc3MiLCJtb2R1bGUiLCJleHBvcnRzIiwicmVxdWlyZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/use-intl/dist/development/core.js":
/*!********************************************************!*\
  !*** ./node_modules/use-intl/dist/development/core.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(rsc)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\nvar createFormatter = __webpack_require__(/*! ./createFormatter-QqAaZwGD.js */ \"(rsc)/./node_modules/use-intl/dist/development/createFormatter-QqAaZwGD.js\");\n__webpack_require__(/*! @formatjs/fast-memoize */ \"(rsc)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n__webpack_require__(/*! intl-messageformat */ \"(rsc)/./node_modules/intl-messageformat/lib/index.js\");\n__webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\nfunction createTranslatorImpl(_ref, namespacePrefix) {\n    let { messages, namespace, ...rest } = _ref;\n    // The `namespacePrefix` is part of the type system.\n    // See the comment in the function invocation.\n    messages = messages[namespacePrefix];\n    namespace = createFormatter.resolveNamespace(namespace, namespacePrefix);\n    return createFormatter.createBaseTranslator({\n        ...rest,\n        messages,\n        namespace\n    });\n}\n/**\n * Translates messages from the given namespace by using the ICU syntax.\n * See https://formatjs.io/docs/core-concepts/icu-syntax.\n *\n * If no namespace is provided, all available messages are returned.\n * The namespace can also indicate nesting by using a dot\n * (e.g. `namespace.Component`).\n */ function createTranslator(_ref) {\n    let { _cache = initializeConfig.createCache(), _formatters = initializeConfig.createIntlFormatters(_cache), getMessageFallback = initializeConfig.defaultGetMessageFallback, messages, namespace, onError = initializeConfig.defaultOnError, ...rest } = _ref;\n    // We have to wrap the actual function so the type inference for the optional\n    // namespace works correctly. See https://stackoverflow.com/a/71529575/343045\n    // The prefix (\"!\") is arbitrary.\n    return createTranslatorImpl({\n        ...rest,\n        onError,\n        cache: _cache,\n        formatters: _formatters,\n        getMessageFallback,\n        // @ts-expect-error `messages` is allowed to be `undefined` here and will be handled internally\n        messages: {\n            \"!\": messages\n        },\n        namespace: namespace ? \"!.\".concat(namespace) : \"!\"\n    }, \"!\");\n}\nexports.IntlError = initializeConfig.IntlError;\nexports.IntlErrorCode = initializeConfig.IntlErrorCode;\nexports._createCache = initializeConfig.createCache;\nexports._createIntlFormatters = initializeConfig.createIntlFormatters;\nexports.initializeConfig = initializeConfig.initializeConfig;\nexports.createFormatter = createFormatter.createFormatter;\nexports.createTranslator = createTranslator;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/use-intl/dist/development/core.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/use-intl/dist/development/createFormatter-QqAaZwGD.js":
/*!****************************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/createFormatter-QqAaZwGD.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar IntlMessageFormat = __webpack_require__(/*! intl-messageformat */ \"(rsc)/./node_modules/intl-messageformat/lib/index.js\");\nvar React = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(rsc)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\nfunction _interopDefault(e) {\n    return e && e.__esModule ? e : {\n        default: e\n    };\n}\nvar IntlMessageFormat__default = /*#__PURE__*/ _interopDefault(IntlMessageFormat);\nfunction setTimeZoneInFormats(formats, timeZone) {\n    if (!formats) return formats;\n    // The only way to set a time zone with `intl-messageformat` is to merge it into the formats\n    // https://github.com/formatjs/formatjs/blob/8256c5271505cf2606e48e3c97ecdd16ede4f1b5/packages/intl/src/message.ts#L15\n    return Object.keys(formats).reduce((acc, key)=>{\n        acc[key] = {\n            timeZone,\n            ...formats[key]\n        };\n        return acc;\n    }, {});\n}\n/**\n * `intl-messageformat` uses separate keys for `date` and `time`, but there's\n * only one native API: `Intl.DateTimeFormat`. Additionally you might want to\n * include both a time and a date in a value, therefore the separation doesn't\n * seem so useful. We offer a single `dateTime` namespace instead, but we have\n * to convert the format before `intl-messageformat` can be used.\n */ function convertFormatsToIntlMessageFormat(formats, timeZone) {\n    const formatsWithTimeZone = timeZone ? {\n        ...formats,\n        dateTime: setTimeZoneInFormats(formats.dateTime, timeZone)\n    } : formats;\n    const mfDateDefaults = IntlMessageFormat__default.default.formats.date;\n    const defaultDateFormats = timeZone ? setTimeZoneInFormats(mfDateDefaults, timeZone) : mfDateDefaults;\n    const mfTimeDefaults = IntlMessageFormat__default.default.formats.time;\n    const defaultTimeFormats = timeZone ? setTimeZoneInFormats(mfTimeDefaults, timeZone) : mfTimeDefaults;\n    return {\n        ...formatsWithTimeZone,\n        date: {\n            ...defaultDateFormats,\n            ...formatsWithTimeZone.dateTime\n        },\n        time: {\n            ...defaultTimeFormats,\n            ...formatsWithTimeZone.dateTime\n        }\n    };\n}\n// Placed here for improved tree shaking. Somehow when this is placed in\n// `formatters.tsx`, then it can't be shaken off from `next-intl`.\nfunction createMessageFormatter(cache, intlFormatters) {\n    const getMessageFormat = initializeConfig.memoFn(function() {\n        return new IntlMessageFormat__default.default(arguments.length <= 0 ? undefined : arguments[0], arguments.length <= 1 ? undefined : arguments[1], arguments.length <= 2 ? undefined : arguments[2], {\n            formatters: intlFormatters,\n            ...arguments.length <= 3 ? undefined : arguments[3]\n        });\n    }, cache.message);\n    return getMessageFormat;\n}\nfunction resolvePath(locale, messages, key, namespace) {\n    const fullKey = initializeConfig.joinPath(namespace, key);\n    if (!messages) {\n        throw new Error(\"No messages available at `\".concat(namespace, \"`.\"));\n    }\n    let message = messages;\n    key.split(\".\").forEach((part)=>{\n        const next = message[part];\n        // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n        if (part == null || next == null) {\n            throw new Error(\"Could not resolve `\".concat(fullKey, \"` in messages for locale `\").concat(locale, \"`.\"));\n        }\n        message = next;\n    });\n    return message;\n}\nfunction prepareTranslationValues(values) {\n    if (Object.keys(values).length === 0) return undefined;\n    // Workaround for https://github.com/formatjs/formatjs/issues/1467\n    const transformedValues = {};\n    Object.keys(values).forEach((key)=>{\n        let index = 0;\n        const value = values[key];\n        let transformed;\n        if (typeof value === \"function\") {\n            transformed = (chunks)=>{\n                const result = value(chunks);\n                return /*#__PURE__*/ React.isValidElement(result) ? /*#__PURE__*/ React.cloneElement(result, {\n                    key: key + index++\n                }) : result;\n            };\n        } else {\n            transformed = value;\n        }\n        transformedValues[key] = transformed;\n    });\n    return transformedValues;\n}\nfunction getMessagesOrError(locale, messages, namespace) {\n    let onError = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : initializeConfig.defaultOnError;\n    try {\n        if (!messages) {\n            throw new Error(\"No messages were configured on the provider.\");\n        }\n        const retrievedMessages = namespace ? resolvePath(locale, messages, namespace) : messages;\n        // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n        if (!retrievedMessages) {\n            throw new Error(\"No messages for namespace `\".concat(namespace, \"` found.\"));\n        }\n        return retrievedMessages;\n    } catch (error) {\n        const intlError = new initializeConfig.IntlError(initializeConfig.IntlErrorCode.MISSING_MESSAGE, error.message);\n        onError(intlError);\n        return intlError;\n    }\n}\nfunction getPlainMessage(candidate, values) {\n    if (values) return undefined;\n    const unescapedMessage = candidate.replace(/'([{}])/gi, \"$1\");\n    // Placeholders can be in the message if there are default values,\n    // or if the user has forgotten to provide values. In the latter\n    // case we need to compile the message to receive an error.\n    const hasPlaceholders = /<|{/.test(unescapedMessage);\n    if (!hasPlaceholders) {\n        return unescapedMessage;\n    }\n    return undefined;\n}\nfunction createBaseTranslator(config) {\n    const messagesOrError = getMessagesOrError(config.locale, config.messages, config.namespace, config.onError);\n    return createBaseTranslatorImpl({\n        ...config,\n        messagesOrError\n    });\n}\nfunction createBaseTranslatorImpl(_ref) {\n    let { cache, defaultTranslationValues, formats: globalFormats, formatters, getMessageFallback = initializeConfig.defaultGetMessageFallback, locale, messagesOrError, namespace, onError, timeZone } = _ref;\n    const hasMessagesError = messagesOrError instanceof initializeConfig.IntlError;\n    function getFallbackFromErrorAndNotify(key, code, message) {\n        const error = new initializeConfig.IntlError(code, message);\n        onError(error);\n        return getMessageFallback({\n            error,\n            key,\n            namespace\n        });\n    }\n    function translateBaseFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */ key, /** Key value pairs for values to interpolate into the message. */ values, /** Provide custom formats for numbers, dates and times. */ formats) {\n        if (hasMessagesError) {\n            // We have already warned about this during render\n            return getMessageFallback({\n                error: messagesOrError,\n                key,\n                namespace\n            });\n        }\n        const messages = messagesOrError;\n        let message;\n        try {\n            message = resolvePath(locale, messages, key, namespace);\n        } catch (error) {\n            return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.MISSING_MESSAGE, error.message);\n        }\n        if (typeof message === \"object\") {\n            let code, errorMessage;\n            if (Array.isArray(message)) {\n                code = initializeConfig.IntlErrorCode.INVALID_MESSAGE;\n                {\n                    errorMessage = \"Message at `\".concat(initializeConfig.joinPath(namespace, key), \"` resolved to an array, but only strings are supported. See https://next-intl.dev/docs/usage/messages#arrays-of-messages\");\n                }\n            } else {\n                code = initializeConfig.IntlErrorCode.INSUFFICIENT_PATH;\n                {\n                    errorMessage = \"Message at `\".concat(initializeConfig.joinPath(namespace, key), \"` resolved to an object, but only strings are supported. Use a `.` to retrieve nested messages. See https://next-intl.dev/docs/usage/messages#structuring-messages\");\n                }\n            }\n            return getFallbackFromErrorAndNotify(key, code, errorMessage);\n        }\n        let messageFormat;\n        // Hot path that avoids creating an `IntlMessageFormat` instance\n        const plainMessage = getPlainMessage(message, values);\n        if (plainMessage) return plainMessage;\n        // Lazy init the message formatter for better tree\n        // shaking in case message formatting is not used.\n        if (!formatters.getMessageFormat) {\n            formatters.getMessageFormat = createMessageFormatter(cache, formatters);\n        }\n        try {\n            messageFormat = formatters.getMessageFormat(message, locale, convertFormatsToIntlMessageFormat({\n                ...globalFormats,\n                ...formats\n            }, timeZone), {\n                formatters: {\n                    ...formatters,\n                    getDateTimeFormat (locales, options) {\n                        // Workaround for https://github.com/formatjs/formatjs/issues/4279\n                        return formatters.getDateTimeFormat(locales, {\n                            timeZone,\n                            ...options\n                        });\n                    }\n                }\n            });\n        } catch (error) {\n            const thrownError = error;\n            return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.INVALID_MESSAGE, thrownError.message + (\"originalMessage\" in thrownError ? \" (\".concat(thrownError.originalMessage, \")\") : \"\"));\n        }\n        try {\n            const formattedMessage = messageFormat.format(// @ts-expect-error `intl-messageformat` expects a different format\n            // for rich text elements since a recent minor update. This\n            // needs to be evaluated in detail, possibly also in regards\n            // to be able to format to parts.\n            prepareTranslationValues({\n                ...defaultTranslationValues,\n                ...values\n            }));\n            if (formattedMessage == null) {\n                throw new Error(\"Unable to format `\".concat(key, \"` in \").concat(namespace ? \"namespace `\".concat(namespace, \"`\") : \"messages\"));\n            }\n            // Limit the function signature to return strings or React elements\n            return /*#__PURE__*/ React.isValidElement(formattedMessage) || // Arrays of React elements\n            Array.isArray(formattedMessage) || typeof formattedMessage === \"string\" ? formattedMessage : String(formattedMessage);\n        } catch (error) {\n            return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.FORMATTING_ERROR, error.message);\n        }\n    }\n    function translateFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */ key, /** Key value pairs for values to interpolate into the message. */ values, /** Provide custom formats for numbers, dates and times. */ formats) {\n        const result = translateBaseFn(key, values, formats);\n        if (typeof result !== \"string\") {\n            return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.INVALID_MESSAGE, \"The message `\".concat(key, \"` in \").concat(namespace ? \"namespace `\".concat(namespace, \"`\") : \"messages\", \" didn't resolve to a string. If you want to format rich text, use `t.rich` instead.\"));\n        }\n        return result;\n    }\n    translateFn.rich = translateBaseFn;\n    // Augment `translateBaseFn` to return plain strings\n    translateFn.markup = (key, values, formats)=>{\n        const result = translateBaseFn(key, // @ts-expect-error -- `MarkupTranslationValues` is practically a sub type\n        // of `RichTranslationValues` but TypeScript isn't smart enough here.\n        values, formats);\n        // When only string chunks are provided to the parser, only\n        // strings should be returned here. Note that we need a runtime\n        // check for this since rich text values could be accidentally\n        // inherited from `defaultTranslationValues`.\n        if (typeof result !== \"string\") {\n            const error = new initializeConfig.IntlError(initializeConfig.IntlErrorCode.FORMATTING_ERROR, \"`t.markup` only accepts functions for formatting that receive and return strings.\\n\\nE.g. t.markup('markup', {b: (chunks) => `<b>${chunks}</b>`})\");\n            onError(error);\n            return getMessageFallback({\n                error,\n                key,\n                namespace\n            });\n        }\n        return result;\n    };\n    translateFn.raw = (key)=>{\n        if (hasMessagesError) {\n            // We have already warned about this during render\n            return getMessageFallback({\n                error: messagesOrError,\n                key,\n                namespace\n            });\n        }\n        const messages = messagesOrError;\n        try {\n            return resolvePath(locale, messages, key, namespace);\n        } catch (error) {\n            return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.MISSING_MESSAGE, error.message);\n        }\n    };\n    translateFn.has = (key)=>{\n        if (hasMessagesError) {\n            return false;\n        }\n        try {\n            resolvePath(locale, messagesOrError, key, namespace);\n            return true;\n        } catch (_unused) {\n            return false;\n        }\n    };\n    return translateFn;\n}\n/**\n * For the strictly typed messages to work we have to wrap the namespace into\n * a mandatory prefix. See https://stackoverflow.com/a/71529575/343045\n */ function resolveNamespace(namespace, namespacePrefix) {\n    return namespace === namespacePrefix ? undefined : namespace.slice((namespacePrefix + \".\").length);\n}\nconst SECOND = 1;\nconst MINUTE = SECOND * 60;\nconst HOUR = MINUTE * 60;\nconst DAY = HOUR * 24;\nconst WEEK = DAY * 7;\nconst MONTH = DAY * (365 / 12); // Approximation\nconst QUARTER = MONTH * 3;\nconst YEAR = DAY * 365;\nconst UNIT_SECONDS = {\n    second: SECOND,\n    seconds: SECOND,\n    minute: MINUTE,\n    minutes: MINUTE,\n    hour: HOUR,\n    hours: HOUR,\n    day: DAY,\n    days: DAY,\n    week: WEEK,\n    weeks: WEEK,\n    month: MONTH,\n    months: MONTH,\n    quarter: QUARTER,\n    quarters: QUARTER,\n    year: YEAR,\n    years: YEAR\n};\nfunction resolveRelativeTimeUnit(seconds) {\n    const absValue = Math.abs(seconds);\n    if (absValue < MINUTE) {\n        return \"second\";\n    } else if (absValue < HOUR) {\n        return \"minute\";\n    } else if (absValue < DAY) {\n        return \"hour\";\n    } else if (absValue < WEEK) {\n        return \"day\";\n    } else if (absValue < MONTH) {\n        return \"week\";\n    } else if (absValue < YEAR) {\n        return \"month\";\n    }\n    return \"year\";\n}\nfunction calculateRelativeTimeValue(seconds, unit) {\n    // We have to round the resulting values, as `Intl.RelativeTimeFormat`\n    // will include fractions like '2.1 hours ago'.\n    return Math.round(seconds / UNIT_SECONDS[unit]);\n}\nfunction createFormatter(_ref) {\n    let { _cache: cache = initializeConfig.createCache(), _formatters: formatters = initializeConfig.createIntlFormatters(cache), formats, locale, now: globalNow, onError = initializeConfig.defaultOnError, timeZone: globalTimeZone } = _ref;\n    function applyTimeZone(options) {\n        var _options;\n        if (!((_options = options) !== null && _options !== void 0 && _options.timeZone)) {\n            if (globalTimeZone) {\n                options = {\n                    ...options,\n                    timeZone: globalTimeZone\n                };\n            } else {\n                onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.ENVIRONMENT_FALLBACK, \"The `timeZone` parameter wasn't provided and there is no global default configured. Consider adding a global default to avoid markup mismatches caused by environment differences. Learn more: https://next-intl.dev/docs/configuration#time-zone\"));\n            }\n        }\n        return options;\n    }\n    function resolveFormatOrOptions(typeFormats, formatOrOptions) {\n        let options;\n        if (typeof formatOrOptions === \"string\") {\n            const formatName = formatOrOptions;\n            options = typeFormats === null || typeFormats === void 0 ? void 0 : typeFormats[formatName];\n            if (!options) {\n                const error = new initializeConfig.IntlError(initializeConfig.IntlErrorCode.MISSING_FORMAT, \"Format `\".concat(formatName, \"` is not available. You can configure it on the provider or provide custom options.\"));\n                onError(error);\n                throw error;\n            }\n        } else {\n            options = formatOrOptions;\n        }\n        return options;\n    }\n    function getFormattedValue(formatOrOptions, typeFormats, formatter, getFallback) {\n        let options;\n        try {\n            options = resolveFormatOrOptions(typeFormats, formatOrOptions);\n        } catch (_unused) {\n            return getFallback();\n        }\n        try {\n            return formatter(options);\n        } catch (error) {\n            onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.FORMATTING_ERROR, error.message));\n            return getFallback();\n        }\n    }\n    function dateTime(/** If a number is supplied, this is interpreted as a UTC timestamp. */ value, /** If a time zone is supplied, the `value` is converted to that time zone.\n   * Otherwise the user time zone will be used. */ formatOrOptions) {\n        return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.dateTime, (options)=>{\n            options = applyTimeZone(options);\n            return formatters.getDateTimeFormat(locale, options).format(value);\n        }, ()=>String(value));\n    }\n    function dateTimeRange(/** If a number is supplied, this is interpreted as a UTC timestamp. */ start, /** If a number is supplied, this is interpreted as a UTC timestamp. */ end, /** If a time zone is supplied, the values are converted to that time zone.\n   * Otherwise the user time zone will be used. */ formatOrOptions) {\n        return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.dateTime, (options)=>{\n            options = applyTimeZone(options);\n            return formatters.getDateTimeFormat(locale, options).formatRange(start, end);\n        }, ()=>[\n                dateTime(start),\n                dateTime(end)\n            ].join(\" – \"));\n    }\n    function number(value, formatOrOptions) {\n        return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.number, (options)=>formatters.getNumberFormat(locale, options).format(value), ()=>String(value));\n    }\n    function getGlobalNow() {\n        if (globalNow) {\n            return globalNow;\n        } else {\n            onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.ENVIRONMENT_FALLBACK, \"The `now` parameter wasn't provided and there is no global default configured. Consider adding a global default to avoid markup mismatches caused by environment differences. Learn more: https://next-intl.dev/docs/configuration#now\"));\n            return new Date();\n        }\n    }\n    function relativeTime(/** The date time that needs to be formatted. */ date, /** The reference point in time to which `date` will be formatted in relation to.  */ nowOrOptions) {\n        try {\n            let nowDate, unit;\n            const opts = {};\n            if (nowOrOptions instanceof Date || typeof nowOrOptions === \"number\") {\n                nowDate = new Date(nowOrOptions);\n            } else if (nowOrOptions) {\n                if (nowOrOptions.now != null) {\n                    nowDate = new Date(nowOrOptions.now);\n                } else {\n                    nowDate = getGlobalNow();\n                }\n                unit = nowOrOptions.unit;\n                opts.style = nowOrOptions.style;\n                // @ts-expect-error -- Types are slightly outdated\n                opts.numberingSystem = nowOrOptions.numberingSystem;\n            }\n            if (!nowDate) {\n                nowDate = getGlobalNow();\n            }\n            const dateDate = new Date(date);\n            const seconds = (dateDate.getTime() - nowDate.getTime()) / 1000;\n            if (!unit) {\n                unit = resolveRelativeTimeUnit(seconds);\n            }\n            // `numeric: 'auto'` can theoretically produce output like \"yesterday\",\n            // but it only works with integers. E.g. -1 day will produce \"yesterday\",\n            // but -1.1 days will produce \"-1.1 days\". Rounding before formatting is\n            // not desired, as the given dates might cross a threshold were the\n            // output isn't correct anymore. Example: 2024-01-08T23:00:00.000Z and\n            // 2024-01-08T01:00:00.000Z would produce \"yesterday\", which is not the\n            // case. By using `always` we can ensure correct output. The only exception\n            // is the formatting of times <1 second as \"now\".\n            opts.numeric = unit === \"second\" ? \"auto\" : \"always\";\n            const value = calculateRelativeTimeValue(seconds, unit);\n            return formatters.getRelativeTimeFormat(locale, opts).format(value, unit);\n        } catch (error) {\n            onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.FORMATTING_ERROR, error.message));\n            return String(date);\n        }\n    }\n    function list(value, formatOrOptions) {\n        const serializedValue = [];\n        const richValues = new Map();\n        // `formatToParts` only accepts strings, therefore we have to temporarily\n        // replace React elements with a placeholder ID that can be used to retrieve\n        // the original value afterwards.\n        let index = 0;\n        for (const item of value){\n            let serializedItem;\n            if (typeof item === \"object\") {\n                serializedItem = String(index);\n                richValues.set(serializedItem, item);\n            } else {\n                serializedItem = String(item);\n            }\n            serializedValue.push(serializedItem);\n            index++;\n        }\n        return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.list, // @ts-expect-error -- `richValues.size` is used to determine the return type, but TypeScript can't infer the meaning of this correctly\n        (options)=>{\n            const result = formatters.getListFormat(locale, options).formatToParts(serializedValue).map((part)=>part.type === \"literal\" ? part.value : richValues.get(part.value) || part.value);\n            if (richValues.size > 0) {\n                return result;\n            } else {\n                return result.join(\"\");\n            }\n        }, ()=>String(value));\n    }\n    return {\n        dateTime,\n        number,\n        relativeTime,\n        list,\n        dateTimeRange\n    };\n}\nexports.createBaseTranslator = createBaseTranslator;\nexports.createFormatter = createFormatter;\nexports.resolveNamespace = resolveNamespace;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/use-intl/dist/development/createFormatter-QqAaZwGD.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar fastMemoize = __webpack_require__(/*! @formatjs/fast-memoize */ \"(rsc)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\nfunction _defineProperty(e, r, t) {\n    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n        value: t,\n        enumerable: !0,\n        configurable: !0,\n        writable: !0\n    }) : e[r] = t, e;\n}\nfunction _toPrimitive(t, r) {\n    if (\"object\" != typeof t || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n        var i = e.call(t, r || \"default\");\n        if (\"object\" != typeof i) return i;\n        throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == typeof i ? i : i + \"\";\n}\nlet IntlErrorCode = /*#__PURE__*/ function(IntlErrorCode) {\n    IntlErrorCode[\"MISSING_MESSAGE\"] = \"MISSING_MESSAGE\";\n    IntlErrorCode[\"MISSING_FORMAT\"] = \"MISSING_FORMAT\";\n    IntlErrorCode[\"ENVIRONMENT_FALLBACK\"] = \"ENVIRONMENT_FALLBACK\";\n    IntlErrorCode[\"INSUFFICIENT_PATH\"] = \"INSUFFICIENT_PATH\";\n    IntlErrorCode[\"INVALID_MESSAGE\"] = \"INVALID_MESSAGE\";\n    IntlErrorCode[\"INVALID_KEY\"] = \"INVALID_KEY\";\n    IntlErrorCode[\"FORMATTING_ERROR\"] = \"FORMATTING_ERROR\";\n    return IntlErrorCode;\n}({});\nclass IntlError extends Error {\n    constructor(code, originalMessage){\n        let message = code;\n        if (originalMessage) {\n            message += \": \" + originalMessage;\n        }\n        super(message);\n        _defineProperty(this, \"code\", void 0);\n        _defineProperty(this, \"originalMessage\", void 0);\n        this.code = code;\n        if (originalMessage) {\n            this.originalMessage = originalMessage;\n        }\n    }\n}\nfunction joinPath() {\n    for(var _len = arguments.length, parts = new Array(_len), _key = 0; _key < _len; _key++){\n        parts[_key] = arguments[_key];\n    }\n    return parts.filter(Boolean).join(\".\");\n}\n/**\n * Contains defaults that are used for all entry points into the core.\n * See also `InitializedIntlConfiguration`.\n */ function defaultGetMessageFallback(props) {\n    return joinPath(props.namespace, props.key);\n}\nfunction defaultOnError(error) {\n    console.error(error);\n}\nfunction createCache() {\n    return {\n        dateTime: {},\n        number: {},\n        message: {},\n        relativeTime: {},\n        pluralRules: {},\n        list: {},\n        displayNames: {}\n    };\n}\nfunction createMemoCache(store) {\n    return {\n        create () {\n            return {\n                get (key) {\n                    return store[key];\n                },\n                set (key, value) {\n                    store[key] = value;\n                }\n            };\n        }\n    };\n}\nfunction memoFn(fn, cache) {\n    return fastMemoize.memoize(fn, {\n        cache: createMemoCache(cache),\n        strategy: fastMemoize.strategies.variadic\n    });\n}\nfunction memoConstructor(ConstructorFn, cache) {\n    return memoFn(function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        return new ConstructorFn(...args);\n    }, cache);\n}\nfunction createIntlFormatters(cache) {\n    const getDateTimeFormat = memoConstructor(Intl.DateTimeFormat, cache.dateTime);\n    const getNumberFormat = memoConstructor(Intl.NumberFormat, cache.number);\n    const getPluralRules = memoConstructor(Intl.PluralRules, cache.pluralRules);\n    const getRelativeTimeFormat = memoConstructor(Intl.RelativeTimeFormat, cache.relativeTime);\n    const getListFormat = memoConstructor(Intl.ListFormat, cache.list);\n    const getDisplayNames = memoConstructor(Intl.DisplayNames, cache.displayNames);\n    return {\n        getDateTimeFormat,\n        getNumberFormat,\n        getPluralRules,\n        getRelativeTimeFormat,\n        getListFormat,\n        getDisplayNames\n    };\n}\nfunction validateMessagesSegment(messages, invalidKeyLabels, parentPath) {\n    Object.entries(messages).forEach((_ref)=>{\n        let [key, messageOrMessages] = _ref;\n        if (key.includes(\".\")) {\n            let keyLabel = key;\n            if (parentPath) keyLabel += \" (at \".concat(parentPath, \")\");\n            invalidKeyLabels.push(keyLabel);\n        }\n        // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n        if (messageOrMessages != null && typeof messageOrMessages === \"object\") {\n            validateMessagesSegment(messageOrMessages, invalidKeyLabels, joinPath(parentPath, key));\n        }\n    });\n}\nfunction validateMessages(messages, onError) {\n    const invalidKeyLabels = [];\n    validateMessagesSegment(messages, invalidKeyLabels);\n    if (invalidKeyLabels.length > 0) {\n        onError(new IntlError(IntlErrorCode.INVALID_KEY, 'Namespace keys can not contain the character \".\" as this is used to express nesting. Please remove it or replace it with another character.\\n\\nInvalid '.concat(invalidKeyLabels.length === 1 ? \"key\" : \"keys\", \": \").concat(invalidKeyLabels.join(\", \"), '\\n\\nIf you\\'re migrating from a flat structure, you can convert your messages as follows:\\n\\nimport {set} from \"lodash\";\\n\\nconst input = {\\n  \"one.one\": \"1.1\",\\n  \"one.two\": \"1.2\",\\n  \"two.one.one\": \"2.1.1\"\\n};\\n\\nconst output = Object.entries(input).reduce(\\n  (acc, [key, value]) => set(acc, key, value),\\n  {}\\n);\\n\\n// Output:\\n//\\n// {\\n//   \"one\": {\\n//     \"one\": \"1.1\",\\n//     \"two\": \"1.2\"\\n//   },\\n//   \"two\": {\\n//     \"one\": {\\n//       \"one\": \"2.1.1\"\\n//     }\\n//   }\\n// }\\n')));\n    }\n}\n/**\n * Enhances the incoming props with defaults.\n */ function initializeConfig(_ref) {\n    let { getMessageFallback, messages, onError, ...rest } = _ref;\n    const finalOnError = onError || defaultOnError;\n    const finalGetMessageFallback = getMessageFallback || defaultGetMessageFallback;\n    {\n        if (messages) {\n            validateMessages(messages, finalOnError);\n        }\n    }\n    return {\n        ...rest,\n        messages,\n        onError: finalOnError,\n        getMessageFallback: finalGetMessageFallback\n    };\n}\nexports.IntlError = IntlError;\nexports.IntlErrorCode = IntlErrorCode;\nexports.createCache = createCache;\nexports.createIntlFormatters = createIntlFormatters;\nexports.defaultGetMessageFallback = defaultGetMessageFallback;\nexports.defaultOnError = defaultOnError;\nexports.initializeConfig = initializeConfig;\nexports.joinPath = joinPath;\nexports.memoFn = memoFn;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\n");

/***/ })

};
;