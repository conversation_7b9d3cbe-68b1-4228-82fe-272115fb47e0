"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-intl";
exports.ids = ["vendor-chunks/next-intl"];
exports.modules = {

/***/ "(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nfunction _extends() {\n    return _extends = Object.assign ? Object.assign.bind() : function(n) {\n        for(var e = 1; e < arguments.length; e++){\n            var t = arguments[e];\n            for(var r in t)({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n        }\n        return n;\n    }, _extends.apply(null, arguments);\n}\nexports[\"extends\"] = _extends;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvX3ZpcnR1YWwvX3JvbGx1cFBsdWdpbkJhYmVsSGVscGVycy5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUVBQSw4Q0FBNkM7SUFBRUcsT0FBTztBQUFLLENBQUMsRUFBQztBQUU3RCxTQUFTQztJQUNQLE9BQU9BLFdBQVdKLE9BQU9LLE1BQU0sR0FBR0wsT0FBT0ssTUFBTSxDQUFDQyxJQUFJLEtBQUssU0FBVUMsQ0FBQztRQUNsRSxJQUFLLElBQUlDLElBQUksR0FBR0EsSUFBSUMsVUFBVUMsTUFBTSxFQUFFRixJQUFLO1lBQ3pDLElBQUlHLElBQUlGLFNBQVMsQ0FBQ0QsRUFBRTtZQUNwQixJQUFLLElBQUlJLEtBQUtELEVBQUcsQ0FBQyxDQUFDLEdBQUdFLGNBQWMsQ0FBQ0MsSUFBSSxDQUFDSCxHQUFHQyxNQUFPTCxDQUFBQSxDQUFDLENBQUNLLEVBQUUsR0FBR0QsQ0FBQyxDQUFDQyxFQUFFO1FBQ2pFO1FBQ0EsT0FBT0w7SUFDVCxHQUFHSCxTQUFTVyxLQUFLLENBQUMsTUFBTU47QUFDMUI7QUFFQVAsa0JBQWUsR0FBR0UiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zbWUtYW5hbHl0aWNhLWRvY3MvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvX3ZpcnR1YWwvX3JvbGx1cFBsdWdpbkJhYmVsSGVscGVycy5qcz9lZDE3Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICdfX2VzTW9kdWxlJywgeyB2YWx1ZTogdHJ1ZSB9KTtcblxuZnVuY3Rpb24gX2V4dGVuZHMoKSB7XG4gIHJldHVybiBfZXh0ZW5kcyA9IE9iamVjdC5hc3NpZ24gPyBPYmplY3QuYXNzaWduLmJpbmQoKSA6IGZ1bmN0aW9uIChuKSB7XG4gICAgZm9yICh2YXIgZSA9IDE7IGUgPCBhcmd1bWVudHMubGVuZ3RoOyBlKyspIHtcbiAgICAgIHZhciB0ID0gYXJndW1lbnRzW2VdO1xuICAgICAgZm9yICh2YXIgciBpbiB0KSAoe30pLmhhc093blByb3BlcnR5LmNhbGwodCwgcikgJiYgKG5bcl0gPSB0W3JdKTtcbiAgICB9XG4gICAgcmV0dXJuIG47XG4gIH0sIF9leHRlbmRzLmFwcGx5KG51bGwsIGFyZ3VtZW50cyk7XG59XG5cbmV4cG9ydHMuZXh0ZW5kcyA9IF9leHRlbmRzO1xuIl0sIm5hbWVzIjpbIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZXhwb3J0cyIsInZhbHVlIiwiX2V4dGVuZHMiLCJhc3NpZ24iLCJiaW5kIiwibiIsImUiLCJhcmd1bWVudHMiLCJsZW5ndGgiLCJ0IiwiciIsImhhc093blByb3BlcnR5IiwiY2FsbCIsImFwcGx5IiwiZXh0ZW5kcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/index.react-client.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/index.react-client.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar index = __webpack_require__(/*! ./react-client/index.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/index.js\");\nvar useLocale = __webpack_require__(/*! ./react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar NextIntlClientProvider = __webpack_require__(/*! ./shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/NextIntlClientProvider.js\");\nvar useIntl = __webpack_require__(/*! use-intl */ \"(ssr)/./node_modules/use-intl/dist/index.js\");\nexports.useFormatter = index.useFormatter;\nexports.useTranslations = index.useTranslations;\nexports.useLocale = useLocale.default;\nexports.NextIntlClientProvider = NextIntlClientProvider.default;\nObject.keys(useIntl).forEach(function(k) {\n    if (k !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {\n        enumerable: true,\n        get: function() {\n            return useIntl[k];\n        }\n    });\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation.react-client.js":
/*!****************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation.react-client.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar createSharedPathnamesNavigation = __webpack_require__(/*! ./navigation/react-client/createSharedPathnamesNavigation.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createSharedPathnamesNavigation.js\");\nvar createLocalizedPathnamesNavigation = __webpack_require__(/*! ./navigation/react-client/createLocalizedPathnamesNavigation.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createLocalizedPathnamesNavigation.js\");\nvar createNavigation = __webpack_require__(/*! ./navigation/react-client/createNavigation.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createNavigation.js\");\nexports.createSharedPathnamesNavigation = createSharedPathnamesNavigation.default;\nexports.createLocalizedPathnamesNavigation = createLocalizedPathnamesNavigation.default;\nexports.createNavigation = createNavigation.default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvbmF2aWdhdGlvbi5yZWFjdC1jbGllbnQuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFFQUEsOENBQTZDO0lBQUVHLE9BQU87QUFBSyxDQUFDLEVBQUM7QUFFN0QsSUFBSUMsa0NBQWtDQyxtQkFBT0EsQ0FBQztBQUM5QyxJQUFJQyxxQ0FBcUNELG1CQUFPQSxDQUFDO0FBQ2pELElBQUlFLG1CQUFtQkYsbUJBQU9BLENBQUM7QUFJL0JILHVDQUF1QyxHQUFHRSxnQ0FBZ0NJLE9BQU87QUFDakZOLDBDQUEwQyxHQUFHSSxtQ0FBbUNFLE9BQU87QUFDdkZOLHdCQUF3QixHQUFHSyxpQkFBaUJDLE9BQU8iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zbWUtYW5hbHl0aWNhLWRvY3MvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvbmF2aWdhdGlvbi5yZWFjdC1jbGllbnQuanM/ZDYyOSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAnX19lc01vZHVsZScsIHsgdmFsdWU6IHRydWUgfSk7XG5cbnZhciBjcmVhdGVTaGFyZWRQYXRobmFtZXNOYXZpZ2F0aW9uID0gcmVxdWlyZSgnLi9uYXZpZ2F0aW9uL3JlYWN0LWNsaWVudC9jcmVhdGVTaGFyZWRQYXRobmFtZXNOYXZpZ2F0aW9uLmpzJyk7XG52YXIgY3JlYXRlTG9jYWxpemVkUGF0aG5hbWVzTmF2aWdhdGlvbiA9IHJlcXVpcmUoJy4vbmF2aWdhdGlvbi9yZWFjdC1jbGllbnQvY3JlYXRlTG9jYWxpemVkUGF0aG5hbWVzTmF2aWdhdGlvbi5qcycpO1xudmFyIGNyZWF0ZU5hdmlnYXRpb24gPSByZXF1aXJlKCcuL25hdmlnYXRpb24vcmVhY3QtY2xpZW50L2NyZWF0ZU5hdmlnYXRpb24uanMnKTtcblxuXG5cbmV4cG9ydHMuY3JlYXRlU2hhcmVkUGF0aG5hbWVzTmF2aWdhdGlvbiA9IGNyZWF0ZVNoYXJlZFBhdGhuYW1lc05hdmlnYXRpb24uZGVmYXVsdDtcbmV4cG9ydHMuY3JlYXRlTG9jYWxpemVkUGF0aG5hbWVzTmF2aWdhdGlvbiA9IGNyZWF0ZUxvY2FsaXplZFBhdGhuYW1lc05hdmlnYXRpb24uZGVmYXVsdDtcbmV4cG9ydHMuY3JlYXRlTmF2aWdhdGlvbiA9IGNyZWF0ZU5hdmlnYXRpb24uZGVmYXVsdDtcbiJdLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsImNyZWF0ZVNoYXJlZFBhdGhuYW1lc05hdmlnYXRpb24iLCJyZXF1aXJlIiwiY3JlYXRlTG9jYWxpemVkUGF0aG5hbWVzTmF2aWdhdGlvbiIsImNyZWF0ZU5hdmlnYXRpb24iLCJkZWZhdWx0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation.react-client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/ClientLink.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/ClientLink.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar utils = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\nvar LegacyBaseLink = __webpack_require__(/*! ../shared/LegacyBaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/LegacyBaseLink.js\");\nfunction _interopDefault(e) {\n    return e && e.__esModule ? e : {\n        default: e\n    };\n}\nvar React__default = /*#__PURE__*/ _interopDefault(React);\nfunction ClientLink(_ref, ref) {\n    let { locale, localePrefix, ...rest } = _ref;\n    const defaultLocale = useLocale.default();\n    const finalLocale = locale || defaultLocale;\n    const prefix = utils.getLocalePrefix(finalLocale, localePrefix);\n    return /*#__PURE__*/ React__default.default.createElement(LegacyBaseLink.default, _rollupPluginBabelHelpers.extends({\n        ref: ref,\n        locale: finalLocale,\n        localePrefixMode: localePrefix.mode,\n        prefix: prefix\n    }, rest));\n}\n/**\n * Wraps `next/link` and prefixes the `href` with the current locale if\n * necessary.\n *\n * @example\n * ```tsx\n * import {Link} from 'next-intl';\n *\n * // When the user is on `/en`, the link will point to `/en/about`\n * <Link href=\"/about\">About</Link>\n *\n * // You can override the `locale` to switch to another language\n * <Link href=\"/\" locale=\"de\">Switch to German</Link>\n * ```\n *\n * Note that when a `locale` prop is passed to switch the locale, the `prefetch`\n * prop is not supported. This is because Next.js would prefetch the page and\n * the `set-cookie` response header would cause the locale cookie on the current\n * page to be overwritten before the user even decides to change the locale.\n */ const ClientLinkWithRef = /*#__PURE__*/ React.forwardRef(ClientLink);\nClientLinkWithRef.displayName = \"ClientLink\";\nexports[\"default\"] = ClientLinkWithRef;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/ClientLink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createLocalizedPathnamesNavigation.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/createLocalizedPathnamesNavigation.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar config = __webpack_require__(/*! ../../routing/config.js */ \"(ssr)/./node_modules/next-intl/dist/development/routing/config.js\");\nvar utils = __webpack_require__(/*! ../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js\");\nvar ClientLink = __webpack_require__(/*! ./ClientLink.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/ClientLink.js\");\nvar redirects = __webpack_require__(/*! ./redirects.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/redirects.js\");\nvar useBasePathname = __webpack_require__(/*! ./useBasePathname.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBasePathname.js\");\nvar useBaseRouter = __webpack_require__(/*! ./useBaseRouter.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBaseRouter.js\");\nfunction _interopDefault(e) {\n    return e && e.__esModule ? e : {\n        default: e\n    };\n}\nvar React__default = /*#__PURE__*/ _interopDefault(React);\n/**\n * @deprecated Consider switching to `createNavigation` (see https://next-intl.dev/blog/next-intl-3-22#create-navigation)\n **/ function createLocalizedPathnamesNavigation(routing) {\n    const config$1 = config.receiveRoutingConfig(routing);\n    const localeCookie = config.receiveLocaleCookie(routing.localeCookie);\n    function useTypedLocale() {\n        const locale = useLocale.default();\n        const isValid = config$1.locales.includes(locale);\n        if (!isValid) {\n            throw new Error('Unknown locale encountered: \"'.concat(locale, '\". Make sure to validate the locale in `i18n.ts`.'));\n        }\n        return locale;\n    }\n    function Link(_ref, ref) {\n        let { href, locale, ...rest } = _ref;\n        const defaultLocale = useTypedLocale();\n        const finalLocale = locale || defaultLocale;\n        return /*#__PURE__*/ React__default.default.createElement(ClientLink.default, _rollupPluginBabelHelpers.extends({\n            ref: ref,\n            href: utils.compileLocalizedPathname({\n                locale: finalLocale,\n                // @ts-expect-error -- This is ok\n                pathname: href,\n                // @ts-expect-error -- This is ok\n                params: typeof href === \"object\" ? href.params : undefined,\n                pathnames: config$1.pathnames\n            }),\n            locale: locale,\n            localeCookie: localeCookie,\n            localePrefix: config$1.localePrefix\n        }, rest));\n    }\n    const LinkWithRef = /*#__PURE__*/ React.forwardRef(Link);\n    LinkWithRef.displayName = \"Link\";\n    function redirect(href) {\n        // eslint-disable-next-line react-hooks/rules-of-hooks -- Reading from context here is fine, since `redirect` should be called during render\n        const locale = useTypedLocale();\n        const resolvedHref = getPathname({\n            href,\n            locale\n        });\n        for(var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n            args[_key - 1] = arguments[_key];\n        }\n        return redirects.clientRedirect({\n            pathname: resolvedHref,\n            localePrefix: config$1.localePrefix\n        }, ...args);\n    }\n    function permanentRedirect(href) {\n        // eslint-disable-next-line react-hooks/rules-of-hooks -- Reading from context here is fine, since `redirect` should be called during render\n        const locale = useTypedLocale();\n        const resolvedHref = getPathname({\n            href,\n            locale\n        });\n        for(var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++){\n            args[_key2 - 1] = arguments[_key2];\n        }\n        return redirects.clientPermanentRedirect({\n            pathname: resolvedHref,\n            localePrefix: config$1.localePrefix\n        }, ...args);\n    }\n    function useRouter() {\n        const baseRouter = useBaseRouter.default(config$1.localePrefix, localeCookie);\n        const defaultLocale = useTypedLocale();\n        return React.useMemo(()=>({\n                ...baseRouter,\n                push (href) {\n                    var _args$;\n                    for(var _len3 = arguments.length, args = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++){\n                        args[_key3 - 1] = arguments[_key3];\n                    }\n                    const resolvedHref = getPathname({\n                        href,\n                        locale: ((_args$ = args[0]) === null || _args$ === void 0 ? void 0 : _args$.locale) || defaultLocale\n                    });\n                    return baseRouter.push(resolvedHref, ...args);\n                },\n                replace (href) {\n                    var _args$2;\n                    for(var _len4 = arguments.length, args = new Array(_len4 > 1 ? _len4 - 1 : 0), _key4 = 1; _key4 < _len4; _key4++){\n                        args[_key4 - 1] = arguments[_key4];\n                    }\n                    const resolvedHref = getPathname({\n                        href,\n                        locale: ((_args$2 = args[0]) === null || _args$2 === void 0 ? void 0 : _args$2.locale) || defaultLocale\n                    });\n                    return baseRouter.replace(resolvedHref, ...args);\n                },\n                prefetch (href) {\n                    var _args$3;\n                    for(var _len5 = arguments.length, args = new Array(_len5 > 1 ? _len5 - 1 : 0), _key5 = 1; _key5 < _len5; _key5++){\n                        args[_key5 - 1] = arguments[_key5];\n                    }\n                    const resolvedHref = getPathname({\n                        href,\n                        locale: ((_args$3 = args[0]) === null || _args$3 === void 0 ? void 0 : _args$3.locale) || defaultLocale\n                    });\n                    return baseRouter.prefetch(resolvedHref, ...args);\n                }\n            }), [\n            baseRouter,\n            defaultLocale\n        ]);\n    }\n    function usePathname() {\n        const pathname = useBasePathname.default(config$1);\n        const locale = useTypedLocale();\n        // @ts-expect-error -- Mirror the behavior from Next.js, where `null` is returned when `usePathname` is used outside of Next, but the types indicate that a string is always returned.\n        return React.useMemo(()=>pathname ? utils.getRoute(locale, pathname, config$1.pathnames) : pathname, [\n            locale,\n            pathname\n        ]);\n    }\n    function getPathname(_ref2) {\n        let { href, locale } = _ref2;\n        return utils.compileLocalizedPathname({\n            ...utils.normalizeNameOrNameWithParams(href),\n            locale,\n            pathnames: config$1.pathnames\n        });\n    }\n    return {\n        Link: LinkWithRef,\n        redirect,\n        permanentRedirect,\n        usePathname,\n        useRouter,\n        getPathname\n    };\n}\nexports[\"default\"] = createLocalizedPathnamesNavigation;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createLocalizedPathnamesNavigation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createNavigation.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/createNavigation.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar createSharedNavigationFns = __webpack_require__(/*! ../shared/createSharedNavigationFns.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/createSharedNavigationFns.js\");\nvar syncLocaleCookie = __webpack_require__(/*! ../shared/syncLocaleCookie.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/syncLocaleCookie.js\");\nvar utils = __webpack_require__(/*! ../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js\");\nvar useBasePathname = __webpack_require__(/*! ./useBasePathname.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBasePathname.js\");\nfunction createNavigation(routing) {\n    function useTypedLocale() {\n        return useLocale.default();\n    }\n    const { Link, config, getPathname, ...redirects } = createSharedNavigationFns.default(useTypedLocale, routing);\n    /** @see https://next-intl.dev/docs/routing/navigation#usepathname */ function usePathname() {\n        const pathname = useBasePathname.default(config);\n        const locale = useTypedLocale();\n        // @ts-expect-error -- Mirror the behavior from Next.js, where `null` is returned when `usePathname` is used outside of Next, but the types indicate that a string is always returned.\n        return React.useMemo(()=>pathname && // @ts-expect-error -- This is fine\n            config.pathnames ? utils.getRoute(locale, pathname, // @ts-expect-error -- This is fine\n            config.pathnames) : pathname, [\n            locale,\n            pathname\n        ]);\n    }\n    function useRouter() {\n        const router = navigation.useRouter();\n        const curLocale = useTypedLocale();\n        const nextPathname = navigation.usePathname();\n        return React.useMemo(()=>{\n            function createHandler(fn) {\n                return function handler(href, options) {\n                    const { locale: nextLocale, ...rest } = options || {};\n                    // @ts-expect-error -- We're passing a domain here just in case\n                    const pathname = getPathname({\n                        href,\n                        locale: nextLocale || curLocale,\n                        domain: window.location.host\n                    });\n                    const args = [\n                        pathname\n                    ];\n                    if (Object.keys(rest).length > 0) {\n                        // @ts-expect-error -- This is fine\n                        args.push(rest);\n                    }\n                    fn(...args);\n                    syncLocaleCookie.default(config.localeCookie, nextPathname, curLocale, nextLocale);\n                };\n            }\n            return {\n                ...router,\n                /** @see https://next-intl.dev/docs/routing/navigation#userouter */ push: createHandler(router.push),\n                /** @see https://next-intl.dev/docs/routing/navigation#userouter */ replace: createHandler(router.replace),\n                /** @see https://next-intl.dev/docs/routing/navigation#userouter */ prefetch: createHandler(router.prefetch)\n            };\n        }, [\n            curLocale,\n            nextPathname,\n            router\n        ]);\n    }\n    return {\n        ...redirects,\n        Link,\n        usePathname,\n        useRouter,\n        getPathname\n    };\n}\nexports[\"default\"] = createNavigation;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createNavigation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createSharedPathnamesNavigation.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/createSharedPathnamesNavigation.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar config = __webpack_require__(/*! ../../routing/config.js */ \"(ssr)/./node_modules/next-intl/dist/development/routing/config.js\");\nvar ClientLink = __webpack_require__(/*! ./ClientLink.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/ClientLink.js\");\nvar redirects = __webpack_require__(/*! ./redirects.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/redirects.js\");\nvar useBasePathname = __webpack_require__(/*! ./useBasePathname.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBasePathname.js\");\nvar useBaseRouter = __webpack_require__(/*! ./useBaseRouter.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBaseRouter.js\");\nfunction _interopDefault(e) {\n    return e && e.__esModule ? e : {\n        default: e\n    };\n}\nvar React__default = /*#__PURE__*/ _interopDefault(React);\n/**\n * @deprecated Consider switching to `createNavigation` (see https://next-intl.dev/blog/next-intl-3-22#create-navigation)\n **/ function createSharedPathnamesNavigation(routing) {\n    const localePrefix = config.receiveLocalePrefixConfig(routing === null || routing === void 0 ? void 0 : routing.localePrefix);\n    const localeCookie = config.receiveLocaleCookie(routing === null || routing === void 0 ? void 0 : routing.localeCookie);\n    function Link(props, ref) {\n        return /*#__PURE__*/ React__default.default.createElement(ClientLink.default, _rollupPluginBabelHelpers.extends({\n            ref: ref,\n            localeCookie: localeCookie,\n            localePrefix: localePrefix\n        }, props));\n    }\n    const LinkWithRef = /*#__PURE__*/ React.forwardRef(Link);\n    LinkWithRef.displayName = \"Link\";\n    function redirect(pathname) {\n        for(var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n            args[_key - 1] = arguments[_key];\n        }\n        return redirects.clientRedirect({\n            pathname,\n            localePrefix\n        }, ...args);\n    }\n    function permanentRedirect(pathname) {\n        for(var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++){\n            args[_key2 - 1] = arguments[_key2];\n        }\n        return redirects.clientPermanentRedirect({\n            pathname,\n            localePrefix\n        }, ...args);\n    }\n    function usePathname() {\n        const result = useBasePathname.default({\n            localePrefix,\n            defaultLocale: routing === null || routing === void 0 ? void 0 : routing.defaultLocale\n        });\n        // @ts-expect-error -- Mirror the behavior from Next.js, where `null` is returned when `usePathname` is used outside of Next, but the types indicate that a string is always returned.\n        return result;\n    }\n    function useRouter() {\n        return useBaseRouter.default(localePrefix, localeCookie);\n    }\n    return {\n        Link: LinkWithRef,\n        redirect,\n        permanentRedirect,\n        usePathname,\n        useRouter\n    };\n}\nexports[\"default\"] = createSharedPathnamesNavigation;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createSharedPathnamesNavigation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/redirects.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/redirects.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar redirects = __webpack_require__(/*! ../shared/redirects.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/redirects.js\");\nfunction createRedirectFn(redirectFn) {\n    return function clientRedirect(params) {\n        let locale;\n        try {\n            // eslint-disable-next-line react-hooks/rules-of-hooks -- Reading from context here is fine, since `redirect` should be called during render\n            locale = useLocale.default();\n        } catch (e) {\n            {\n                throw new Error(\"`redirect()` and `permanentRedirect()` can only be called during render. To redirect in an event handler or similar, you can use `useRouter()` instead.\");\n            }\n        }\n        for(var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n            args[_key - 1] = arguments[_key];\n        }\n        return redirectFn({\n            ...params,\n            locale\n        }, ...args);\n    };\n}\nconst clientRedirect = createRedirectFn(redirects.baseRedirect);\nconst clientPermanentRedirect = createRedirectFn(redirects.basePermanentRedirect);\nexports.clientPermanentRedirect = clientPermanentRedirect;\nexports.clientRedirect = clientRedirect;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/redirects.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBasePathname.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/useBasePathname.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar utils = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\nfunction useBasePathname(config) {\n    // The types aren't entirely correct here. Outside of Next.js\n    // `useParams` can be called, but the return type is `null`.\n    // Notes on `useNextPathname`:\n    // - Types aren't entirely correct. Outside of Next.js the\n    //   hook will return `null` (e.g. unit tests)\n    // - A base path is stripped from the result\n    // - Rewrites *are* taken into account (i.e. the pathname\n    //   that the user sees in the browser is returned)\n    const pathname = navigation.usePathname();\n    const locale = useLocale.default();\n    return React.useMemo(()=>{\n        if (!pathname) return pathname;\n        let unlocalizedPathname = pathname;\n        const prefix = utils.getLocalePrefix(locale, config.localePrefix);\n        const isPathnamePrefixed = utils.hasPathnamePrefixed(prefix, pathname);\n        if (isPathnamePrefixed) {\n            unlocalizedPathname = utils.unprefixPathname(pathname, prefix);\n        } else if (config.localePrefix.mode === \"as-needed\" && config.localePrefix.prefixes) {\n            // Workaround for https://github.com/vercel/next.js/issues/73085\n            const localeAsPrefix = utils.getLocaleAsPrefix(locale);\n            if (utils.hasPathnamePrefixed(localeAsPrefix, pathname)) {\n                unlocalizedPathname = utils.unprefixPathname(pathname, localeAsPrefix);\n            }\n        }\n        return unlocalizedPathname;\n    }, [\n        config.localePrefix,\n        locale,\n        pathname\n    ]);\n}\nexports[\"default\"] = useBasePathname;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBasePathname.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBaseRouter.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/useBaseRouter.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar utils$1 = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\nvar syncLocaleCookie = __webpack_require__(/*! ../shared/syncLocaleCookie.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/syncLocaleCookie.js\");\nvar utils = __webpack_require__(/*! ../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js\");\n/**\n * Returns a wrapped instance of `useRouter` from `next/navigation` that\n * will automatically localize the `href` parameters it receives.\n *\n * @example\n * ```tsx\n * 'use client';\n *\n * import {useRouter} from 'next-intl/client';\n *\n * const router = useRouter();\n *\n * // When the user is on `/en`, the router will navigate to `/en/about`\n * router.push('/about');\n *\n * // Optionally, you can switch the locale by passing the second argument\n * router.push('/about', {locale: 'de'});\n * ```\n */ function useBaseRouter(localePrefix, localeCookie) {\n    const router = navigation.useRouter();\n    const locale = useLocale.default();\n    const pathname = navigation.usePathname();\n    return React.useMemo(()=>{\n        function localize(href, nextLocale) {\n            let curPathname = window.location.pathname;\n            const basePath = utils.getBasePath(pathname);\n            if (basePath) curPathname = curPathname.replace(basePath, \"\");\n            const targetLocale = nextLocale || locale;\n            // We generate a prefix in any case, but decide\n            // in `localizeHref` if we apply it or not\n            const prefix = utils$1.getLocalePrefix(targetLocale, localePrefix);\n            return utils$1.localizeHref(href, targetLocale, locale, curPathname, prefix);\n        }\n        function createHandler(fn) {\n            return function handler(href, options) {\n                const { locale: nextLocale, ...rest } = options || {};\n                syncLocaleCookie.default(localeCookie, pathname, locale, nextLocale);\n                const args = [\n                    localize(href, nextLocale)\n                ];\n                if (Object.keys(rest).length > 0) {\n                    args.push(rest);\n                }\n                // @ts-expect-error -- This is ok\n                return fn(...args);\n            };\n        }\n        return {\n            ...router,\n            push: createHandler(router.push),\n            replace: createHandler(router.replace),\n            prefetch: createHandler(router.prefetch)\n        };\n    }, [\n        locale,\n        localeCookie,\n        localePrefix,\n        pathname,\n        router\n    ]);\n}\nexports[\"default\"] = useBaseRouter;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBaseRouter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/shared/BaseLink.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/shared/BaseLink.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar NextLink = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar syncLocaleCookie = __webpack_require__(/*! ./syncLocaleCookie.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/syncLocaleCookie.js\");\nfunction _interopDefault(e) {\n    return e && e.__esModule ? e : {\n        default: e\n    };\n}\nvar NextLink__default = /*#__PURE__*/ _interopDefault(NextLink);\nvar React__default = /*#__PURE__*/ _interopDefault(React);\nfunction BaseLink(_ref, ref) {\n    let { defaultLocale, href, locale, localeCookie, onClick, prefetch, unprefixed, ...rest } = _ref;\n    const curLocale = useLocale.default();\n    const isChangingLocale = locale != null && locale !== curLocale;\n    const linkLocale = locale || curLocale;\n    const host = useHost();\n    const finalHref = // Only after hydration (to avoid mismatches)\n    host && // If there is an `unprefixed` prop, the\n    // `defaultLocale` might differ by domain\n    unprefixed && // Unprefix the pathname if a domain matches\n    (unprefixed.domains[host] === linkLocale || // … and handle unknown domains by applying the\n    // global `defaultLocale` (e.g. on localhost)\n    !Object.keys(unprefixed.domains).includes(host) && curLocale === defaultLocale && !locale) ? unprefixed.pathname : href;\n    // The types aren't entirely correct here. Outside of Next.js\n    // `useParams` can be called, but the return type is `null`.\n    const pathname = navigation.usePathname();\n    function onLinkClick(event) {\n        syncLocaleCookie.default(localeCookie, pathname, curLocale, locale);\n        if (onClick) onClick(event);\n    }\n    if (isChangingLocale) {\n        if (prefetch && \"development\" !== \"production\") {\n            console.error(\"The `prefetch` prop is currently not supported when using the `locale` prop on `Link` to switch the locale.`\");\n        }\n        prefetch = false;\n    }\n    return /*#__PURE__*/ React__default.default.createElement(NextLink__default.default, _rollupPluginBabelHelpers.extends({\n        ref: ref,\n        href: finalHref,\n        hrefLang: isChangingLocale ? locale : undefined,\n        onClick: onLinkClick,\n        prefetch: prefetch\n    }, rest));\n}\nfunction useHost() {\n    const [host, setHost] = React.useState();\n    React.useEffect(()=>{\n        setHost(window.location.host);\n    }, []);\n    return host;\n}\nvar BaseLink$1 = /*#__PURE__*/ React.forwardRef(BaseLink);\nexports[\"default\"] = BaseLink$1;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/shared/BaseLink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/shared/LegacyBaseLink.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/shared/LegacyBaseLink.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar utils = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\nvar BaseLink = __webpack_require__(/*! ./BaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/BaseLink.js\");\nfunction _interopDefault(e) {\n    return e && e.__esModule ? e : {\n        default: e\n    };\n}\nvar React__default = /*#__PURE__*/ _interopDefault(React);\nfunction LegacyBaseLink(_ref, ref) {\n    let { href, locale, localeCookie, localePrefixMode, prefix, ...rest } = _ref;\n    // The types aren't entirely correct here. Outside of Next.js\n    // `useParams` can be called, but the return type is `null`.\n    const pathname = navigation.usePathname();\n    const curLocale = useLocale.default();\n    const isChangingLocale = locale !== curLocale;\n    const [localizedHref, setLocalizedHref] = React.useState(()=>utils.isLocalizableHref(href) && (localePrefixMode !== \"never\" || isChangingLocale) ? // For the `localePrefix: 'as-needed' strategy, the href shouldn't\n        // be prefixed if the locale is the default locale. To determine this, we\n        // need a) the default locale and b) the information if we use prefixed\n        // routing. The default locale can vary by domain, therefore during the\n        // RSC as well as the SSR render, we can't determine the default locale\n        // statically. Therefore we always prefix the href since this will\n        // always result in a valid URL, even if it might cause a redirect. This\n        // is better than pointing to a non-localized href during the server\n        // render, which would potentially be wrong. The final href is\n        // determined in the effect below.\n        utils.prefixHref(href, prefix) : href);\n    React.useEffect(()=>{\n        if (!pathname) return;\n        setLocalizedHref(utils.localizeHref(href, locale, curLocale, pathname, prefix));\n    }, [\n        curLocale,\n        href,\n        locale,\n        pathname,\n        prefix\n    ]);\n    return /*#__PURE__*/ React__default.default.createElement(BaseLink.default, _rollupPluginBabelHelpers.extends({\n        ref: ref,\n        href: localizedHref,\n        locale: locale,\n        localeCookie: localeCookie\n    }, rest));\n}\nconst LegacyBaseLinkWithRef = /*#__PURE__*/ React.forwardRef(LegacyBaseLink);\nLegacyBaseLinkWithRef.displayName = \"ClientLink\";\nexports[\"default\"] = LegacyBaseLinkWithRef;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/shared/LegacyBaseLink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/shared/createSharedNavigationFns.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/shared/createSharedNavigationFns.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar config = __webpack_require__(/*! ../../routing/config.js */ \"(ssr)/./node_modules/next-intl/dist/development/routing/config.js\");\nvar utils$1 = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\nvar BaseLink = __webpack_require__(/*! ./BaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/BaseLink.js\");\nvar utils = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js\");\nfunction _interopDefault(e) {\n    return e && e.__esModule ? e : {\n        default: e\n    };\n}\nvar React__default = /*#__PURE__*/ _interopDefault(React);\n/**\n * Shared implementations for `react-server` and `react-client`\n */ function createSharedNavigationFns(getLocale, routing) {\n    const config$1 = config.receiveRoutingConfig(routing || {});\n    {\n        utils.validateReceivedConfig(config$1);\n    }\n    const pathnames = config$1.pathnames;\n    // This combination requires that the current host is known in order to\n    // compute a correct pathname. Since that can only be achieved by reading from\n    // headers, this would break static rendering. Therefore, as a workaround we\n    // always add a prefix in this case to be on the safe side. The downside is\n    // that the user might get redirected again if the middleware detects that the\n    // prefix is not needed.\n    const forcePrefixSsr = config$1.localePrefix.mode === \"as-needed\" && config$1.domains || undefined;\n    function Link(_ref, ref) {\n        let { href, locale, ...rest } = _ref;\n        let pathname, params, query;\n        if (typeof href === \"object\") {\n            pathname = href.pathname;\n            query = href.query;\n            // @ts-expect-error -- This is ok\n            params = href.params;\n        } else {\n            pathname = href;\n        }\n        // @ts-expect-error -- This is ok\n        const isLocalizable = utils$1.isLocalizableHref(href);\n        const localePromiseOrValue = getLocale();\n        const curLocale = utils$1.isPromise(localePromiseOrValue) ? React.use(localePromiseOrValue) : localePromiseOrValue;\n        const finalPathname = isLocalizable ? getPathname(// @ts-expect-error -- This is ok\n        {\n            locale: locale || curLocale,\n            href: pathnames == null ? pathname : {\n                pathname,\n                params\n            }\n        }, locale != null || forcePrefixSsr || undefined) : pathname;\n        return /*#__PURE__*/ React__default.default.createElement(BaseLink.default, _rollupPluginBabelHelpers.extends({\n            ref: ref,\n            defaultLocale: config$1.defaultLocale,\n            href: typeof href === \"object\" ? {\n                ...href,\n                pathname: finalPathname\n            } : finalPathname,\n            locale: locale,\n            localeCookie: config$1.localeCookie,\n            unprefixed: forcePrefixSsr && isLocalizable ? {\n                domains: config$1.domains.reduce((acc, domain)=>{\n                    // @ts-expect-error -- This is ok\n                    acc[domain.domain] = domain.defaultLocale;\n                    return acc;\n                }, {}),\n                pathname: getPathname(// @ts-expect-error -- This is ok\n                {\n                    locale: curLocale,\n                    href: pathnames == null ? {\n                        pathname,\n                        query\n                    } : {\n                        pathname,\n                        query,\n                        params\n                    }\n                }, false)\n            } : undefined\n        }, rest));\n    }\n    const LinkWithRef = /*#__PURE__*/ React.forwardRef(Link);\n    function getPathname(args, /** @private Removed in types returned below */ _forcePrefix) {\n        const { href, locale } = args;\n        let pathname;\n        if (pathnames == null) {\n            if (typeof href === \"object\") {\n                pathname = href.pathname;\n                if (href.query) {\n                    pathname += utils.serializeSearchParams(href.query);\n                }\n            } else {\n                pathname = href;\n            }\n        } else {\n            pathname = utils.compileLocalizedPathname({\n                locale,\n                // @ts-expect-error -- This is ok\n                ...utils.normalizeNameOrNameWithParams(href),\n                // @ts-expect-error -- This is ok\n                pathnames: config$1.pathnames\n            });\n        }\n        return utils.applyPathnamePrefix(pathname, locale, config$1, // @ts-expect-error -- This is ok\n        args.domain, _forcePrefix);\n    }\n    function getRedirectFn(fn) {\n        /** @see https://next-intl.dev/docs/routing/navigation#redirect */ return function redirectFn(args) {\n            for(var _len = arguments.length, rest = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n                rest[_key - 1] = arguments[_key];\n            }\n            return fn(// @ts-expect-error -- We're forcing the prefix when no domain is provided\n            getPathname(args, args.domain ? undefined : forcePrefixSsr), ...rest);\n        };\n    }\n    const redirect = getRedirectFn(navigation.redirect);\n    const permanentRedirect = getRedirectFn(navigation.permanentRedirect);\n    return {\n        config: config$1,\n        Link: LinkWithRef,\n        redirect,\n        permanentRedirect,\n        // Remove `_forcePrefix` from public API\n        getPathname: getPathname\n    };\n}\nexports[\"default\"] = createSharedNavigationFns;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvbmF2aWdhdGlvbi9zaGFyZWQvY3JlYXRlU2hhcmVkTmF2aWdhdGlvbkZucy5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUVBQSw4Q0FBNkM7SUFBRUcsT0FBTztBQUFLLENBQUMsRUFBQztBQUU3RCxJQUFJQyw0QkFBNEJDLG1CQUFPQSxDQUFDO0FBQ3hDLElBQUlDLGFBQWFELG1CQUFPQSxDQUFDO0FBQ3pCLElBQUlFLFFBQVFGLG1CQUFPQSxDQUFDO0FBQ3BCLElBQUlHLFNBQVNILG1CQUFPQSxDQUFDO0FBQ3JCLElBQUlJLFVBQVVKLG1CQUFPQSxDQUFDO0FBQ3RCLElBQUlLLFdBQVdMLG1CQUFPQSxDQUFDO0FBQ3ZCLElBQUlNLFFBQVFOLG1CQUFPQSxDQUFDO0FBRXBCLFNBQVNPLGdCQUFpQkMsQ0FBQztJQUFJLE9BQU9BLEtBQUtBLEVBQUVDLFVBQVUsR0FBR0QsSUFBSTtRQUFFRSxTQUFTRjtJQUFFO0FBQUc7QUFFOUUsSUFBSUcsaUJBQWlCLFdBQVcsR0FBRUosZ0JBQWdCTDtBQUVsRDs7Q0FFQyxHQUNELFNBQVNVLDBCQUEwQkMsU0FBUyxFQUFFQyxPQUFPO0lBQ25ELE1BQU1DLFdBQVdaLE9BQU9hLG9CQUFvQixDQUFDRixXQUFXLENBQUM7SUFDekQ7UUFDRVIsTUFBTVcsc0JBQXNCLENBQUNGO0lBQy9CO0lBQ0EsTUFBTUcsWUFBWUgsU0FBU0csU0FBUztJQUVwQyx1RUFBdUU7SUFDdkUsOEVBQThFO0lBQzlFLDRFQUE0RTtJQUM1RSwyRUFBMkU7SUFDM0UsOEVBQThFO0lBQzlFLHdCQUF3QjtJQUN4QixNQUFNQyxpQkFBaUJKLFNBQVNLLFlBQVksQ0FBQ0MsSUFBSSxLQUFLLGVBQWVOLFNBQVNPLE9BQU8sSUFBSUM7SUFDekYsU0FBU0MsS0FBS0MsSUFBSSxFQUFFQyxHQUFHO1FBQ3JCLElBQUksRUFDRkMsSUFBSSxFQUNKQyxNQUFNLEVBQ04sR0FBR0MsTUFDSixHQUFHSjtRQUNKLElBQUlLLFVBQVVDLFFBQVFDO1FBQ3RCLElBQUksT0FBT0wsU0FBUyxVQUFVO1lBQzVCRyxXQUFXSCxLQUFLRyxRQUFRO1lBQ3hCRSxRQUFRTCxLQUFLSyxLQUFLO1lBQ2xCLGlDQUFpQztZQUNqQ0QsU0FBU0osS0FBS0ksTUFBTTtRQUN0QixPQUFPO1lBQ0xELFdBQVdIO1FBQ2I7UUFFQSxpQ0FBaUM7UUFDakMsTUFBTU0sZ0JBQWdCN0IsUUFBUThCLGlCQUFpQixDQUFDUDtRQUNoRCxNQUFNUSx1QkFBdUJ0QjtRQUM3QixNQUFNdUIsWUFBWWhDLFFBQVFpQyxTQUFTLENBQUNGLHdCQUF3QmpDLE1BQU1vQyxHQUFHLENBQUNILHdCQUF3QkE7UUFDOUYsTUFBTUksZ0JBQWdCTixnQkFBZ0JPLFlBQ3RDLGlDQUFpQztRQUNqQztZQUNFWixRQUFRQSxVQUFVUTtZQUNsQlQsTUFBTVQsYUFBYSxPQUFPWSxXQUFXO2dCQUNuQ0E7Z0JBQ0FDO1lBQ0Y7UUFDRixHQUFHSCxVQUFVLFFBQVFULGtCQUFrQkksYUFBYU87UUFDcEQsT0FBTyxXQUFXLEdBQUVuQixlQUFlRCxPQUFPLENBQUMrQixhQUFhLENBQUNwQyxTQUFTSyxPQUFPLEVBQUVYLDBCQUEwQjJDLE9BQU8sQ0FBQztZQUMzR2hCLEtBQUtBO1lBR0xpQixlQUFlNUIsU0FBUzRCLGFBQWE7WUFHckNoQixNQUFNLE9BQU9BLFNBQVMsV0FBVztnQkFDL0IsR0FBR0EsSUFBSTtnQkFDUEcsVUFBVVM7WUFDWixJQUFJQTtZQUNKWCxRQUFRQTtZQUNSZ0IsY0FBYzdCLFNBQVM2QixZQUFZO1lBSW5DQyxZQUFZMUIsa0JBQWtCYyxnQkFBZ0I7Z0JBQzVDWCxTQUFTUCxTQUFTTyxPQUFPLENBQUN3QixNQUFNLENBQUMsQ0FBQ0MsS0FBS0M7b0JBQ3JDLGlDQUFpQztvQkFDakNELEdBQUcsQ0FBQ0MsT0FBT0EsTUFBTSxDQUFDLEdBQUdBLE9BQU9MLGFBQWE7b0JBQ3pDLE9BQU9JO2dCQUNULEdBQUcsQ0FBQztnQkFDSmpCLFVBQVVVLFlBQ1YsaUNBQWlDO2dCQUNqQztvQkFDRVosUUFBUVE7b0JBQ1JULE1BQU1ULGFBQWEsT0FBTzt3QkFDeEJZO3dCQUNBRTtvQkFDRixJQUFJO3dCQUNGRjt3QkFDQUU7d0JBQ0FEO29CQUNGO2dCQUNGLEdBQUc7WUFDTCxJQUFJUjtRQUNOLEdBQUdNO0lBQ0w7SUFDQSxNQUFNb0IsY0FBYyxXQUFXLEdBQUUvQyxNQUFNZ0QsVUFBVSxDQUFDMUI7SUFDbEQsU0FBU2dCLFlBQVlXLElBQUksRUFBRSw2Q0FBNkMsR0FDeEVDLFlBQVk7UUFDVixNQUFNLEVBQ0p6QixJQUFJLEVBQ0pDLE1BQU0sRUFDUCxHQUFHdUI7UUFDSixJQUFJckI7UUFDSixJQUFJWixhQUFhLE1BQU07WUFDckIsSUFBSSxPQUFPUyxTQUFTLFVBQVU7Z0JBQzVCRyxXQUFXSCxLQUFLRyxRQUFRO2dCQUN4QixJQUFJSCxLQUFLSyxLQUFLLEVBQUU7b0JBQ2RGLFlBQVl4QixNQUFNK0MscUJBQXFCLENBQUMxQixLQUFLSyxLQUFLO2dCQUNwRDtZQUNGLE9BQU87Z0JBQ0xGLFdBQVdIO1lBQ2I7UUFDRixPQUFPO1lBQ0xHLFdBQVd4QixNQUFNZ0Qsd0JBQXdCLENBQUM7Z0JBQ3hDMUI7Z0JBQ0EsaUNBQWlDO2dCQUNqQyxHQUFHdEIsTUFBTWlELDZCQUE2QixDQUFDNUIsS0FBSztnQkFDNUMsaUNBQWlDO2dCQUNqQ1QsV0FBV0gsU0FBU0csU0FBUztZQUMvQjtRQUNGO1FBQ0EsT0FBT1osTUFBTWtELG1CQUFtQixDQUFDMUIsVUFBVUYsUUFBUWIsVUFDbkQsaUNBQWlDO1FBQ2pDb0MsS0FBS0gsTUFBTSxFQUFFSTtJQUNmO0lBQ0EsU0FBU0ssY0FBY0MsRUFBRTtRQUN2QixnRUFBZ0UsR0FDaEUsT0FBTyxTQUFTQyxXQUFXUixJQUFJO1lBQzdCLElBQUssSUFBSVMsT0FBT0MsVUFBVUMsTUFBTSxFQUFFakMsT0FBTyxJQUFJa0MsTUFBTUgsT0FBTyxJQUFJQSxPQUFPLElBQUksSUFBSUksT0FBTyxHQUFHQSxPQUFPSixNQUFNSSxPQUFRO2dCQUMxR25DLElBQUksQ0FBQ21DLE9BQU8sRUFBRSxHQUFHSCxTQUFTLENBQUNHLEtBQUs7WUFDbEM7WUFDQSxPQUFPTixHQUNQLDBFQUEwRTtZQUMxRWxCLFlBQVlXLE1BQU1BLEtBQUtILE1BQU0sR0FBR3pCLFlBQVlKLG9CQUFvQlU7UUFDbEU7SUFDRjtJQUNBLE1BQU1vQyxXQUFXUixjQUFjeEQsV0FBV2dFLFFBQVE7SUFDbEQsTUFBTUMsb0JBQW9CVCxjQUFjeEQsV0FBV2lFLGlCQUFpQjtJQUNwRSxPQUFPO1FBQ0wvRCxRQUFRWTtRQUNSUyxNQUFNeUI7UUFDTmdCO1FBQ0FDO1FBQ0Esd0NBQXdDO1FBQ3hDMUIsYUFBYUE7SUFDZjtBQUNGO0FBRUEzQyxrQkFBZSxHQUFHZSIsInNvdXJjZXMiOlsid2VicGFjazovL3NtZS1hbmFseXRpY2EtZG9jcy8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9kZXZlbG9wbWVudC9uYXZpZ2F0aW9uL3NoYXJlZC9jcmVhdGVTaGFyZWROYXZpZ2F0aW9uRm5zLmpzP2E4MDUiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgJ19fZXNNb2R1bGUnLCB7IHZhbHVlOiB0cnVlIH0pO1xuXG52YXIgX3JvbGx1cFBsdWdpbkJhYmVsSGVscGVycyA9IHJlcXVpcmUoJy4uLy4uL192aXJ0dWFsL19yb2xsdXBQbHVnaW5CYWJlbEhlbHBlcnMuanMnKTtcbnZhciBuYXZpZ2F0aW9uID0gcmVxdWlyZSgnbmV4dC9uYXZpZ2F0aW9uJyk7XG52YXIgUmVhY3QgPSByZXF1aXJlKCdyZWFjdCcpO1xudmFyIGNvbmZpZyA9IHJlcXVpcmUoJy4uLy4uL3JvdXRpbmcvY29uZmlnLmpzJyk7XG52YXIgdXRpbHMkMSA9IHJlcXVpcmUoJy4uLy4uL3NoYXJlZC91dGlscy5qcycpO1xudmFyIEJhc2VMaW5rID0gcmVxdWlyZSgnLi9CYXNlTGluay5qcycpO1xudmFyIHV0aWxzID0gcmVxdWlyZSgnLi91dGlscy5qcycpO1xuXG5mdW5jdGlvbiBfaW50ZXJvcERlZmF1bHQgKGUpIHsgcmV0dXJuIGUgJiYgZS5fX2VzTW9kdWxlID8gZSA6IHsgZGVmYXVsdDogZSB9OyB9XG5cbnZhciBSZWFjdF9fZGVmYXVsdCA9IC8qI19fUFVSRV9fKi9faW50ZXJvcERlZmF1bHQoUmVhY3QpO1xuXG4vKipcbiAqIFNoYXJlZCBpbXBsZW1lbnRhdGlvbnMgZm9yIGByZWFjdC1zZXJ2ZXJgIGFuZCBgcmVhY3QtY2xpZW50YFxuICovXG5mdW5jdGlvbiBjcmVhdGVTaGFyZWROYXZpZ2F0aW9uRm5zKGdldExvY2FsZSwgcm91dGluZykge1xuICBjb25zdCBjb25maWckMSA9IGNvbmZpZy5yZWNlaXZlUm91dGluZ0NvbmZpZyhyb3V0aW5nIHx8IHt9KTtcbiAge1xuICAgIHV0aWxzLnZhbGlkYXRlUmVjZWl2ZWRDb25maWcoY29uZmlnJDEpO1xuICB9XG4gIGNvbnN0IHBhdGhuYW1lcyA9IGNvbmZpZyQxLnBhdGhuYW1lcztcblxuICAvLyBUaGlzIGNvbWJpbmF0aW9uIHJlcXVpcmVzIHRoYXQgdGhlIGN1cnJlbnQgaG9zdCBpcyBrbm93biBpbiBvcmRlciB0b1xuICAvLyBjb21wdXRlIGEgY29ycmVjdCBwYXRobmFtZS4gU2luY2UgdGhhdCBjYW4gb25seSBiZSBhY2hpZXZlZCBieSByZWFkaW5nIGZyb21cbiAgLy8gaGVhZGVycywgdGhpcyB3b3VsZCBicmVhayBzdGF0aWMgcmVuZGVyaW5nLiBUaGVyZWZvcmUsIGFzIGEgd29ya2Fyb3VuZCB3ZVxuICAvLyBhbHdheXMgYWRkIGEgcHJlZml4IGluIHRoaXMgY2FzZSB0byBiZSBvbiB0aGUgc2FmZSBzaWRlLiBUaGUgZG93bnNpZGUgaXNcbiAgLy8gdGhhdCB0aGUgdXNlciBtaWdodCBnZXQgcmVkaXJlY3RlZCBhZ2FpbiBpZiB0aGUgbWlkZGxld2FyZSBkZXRlY3RzIHRoYXQgdGhlXG4gIC8vIHByZWZpeCBpcyBub3QgbmVlZGVkLlxuICBjb25zdCBmb3JjZVByZWZpeFNzciA9IGNvbmZpZyQxLmxvY2FsZVByZWZpeC5tb2RlID09PSAnYXMtbmVlZGVkJyAmJiBjb25maWckMS5kb21haW5zIHx8IHVuZGVmaW5lZDtcbiAgZnVuY3Rpb24gTGluayhfcmVmLCByZWYpIHtcbiAgICBsZXQge1xuICAgICAgaHJlZixcbiAgICAgIGxvY2FsZSxcbiAgICAgIC4uLnJlc3RcbiAgICB9ID0gX3JlZjtcbiAgICBsZXQgcGF0aG5hbWUsIHBhcmFtcywgcXVlcnk7XG4gICAgaWYgKHR5cGVvZiBocmVmID09PSAnb2JqZWN0Jykge1xuICAgICAgcGF0aG5hbWUgPSBocmVmLnBhdGhuYW1lO1xuICAgICAgcXVlcnkgPSBocmVmLnF1ZXJ5O1xuICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvciAtLSBUaGlzIGlzIG9rXG4gICAgICBwYXJhbXMgPSBocmVmLnBhcmFtcztcbiAgICB9IGVsc2Uge1xuICAgICAgcGF0aG5hbWUgPSBocmVmO1xuICAgIH1cblxuICAgIC8vIEB0cy1leHBlY3QtZXJyb3IgLS0gVGhpcyBpcyBva1xuICAgIGNvbnN0IGlzTG9jYWxpemFibGUgPSB1dGlscyQxLmlzTG9jYWxpemFibGVIcmVmKGhyZWYpO1xuICAgIGNvbnN0IGxvY2FsZVByb21pc2VPclZhbHVlID0gZ2V0TG9jYWxlKCk7XG4gICAgY29uc3QgY3VyTG9jYWxlID0gdXRpbHMkMS5pc1Byb21pc2UobG9jYWxlUHJvbWlzZU9yVmFsdWUpID8gUmVhY3QudXNlKGxvY2FsZVByb21pc2VPclZhbHVlKSA6IGxvY2FsZVByb21pc2VPclZhbHVlO1xuICAgIGNvbnN0IGZpbmFsUGF0aG5hbWUgPSBpc0xvY2FsaXphYmxlID8gZ2V0UGF0aG5hbWUoXG4gICAgLy8gQHRzLWV4cGVjdC1lcnJvciAtLSBUaGlzIGlzIG9rXG4gICAge1xuICAgICAgbG9jYWxlOiBsb2NhbGUgfHwgY3VyTG9jYWxlLFxuICAgICAgaHJlZjogcGF0aG5hbWVzID09IG51bGwgPyBwYXRobmFtZSA6IHtcbiAgICAgICAgcGF0aG5hbWUsXG4gICAgICAgIHBhcmFtc1xuICAgICAgfVxuICAgIH0sIGxvY2FsZSAhPSBudWxsIHx8IGZvcmNlUHJlZml4U3NyIHx8IHVuZGVmaW5lZCkgOiBwYXRobmFtZTtcbiAgICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0X19kZWZhdWx0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChCYXNlTGluay5kZWZhdWx0LCBfcm9sbHVwUGx1Z2luQmFiZWxIZWxwZXJzLmV4dGVuZHMoe1xuICAgICAgcmVmOiByZWZcbiAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3IgLS0gQXZhaWxhYmxlIGFmdGVyIHRoZSB2YWxpZGF0aW9uXG4gICAgICAsXG4gICAgICBkZWZhdWx0TG9jYWxlOiBjb25maWckMS5kZWZhdWx0TG9jYWxlXG4gICAgICAvLyBAdHMtZXhwZWN0LWVycm9yIC0tIFRoaXMgaXMgb2tcbiAgICAgICxcbiAgICAgIGhyZWY6IHR5cGVvZiBocmVmID09PSAnb2JqZWN0JyA/IHtcbiAgICAgICAgLi4uaHJlZixcbiAgICAgICAgcGF0aG5hbWU6IGZpbmFsUGF0aG5hbWVcbiAgICAgIH0gOiBmaW5hbFBhdGhuYW1lLFxuICAgICAgbG9jYWxlOiBsb2NhbGUsXG4gICAgICBsb2NhbGVDb29raWU6IGNvbmZpZyQxLmxvY2FsZUNvb2tpZVxuICAgICAgLy8gUHJvdmlkZSB0aGUgbWluaW1hbCByZWxldmFudCBpbmZvcm1hdGlvbiB0byB0aGUgY2xpZW50IHNpZGUgaW4gb3JkZXJcbiAgICAgIC8vIHRvIHBvdGVudGlhbGx5IHJlbW92ZSB0aGUgcHJlZml4IGluIGNhc2Ugb2YgdGhlIGBmb3JjZVByZWZpeFNzcmAgY2FzZVxuICAgICAgLFxuICAgICAgdW5wcmVmaXhlZDogZm9yY2VQcmVmaXhTc3IgJiYgaXNMb2NhbGl6YWJsZSA/IHtcbiAgICAgICAgZG9tYWluczogY29uZmlnJDEuZG9tYWlucy5yZWR1Y2UoKGFjYywgZG9tYWluKSA9PiB7XG4gICAgICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvciAtLSBUaGlzIGlzIG9rXG4gICAgICAgICAgYWNjW2RvbWFpbi5kb21haW5dID0gZG9tYWluLmRlZmF1bHRMb2NhbGU7XG4gICAgICAgICAgcmV0dXJuIGFjYztcbiAgICAgICAgfSwge30pLFxuICAgICAgICBwYXRobmFtZTogZ2V0UGF0aG5hbWUoXG4gICAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3IgLS0gVGhpcyBpcyBva1xuICAgICAgICB7XG4gICAgICAgICAgbG9jYWxlOiBjdXJMb2NhbGUsXG4gICAgICAgICAgaHJlZjogcGF0aG5hbWVzID09IG51bGwgPyB7XG4gICAgICAgICAgICBwYXRobmFtZSxcbiAgICAgICAgICAgIHF1ZXJ5XG4gICAgICAgICAgfSA6IHtcbiAgICAgICAgICAgIHBhdGhuYW1lLFxuICAgICAgICAgICAgcXVlcnksXG4gICAgICAgICAgICBwYXJhbXNcbiAgICAgICAgICB9XG4gICAgICAgIH0sIGZhbHNlKVxuICAgICAgfSA6IHVuZGVmaW5lZFxuICAgIH0sIHJlc3QpKTtcbiAgfVxuICBjb25zdCBMaW5rV2l0aFJlZiA9IC8qI19fUFVSRV9fKi9SZWFjdC5mb3J3YXJkUmVmKExpbmspO1xuICBmdW5jdGlvbiBnZXRQYXRobmFtZShhcmdzLCAvKiogQHByaXZhdGUgUmVtb3ZlZCBpbiB0eXBlcyByZXR1cm5lZCBiZWxvdyAqL1xuICBfZm9yY2VQcmVmaXgpIHtcbiAgICBjb25zdCB7XG4gICAgICBocmVmLFxuICAgICAgbG9jYWxlXG4gICAgfSA9IGFyZ3M7XG4gICAgbGV0IHBhdGhuYW1lO1xuICAgIGlmIChwYXRobmFtZXMgPT0gbnVsbCkge1xuICAgICAgaWYgKHR5cGVvZiBocmVmID09PSAnb2JqZWN0Jykge1xuICAgICAgICBwYXRobmFtZSA9IGhyZWYucGF0aG5hbWU7XG4gICAgICAgIGlmIChocmVmLnF1ZXJ5KSB7XG4gICAgICAgICAgcGF0aG5hbWUgKz0gdXRpbHMuc2VyaWFsaXplU2VhcmNoUGFyYW1zKGhyZWYucXVlcnkpO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBwYXRobmFtZSA9IGhyZWY7XG4gICAgICB9XG4gICAgfSBlbHNlIHtcbiAgICAgIHBhdGhuYW1lID0gdXRpbHMuY29tcGlsZUxvY2FsaXplZFBhdGhuYW1lKHtcbiAgICAgICAgbG9jYWxlLFxuICAgICAgICAvLyBAdHMtZXhwZWN0LWVycm9yIC0tIFRoaXMgaXMgb2tcbiAgICAgICAgLi4udXRpbHMubm9ybWFsaXplTmFtZU9yTmFtZVdpdGhQYXJhbXMoaHJlZiksXG4gICAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3IgLS0gVGhpcyBpcyBva1xuICAgICAgICBwYXRobmFtZXM6IGNvbmZpZyQxLnBhdGhuYW1lc1xuICAgICAgfSk7XG4gICAgfVxuICAgIHJldHVybiB1dGlscy5hcHBseVBhdGhuYW1lUHJlZml4KHBhdGhuYW1lLCBsb2NhbGUsIGNvbmZpZyQxLFxuICAgIC8vIEB0cy1leHBlY3QtZXJyb3IgLS0gVGhpcyBpcyBva1xuICAgIGFyZ3MuZG9tYWluLCBfZm9yY2VQcmVmaXgpO1xuICB9XG4gIGZ1bmN0aW9uIGdldFJlZGlyZWN0Rm4oZm4pIHtcbiAgICAvKiogQHNlZSBodHRwczovL25leHQtaW50bC5kZXYvZG9jcy9yb3V0aW5nL25hdmlnYXRpb24jcmVkaXJlY3QgKi9cbiAgICByZXR1cm4gZnVuY3Rpb24gcmVkaXJlY3RGbihhcmdzKSB7XG4gICAgICBmb3IgKHZhciBfbGVuID0gYXJndW1lbnRzLmxlbmd0aCwgcmVzdCA9IG5ldyBBcnJheShfbGVuID4gMSA/IF9sZW4gLSAxIDogMCksIF9rZXkgPSAxOyBfa2V5IDwgX2xlbjsgX2tleSsrKSB7XG4gICAgICAgIHJlc3RbX2tleSAtIDFdID0gYXJndW1lbnRzW19rZXldO1xuICAgICAgfVxuICAgICAgcmV0dXJuIGZuKFxuICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvciAtLSBXZSdyZSBmb3JjaW5nIHRoZSBwcmVmaXggd2hlbiBubyBkb21haW4gaXMgcHJvdmlkZWRcbiAgICAgIGdldFBhdGhuYW1lKGFyZ3MsIGFyZ3MuZG9tYWluID8gdW5kZWZpbmVkIDogZm9yY2VQcmVmaXhTc3IpLCAuLi5yZXN0KTtcbiAgICB9O1xuICB9XG4gIGNvbnN0IHJlZGlyZWN0ID0gZ2V0UmVkaXJlY3RGbihuYXZpZ2F0aW9uLnJlZGlyZWN0KTtcbiAgY29uc3QgcGVybWFuZW50UmVkaXJlY3QgPSBnZXRSZWRpcmVjdEZuKG5hdmlnYXRpb24ucGVybWFuZW50UmVkaXJlY3QpO1xuICByZXR1cm4ge1xuICAgIGNvbmZpZzogY29uZmlnJDEsXG4gICAgTGluazogTGlua1dpdGhSZWYsXG4gICAgcmVkaXJlY3QsXG4gICAgcGVybWFuZW50UmVkaXJlY3QsXG4gICAgLy8gUmVtb3ZlIGBfZm9yY2VQcmVmaXhgIGZyb20gcHVibGljIEFQSVxuICAgIGdldFBhdGhuYW1lOiBnZXRQYXRobmFtZVxuICB9O1xufVxuXG5leHBvcnRzLmRlZmF1bHQgPSBjcmVhdGVTaGFyZWROYXZpZ2F0aW9uRm5zO1xuIl0sIm5hbWVzIjpbIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZXhwb3J0cyIsInZhbHVlIiwiX3JvbGx1cFBsdWdpbkJhYmVsSGVscGVycyIsInJlcXVpcmUiLCJuYXZpZ2F0aW9uIiwiUmVhY3QiLCJjb25maWciLCJ1dGlscyQxIiwiQmFzZUxpbmsiLCJ1dGlscyIsIl9pbnRlcm9wRGVmYXVsdCIsImUiLCJfX2VzTW9kdWxlIiwiZGVmYXVsdCIsIlJlYWN0X19kZWZhdWx0IiwiY3JlYXRlU2hhcmVkTmF2aWdhdGlvbkZucyIsImdldExvY2FsZSIsInJvdXRpbmciLCJjb25maWckMSIsInJlY2VpdmVSb3V0aW5nQ29uZmlnIiwidmFsaWRhdGVSZWNlaXZlZENvbmZpZyIsInBhdGhuYW1lcyIsImZvcmNlUHJlZml4U3NyIiwibG9jYWxlUHJlZml4IiwibW9kZSIsImRvbWFpbnMiLCJ1bmRlZmluZWQiLCJMaW5rIiwiX3JlZiIsInJlZiIsImhyZWYiLCJsb2NhbGUiLCJyZXN0IiwicGF0aG5hbWUiLCJwYXJhbXMiLCJxdWVyeSIsImlzTG9jYWxpemFibGUiLCJpc0xvY2FsaXphYmxlSHJlZiIsImxvY2FsZVByb21pc2VPclZhbHVlIiwiY3VyTG9jYWxlIiwiaXNQcm9taXNlIiwidXNlIiwiZmluYWxQYXRobmFtZSIsImdldFBhdGhuYW1lIiwiY3JlYXRlRWxlbWVudCIsImV4dGVuZHMiLCJkZWZhdWx0TG9jYWxlIiwibG9jYWxlQ29va2llIiwidW5wcmVmaXhlZCIsInJlZHVjZSIsImFjYyIsImRvbWFpbiIsIkxpbmtXaXRoUmVmIiwiZm9yd2FyZFJlZiIsImFyZ3MiLCJfZm9yY2VQcmVmaXgiLCJzZXJpYWxpemVTZWFyY2hQYXJhbXMiLCJjb21waWxlTG9jYWxpemVkUGF0aG5hbWUiLCJub3JtYWxpemVOYW1lT3JOYW1lV2l0aFBhcmFtcyIsImFwcGx5UGF0aG5hbWVQcmVmaXgiLCJnZXRSZWRpcmVjdEZuIiwiZm4iLCJyZWRpcmVjdEZuIiwiX2xlbiIsImFyZ3VtZW50cyIsImxlbmd0aCIsIkFycmF5IiwiX2tleSIsInJlZGlyZWN0IiwicGVybWFuZW50UmVkaXJlY3QiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/shared/createSharedNavigationFns.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/shared/redirects.js":
/*!********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/shared/redirects.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\nvar utils = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\nfunction createRedirectFn(redirectFn) {\n    return function baseRedirect(params) {\n        const prefix = utils.getLocalePrefix(params.locale, params.localePrefix);\n        // This logic is considered legacy and is replaced by `applyPathnamePrefix`.\n        // We keep it this way for now for backwards compatibility.\n        const localizedPathname = params.localePrefix.mode === \"never\" || !utils.isLocalizableHref(params.pathname) ? params.pathname : utils.prefixPathname(prefix, params.pathname);\n        for(var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n            args[_key - 1] = arguments[_key];\n        }\n        return redirectFn(localizedPathname, ...args);\n    };\n}\nconst baseRedirect = createRedirectFn(navigation.redirect);\nconst basePermanentRedirect = createRedirectFn(navigation.permanentRedirect);\nexports.basePermanentRedirect = basePermanentRedirect;\nexports.baseRedirect = baseRedirect;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/shared/redirects.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/shared/syncLocaleCookie.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/shared/syncLocaleCookie.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar utils = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js\");\n/**\n * We have to keep the cookie value in sync as Next.js might\n * skip a request to the server due to its router cache.\n * See https://github.com/amannn/next-intl/issues/786.\n */ function syncLocaleCookie(localeCookie, pathname, locale, nextLocale) {\n    const isSwitchingLocale = nextLocale !== locale && nextLocale != null;\n    if (!localeCookie || !isSwitchingLocale || // Theoretical case, we always have a pathname in a real app,\n    // only not when running e.g. in a simulated test environment\n    !pathname) {\n        return;\n    }\n    const basePath = utils.getBasePath(pathname);\n    const hasBasePath = basePath !== \"\";\n    const defaultPath = hasBasePath ? basePath : \"/\";\n    const { name, ...rest } = localeCookie;\n    if (!rest.path) {\n        rest.path = defaultPath;\n    }\n    let localeCookieString = \"\".concat(name, \"=\").concat(nextLocale, \";\");\n    for (const [key, value] of Object.entries(rest)){\n        // Map object properties to cookie properties.\n        // Interestingly, `maxAge` corresponds to `max-age`,\n        // while `sameSite` corresponds to `SameSite`.\n        // Also, keys are case-insensitive.\n        const targetKey = key === \"maxAge\" ? \"max-age\" : key;\n        localeCookieString += \"\".concat(targetKey);\n        if (typeof value !== \"boolean\") {\n            localeCookieString += \"=\" + value;\n        }\n        // A trailing \";\" is allowed by browsers\n        localeCookieString += \";\";\n    }\n    // Note that writing to `document.cookie` doesn't overwrite all\n    // cookies, but only the ones referenced via the name here.\n    document.cookie = localeCookieString;\n}\nexports[\"default\"] = syncLocaleCookie;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/shared/syncLocaleCookie.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js":
/*!****************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/shared/utils.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar utils = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\n// Minor false positive: A route that has both optional and\n// required params will allow optional params.\n// For `Link`\n// For `getPathname` (hence also its consumers: `redirect`, `useRouter`, …)\nfunction normalizeNameOrNameWithParams(href) {\n    return typeof href === \"string\" ? {\n        pathname: href\n    } : href;\n}\nfunction serializeSearchParams(searchParams) {\n    function serializeValue(value) {\n        return String(value);\n    }\n    const urlSearchParams = new URLSearchParams();\n    for (const [key, value] of Object.entries(searchParams)){\n        if (Array.isArray(value)) {\n            value.forEach((cur)=>{\n                urlSearchParams.append(key, serializeValue(cur));\n            });\n        } else {\n            urlSearchParams.set(key, serializeValue(value));\n        }\n    }\n    return \"?\" + urlSearchParams.toString();\n}\nfunction compileLocalizedPathname(_ref) {\n    let { pathname, locale, params, pathnames, query } = _ref;\n    function getNamedPath(value) {\n        let namedPath = pathnames[value];\n        if (!namedPath) {\n            // Unknown pathnames\n            namedPath = value;\n        }\n        return namedPath;\n    }\n    function compilePath(namedPath) {\n        const template = typeof namedPath === \"string\" ? namedPath : namedPath[locale];\n        let compiled = template;\n        if (params) {\n            Object.entries(params).forEach((_ref2)=>{\n                let [key, value] = _ref2;\n                let regexp, replacer;\n                if (Array.isArray(value)) {\n                    regexp = \"(\\\\[)?\\\\[...\".concat(key, \"\\\\](\\\\])?\");\n                    replacer = value.map((v)=>String(v)).join(\"/\");\n                } else {\n                    regexp = \"\\\\[\".concat(key, \"\\\\]\");\n                    replacer = String(value);\n                }\n                compiled = compiled.replace(new RegExp(regexp, \"g\"), replacer);\n            });\n        }\n        // Clean up optional catch-all segments that were not replaced\n        compiled = compiled.replace(/\\[\\[\\.\\.\\..+\\]\\]/g, \"\");\n        compiled = utils.normalizeTrailingSlash(compiled);\n        if (compiled.includes(\"[\")) {\n            // Next.js throws anyway, therefore better provide a more helpful error message\n            throw new Error(\"Insufficient params provided for localized pathname.\\nTemplate: \".concat(template, \"\\nParams: \").concat(JSON.stringify(params)));\n        }\n        if (query) {\n            compiled += serializeSearchParams(query);\n        }\n        return compiled;\n    }\n    if (typeof pathname === \"string\") {\n        const namedPath = getNamedPath(pathname);\n        const compiled = compilePath(namedPath);\n        return compiled;\n    } else {\n        const { pathname: href, ...rest } = pathname;\n        const namedPath = getNamedPath(href);\n        const compiled = compilePath(namedPath);\n        const result = {\n            ...rest,\n            pathname: compiled\n        };\n        return result;\n    }\n}\nfunction getRoute(locale, pathname, pathnames) {\n    const sortedPathnames = utils.getSortedPathnames(Object.keys(pathnames));\n    const decoded = decodeURI(pathname);\n    for (const internalPathname of sortedPathnames){\n        const localizedPathnamesOrPathname = pathnames[internalPathname];\n        if (typeof localizedPathnamesOrPathname === \"string\") {\n            const localizedPathname = localizedPathnamesOrPathname;\n            if (utils.matchesPathname(localizedPathname, decoded)) {\n                return internalPathname;\n            }\n        } else {\n            if (utils.matchesPathname(localizedPathnamesOrPathname[locale], decoded)) {\n                return internalPathname;\n            }\n        }\n    }\n    return pathname;\n}\nfunction getBasePath(pathname) {\n    let windowPathname = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : window.location.pathname;\n    if (pathname === \"/\") {\n        return windowPathname;\n    } else {\n        return windowPathname.replace(pathname, \"\");\n    }\n}\nfunction applyPathnamePrefix(pathname, locale, routing, domain, force) {\n    const { mode } = routing.localePrefix;\n    let shouldPrefix;\n    if (force !== undefined) {\n        shouldPrefix = force;\n    } else if (utils.isLocalizableHref(pathname)) {\n        if (mode === \"always\") {\n            shouldPrefix = true;\n        } else if (mode === \"as-needed\") {\n            let defaultLocale = routing.defaultLocale;\n            if (routing.domains) {\n                const domainConfig = routing.domains.find((cur)=>cur.domain === domain);\n                if (domainConfig) {\n                    defaultLocale = domainConfig.defaultLocale;\n                } else {\n                    if (!domain) {\n                        console.error(\"You're using a routing configuration with `localePrefix: 'as-needed'` in combination with `domains`. In order to compute a correct pathname, you need to provide a `domain` parameter.\\n\\nSee: https://next-intl.dev/docs/routing#domains-localeprefix-asneeded\");\n                    }\n                }\n            }\n            shouldPrefix = defaultLocale !== locale;\n        }\n    }\n    return shouldPrefix ? utils.prefixPathname(utils.getLocalePrefix(locale, routing.localePrefix), pathname) : pathname;\n}\nfunction validateReceivedConfig(config) {\n    var _config$localePrefix;\n    if (((_config$localePrefix = config.localePrefix) === null || _config$localePrefix === void 0 ? void 0 : _config$localePrefix.mode) === \"as-needed\" && !(\"defaultLocale\" in config)) {\n        throw new Error(\"`localePrefix: 'as-needed' requires a `defaultLocale`.\");\n    }\n}\nexports.applyPathnamePrefix = applyPathnamePrefix;\nexports.compileLocalizedPathname = compileLocalizedPathname;\nexports.getBasePath = getBasePath;\nexports.getRoute = getRoute;\nexports.normalizeNameOrNameWithParams = normalizeNameOrNameWithParams;\nexports.serializeSearchParams = serializeSearchParams;\nexports.validateReceivedConfig = validateReceivedConfig;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvbmF2aWdhdGlvbi9zaGFyZWQvdXRpbHMuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFFQUEsOENBQTZDO0lBQUVHLE9BQU87QUFBSyxDQUFDLEVBQUM7QUFFN0QsSUFBSUMsUUFBUUMsbUJBQU9BLENBQUM7QUFFcEIsMkRBQTJEO0FBQzNELDhDQUE4QztBQUU5QyxhQUFhO0FBRWIsMkVBQTJFO0FBRTNFLFNBQVNDLDhCQUE4QkMsSUFBSTtJQUN6QyxPQUFPLE9BQU9BLFNBQVMsV0FBVztRQUNoQ0MsVUFBVUQ7SUFDWixJQUFJQTtBQUNOO0FBQ0EsU0FBU0Usc0JBQXNCQyxZQUFZO0lBQ3pDLFNBQVNDLGVBQWVSLEtBQUs7UUFDM0IsT0FBT1MsT0FBT1Q7SUFDaEI7SUFDQSxNQUFNVSxrQkFBa0IsSUFBSUM7SUFDNUIsS0FBSyxNQUFNLENBQUNDLEtBQUtaLE1BQU0sSUFBSUgsT0FBT2dCLE9BQU8sQ0FBQ04sY0FBZTtRQUN2RCxJQUFJTyxNQUFNQyxPQUFPLENBQUNmLFFBQVE7WUFDeEJBLE1BQU1nQixPQUFPLENBQUNDLENBQUFBO2dCQUNaUCxnQkFBZ0JRLE1BQU0sQ0FBQ04sS0FBS0osZUFBZVM7WUFDN0M7UUFDRixPQUFPO1lBQ0xQLGdCQUFnQlMsR0FBRyxDQUFDUCxLQUFLSixlQUFlUjtRQUMxQztJQUNGO0lBQ0EsT0FBTyxNQUFNVSxnQkFBZ0JVLFFBQVE7QUFDdkM7QUFDQSxTQUFTQyx5QkFBeUJDLElBQUk7SUFDcEMsSUFBSSxFQUNGakIsUUFBUSxFQUNSa0IsTUFBTSxFQUNOQyxNQUFNLEVBQ05DLFNBQVMsRUFDVEMsS0FBSyxFQUNOLEdBQUdKO0lBQ0osU0FBU0ssYUFBYTNCLEtBQUs7UUFDekIsSUFBSTRCLFlBQVlILFNBQVMsQ0FBQ3pCLE1BQU07UUFDaEMsSUFBSSxDQUFDNEIsV0FBVztZQUNkLG9CQUFvQjtZQUNwQkEsWUFBWTVCO1FBQ2Q7UUFDQSxPQUFPNEI7SUFDVDtJQUNBLFNBQVNDLFlBQVlELFNBQVM7UUFDNUIsTUFBTUUsV0FBVyxPQUFPRixjQUFjLFdBQVdBLFlBQVlBLFNBQVMsQ0FBQ0wsT0FBTztRQUM5RSxJQUFJUSxXQUFXRDtRQUNmLElBQUlOLFFBQVE7WUFDVjNCLE9BQU9nQixPQUFPLENBQUNXLFFBQVFSLE9BQU8sQ0FBQ2dCLENBQUFBO2dCQUM3QixJQUFJLENBQUNwQixLQUFLWixNQUFNLEdBQUdnQztnQkFDbkIsSUFBSUMsUUFBUUM7Z0JBQ1osSUFBSXBCLE1BQU1DLE9BQU8sQ0FBQ2YsUUFBUTtvQkFDeEJpQyxTQUFTLGVBQWVFLE1BQU0sQ0FBQ3ZCLEtBQUs7b0JBQ3BDc0IsV0FBV2xDLE1BQU1vQyxHQUFHLENBQUNDLENBQUFBLElBQUs1QixPQUFPNEIsSUFBSUMsSUFBSSxDQUFDO2dCQUM1QyxPQUFPO29CQUNMTCxTQUFTLE1BQU1FLE1BQU0sQ0FBQ3ZCLEtBQUs7b0JBQzNCc0IsV0FBV3pCLE9BQU9UO2dCQUNwQjtnQkFDQStCLFdBQVdBLFNBQVNRLE9BQU8sQ0FBQyxJQUFJQyxPQUFPUCxRQUFRLE1BQU1DO1lBQ3ZEO1FBQ0Y7UUFFQSw4REFBOEQ7UUFDOURILFdBQVdBLFNBQVNRLE9BQU8sQ0FBQyxxQkFBcUI7UUFDakRSLFdBQVc5QixNQUFNd0Msc0JBQXNCLENBQUNWO1FBQ3hDLElBQUlBLFNBQVNXLFFBQVEsQ0FBQyxNQUFNO1lBQzFCLCtFQUErRTtZQUMvRSxNQUFNLElBQUlDLE1BQU0sbUVBQW1FUixNQUFNLENBQUNMLFVBQVUsY0FBY0ssTUFBTSxDQUFDUyxLQUFLQyxTQUFTLENBQUNyQjtRQUMxSTtRQUNBLElBQUlFLE9BQU87WUFDVEssWUFBWXpCLHNCQUFzQm9CO1FBQ3BDO1FBQ0EsT0FBT0s7SUFDVDtJQUNBLElBQUksT0FBTzFCLGFBQWEsVUFBVTtRQUNoQyxNQUFNdUIsWUFBWUQsYUFBYXRCO1FBQy9CLE1BQU0wQixXQUFXRixZQUFZRDtRQUM3QixPQUFPRztJQUNULE9BQU87UUFDTCxNQUFNLEVBQ0oxQixVQUFVRCxJQUFJLEVBQ2QsR0FBRzBDLE1BQ0osR0FBR3pDO1FBQ0osTUFBTXVCLFlBQVlELGFBQWF2QjtRQUMvQixNQUFNMkIsV0FBV0YsWUFBWUQ7UUFDN0IsTUFBTW1CLFNBQVM7WUFDYixHQUFHRCxJQUFJO1lBQ1B6QyxVQUFVMEI7UUFDWjtRQUNBLE9BQU9nQjtJQUNUO0FBQ0Y7QUFDQSxTQUFTQyxTQUFTekIsTUFBTSxFQUFFbEIsUUFBUSxFQUFFb0IsU0FBUztJQUMzQyxNQUFNd0Isa0JBQWtCaEQsTUFBTWlELGtCQUFrQixDQUFDckQsT0FBT3NELElBQUksQ0FBQzFCO0lBQzdELE1BQU0yQixVQUFVQyxVQUFVaEQ7SUFDMUIsS0FBSyxNQUFNaUQsb0JBQW9CTCxnQkFBaUI7UUFDOUMsTUFBTU0sK0JBQStCOUIsU0FBUyxDQUFDNkIsaUJBQWlCO1FBQ2hFLElBQUksT0FBT0MsaUNBQWlDLFVBQVU7WUFDcEQsTUFBTUMsb0JBQW9CRDtZQUMxQixJQUFJdEQsTUFBTXdELGVBQWUsQ0FBQ0QsbUJBQW1CSixVQUFVO2dCQUNyRCxPQUFPRTtZQUNUO1FBQ0YsT0FBTztZQUNMLElBQUlyRCxNQUFNd0QsZUFBZSxDQUFDRiw0QkFBNEIsQ0FBQ2hDLE9BQU8sRUFBRTZCLFVBQVU7Z0JBQ3hFLE9BQU9FO1lBQ1Q7UUFDRjtJQUNGO0lBQ0EsT0FBT2pEO0FBQ1Q7QUFDQSxTQUFTcUQsWUFBWXJELFFBQVE7SUFDM0IsSUFBSXNELGlCQUFpQkMsVUFBVUMsTUFBTSxHQUFHLEtBQUtELFNBQVMsQ0FBQyxFQUFFLEtBQUtFLFlBQVlGLFNBQVMsQ0FBQyxFQUFFLEdBQUdHLE9BQU9DLFFBQVEsQ0FBQzNELFFBQVE7SUFDakgsSUFBSUEsYUFBYSxLQUFLO1FBQ3BCLE9BQU9zRDtJQUNULE9BQU87UUFDTCxPQUFPQSxlQUFlcEIsT0FBTyxDQUFDbEMsVUFBVTtJQUMxQztBQUNGO0FBQ0EsU0FBUzRELG9CQUFvQjVELFFBQVEsRUFBRWtCLE1BQU0sRUFBRTJDLE9BQU8sRUFBRUMsTUFBTSxFQUFFQyxLQUFLO0lBQ25FLE1BQU0sRUFDSkMsSUFBSSxFQUNMLEdBQUdILFFBQVFJLFlBQVk7SUFDeEIsSUFBSUM7SUFDSixJQUFJSCxVQUFVTixXQUFXO1FBQ3ZCUyxlQUFlSDtJQUNqQixPQUFPLElBQUluRSxNQUFNdUUsaUJBQWlCLENBQUNuRSxXQUFXO1FBQzVDLElBQUlnRSxTQUFTLFVBQVU7WUFDckJFLGVBQWU7UUFDakIsT0FBTyxJQUFJRixTQUFTLGFBQWE7WUFDL0IsSUFBSUksZ0JBQWdCUCxRQUFRTyxhQUFhO1lBQ3pDLElBQUlQLFFBQVFRLE9BQU8sRUFBRTtnQkFDbkIsTUFBTUMsZUFBZVQsUUFBUVEsT0FBTyxDQUFDRSxJQUFJLENBQUMzRCxDQUFBQSxNQUFPQSxJQUFJa0QsTUFBTSxLQUFLQTtnQkFDaEUsSUFBSVEsY0FBYztvQkFDaEJGLGdCQUFnQkUsYUFBYUYsYUFBYTtnQkFDNUMsT0FBTztvQkFDTCxJQUFJLENBQUNOLFFBQVE7d0JBQ1hVLFFBQVFDLEtBQUssQ0FBQztvQkFDaEI7Z0JBQ0Y7WUFDRjtZQUNBUCxlQUFlRSxrQkFBa0JsRDtRQUNuQztJQUNGO0lBQ0EsT0FBT2dELGVBQWV0RSxNQUFNOEUsY0FBYyxDQUFDOUUsTUFBTStFLGVBQWUsQ0FBQ3pELFFBQVEyQyxRQUFRSSxZQUFZLEdBQUdqRSxZQUFZQTtBQUM5RztBQUNBLFNBQVM0RSx1QkFBdUJDLE1BQU07SUFDcEMsSUFBSUM7SUFDSixJQUFJLENBQUMsQ0FBQ0EsdUJBQXVCRCxPQUFPWixZQUFZLE1BQU0sUUFBUWEseUJBQXlCLEtBQUssSUFBSSxLQUFLLElBQUlBLHFCQUFxQmQsSUFBSSxNQUFNLGVBQWUsQ0FBRSxvQkFBbUJhLE1BQUssR0FBSTtRQUNuTCxNQUFNLElBQUl2QyxNQUFNO0lBQ2xCO0FBQ0Y7QUFFQTVDLDJCQUEyQixHQUFHa0U7QUFDOUJsRSxnQ0FBZ0MsR0FBR3NCO0FBQ25DdEIsbUJBQW1CLEdBQUcyRDtBQUN0QjNELGdCQUFnQixHQUFHaUQ7QUFDbkJqRCxxQ0FBcUMsR0FBR0k7QUFDeENKLDZCQUE2QixHQUFHTztBQUNoQ1AsOEJBQThCLEdBQUdrRiIsInNvdXJjZXMiOlsid2VicGFjazovL3NtZS1hbmFseXRpY2EtZG9jcy8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9kZXZlbG9wbWVudC9uYXZpZ2F0aW9uL3NoYXJlZC91dGlscy5qcz9kNTZjIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICdfX2VzTW9kdWxlJywgeyB2YWx1ZTogdHJ1ZSB9KTtcblxudmFyIHV0aWxzID0gcmVxdWlyZSgnLi4vLi4vc2hhcmVkL3V0aWxzLmpzJyk7XG5cbi8vIE1pbm9yIGZhbHNlIHBvc2l0aXZlOiBBIHJvdXRlIHRoYXQgaGFzIGJvdGggb3B0aW9uYWwgYW5kXG4vLyByZXF1aXJlZCBwYXJhbXMgd2lsbCBhbGxvdyBvcHRpb25hbCBwYXJhbXMuXG5cbi8vIEZvciBgTGlua2BcblxuLy8gRm9yIGBnZXRQYXRobmFtZWAgKGhlbmNlIGFsc28gaXRzIGNvbnN1bWVyczogYHJlZGlyZWN0YCwgYHVzZVJvdXRlcmAsIOKApilcblxuZnVuY3Rpb24gbm9ybWFsaXplTmFtZU9yTmFtZVdpdGhQYXJhbXMoaHJlZikge1xuICByZXR1cm4gdHlwZW9mIGhyZWYgPT09ICdzdHJpbmcnID8ge1xuICAgIHBhdGhuYW1lOiBocmVmXG4gIH0gOiBocmVmO1xufVxuZnVuY3Rpb24gc2VyaWFsaXplU2VhcmNoUGFyYW1zKHNlYXJjaFBhcmFtcykge1xuICBmdW5jdGlvbiBzZXJpYWxpemVWYWx1ZSh2YWx1ZSkge1xuICAgIHJldHVybiBTdHJpbmcodmFsdWUpO1xuICB9XG4gIGNvbnN0IHVybFNlYXJjaFBhcmFtcyA9IG5ldyBVUkxTZWFyY2hQYXJhbXMoKTtcbiAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMoc2VhcmNoUGFyYW1zKSkge1xuICAgIGlmIChBcnJheS5pc0FycmF5KHZhbHVlKSkge1xuICAgICAgdmFsdWUuZm9yRWFjaChjdXIgPT4ge1xuICAgICAgICB1cmxTZWFyY2hQYXJhbXMuYXBwZW5kKGtleSwgc2VyaWFsaXplVmFsdWUoY3VyKSk7XG4gICAgICB9KTtcbiAgICB9IGVsc2Uge1xuICAgICAgdXJsU2VhcmNoUGFyYW1zLnNldChrZXksIHNlcmlhbGl6ZVZhbHVlKHZhbHVlKSk7XG4gICAgfVxuICB9XG4gIHJldHVybiAnPycgKyB1cmxTZWFyY2hQYXJhbXMudG9TdHJpbmcoKTtcbn1cbmZ1bmN0aW9uIGNvbXBpbGVMb2NhbGl6ZWRQYXRobmFtZShfcmVmKSB7XG4gIGxldCB7XG4gICAgcGF0aG5hbWUsXG4gICAgbG9jYWxlLFxuICAgIHBhcmFtcyxcbiAgICBwYXRobmFtZXMsXG4gICAgcXVlcnlcbiAgfSA9IF9yZWY7XG4gIGZ1bmN0aW9uIGdldE5hbWVkUGF0aCh2YWx1ZSkge1xuICAgIGxldCBuYW1lZFBhdGggPSBwYXRobmFtZXNbdmFsdWVdO1xuICAgIGlmICghbmFtZWRQYXRoKSB7XG4gICAgICAvLyBVbmtub3duIHBhdGhuYW1lc1xuICAgICAgbmFtZWRQYXRoID0gdmFsdWU7XG4gICAgfVxuICAgIHJldHVybiBuYW1lZFBhdGg7XG4gIH1cbiAgZnVuY3Rpb24gY29tcGlsZVBhdGgobmFtZWRQYXRoKSB7XG4gICAgY29uc3QgdGVtcGxhdGUgPSB0eXBlb2YgbmFtZWRQYXRoID09PSAnc3RyaW5nJyA/IG5hbWVkUGF0aCA6IG5hbWVkUGF0aFtsb2NhbGVdO1xuICAgIGxldCBjb21waWxlZCA9IHRlbXBsYXRlO1xuICAgIGlmIChwYXJhbXMpIHtcbiAgICAgIE9iamVjdC5lbnRyaWVzKHBhcmFtcykuZm9yRWFjaChfcmVmMiA9PiB7XG4gICAgICAgIGxldCBba2V5LCB2YWx1ZV0gPSBfcmVmMjtcbiAgICAgICAgbGV0IHJlZ2V4cCwgcmVwbGFjZXI7XG4gICAgICAgIGlmIChBcnJheS5pc0FycmF5KHZhbHVlKSkge1xuICAgICAgICAgIHJlZ2V4cCA9IFwiKFxcXFxbKT9cXFxcWy4uLlwiLmNvbmNhdChrZXksIFwiXFxcXF0oXFxcXF0pP1wiKTtcbiAgICAgICAgICByZXBsYWNlciA9IHZhbHVlLm1hcCh2ID0+IFN0cmluZyh2KSkuam9pbignLycpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHJlZ2V4cCA9IFwiXFxcXFtcIi5jb25jYXQoa2V5LCBcIlxcXFxdXCIpO1xuICAgICAgICAgIHJlcGxhY2VyID0gU3RyaW5nKHZhbHVlKTtcbiAgICAgICAgfVxuICAgICAgICBjb21waWxlZCA9IGNvbXBpbGVkLnJlcGxhY2UobmV3IFJlZ0V4cChyZWdleHAsICdnJyksIHJlcGxhY2VyKTtcbiAgICAgIH0pO1xuICAgIH1cblxuICAgIC8vIENsZWFuIHVwIG9wdGlvbmFsIGNhdGNoLWFsbCBzZWdtZW50cyB0aGF0IHdlcmUgbm90IHJlcGxhY2VkXG4gICAgY29tcGlsZWQgPSBjb21waWxlZC5yZXBsYWNlKC9cXFtcXFtcXC5cXC5cXC4uK1xcXVxcXS9nLCAnJyk7XG4gICAgY29tcGlsZWQgPSB1dGlscy5ub3JtYWxpemVUcmFpbGluZ1NsYXNoKGNvbXBpbGVkKTtcbiAgICBpZiAoY29tcGlsZWQuaW5jbHVkZXMoJ1snKSkge1xuICAgICAgLy8gTmV4dC5qcyB0aHJvd3MgYW55d2F5LCB0aGVyZWZvcmUgYmV0dGVyIHByb3ZpZGUgYSBtb3JlIGhlbHBmdWwgZXJyb3IgbWVzc2FnZVxuICAgICAgdGhyb3cgbmV3IEVycm9yKFwiSW5zdWZmaWNpZW50IHBhcmFtcyBwcm92aWRlZCBmb3IgbG9jYWxpemVkIHBhdGhuYW1lLlxcblRlbXBsYXRlOiBcIi5jb25jYXQodGVtcGxhdGUsIFwiXFxuUGFyYW1zOiBcIikuY29uY2F0KEpTT04uc3RyaW5naWZ5KHBhcmFtcykpKTtcbiAgICB9XG4gICAgaWYgKHF1ZXJ5KSB7XG4gICAgICBjb21waWxlZCArPSBzZXJpYWxpemVTZWFyY2hQYXJhbXMocXVlcnkpO1xuICAgIH1cbiAgICByZXR1cm4gY29tcGlsZWQ7XG4gIH1cbiAgaWYgKHR5cGVvZiBwYXRobmFtZSA9PT0gJ3N0cmluZycpIHtcbiAgICBjb25zdCBuYW1lZFBhdGggPSBnZXROYW1lZFBhdGgocGF0aG5hbWUpO1xuICAgIGNvbnN0IGNvbXBpbGVkID0gY29tcGlsZVBhdGgobmFtZWRQYXRoKTtcbiAgICByZXR1cm4gY29tcGlsZWQ7XG4gIH0gZWxzZSB7XG4gICAgY29uc3Qge1xuICAgICAgcGF0aG5hbWU6IGhyZWYsXG4gICAgICAuLi5yZXN0XG4gICAgfSA9IHBhdGhuYW1lO1xuICAgIGNvbnN0IG5hbWVkUGF0aCA9IGdldE5hbWVkUGF0aChocmVmKTtcbiAgICBjb25zdCBjb21waWxlZCA9IGNvbXBpbGVQYXRoKG5hbWVkUGF0aCk7XG4gICAgY29uc3QgcmVzdWx0ID0ge1xuICAgICAgLi4ucmVzdCxcbiAgICAgIHBhdGhuYW1lOiBjb21waWxlZFxuICAgIH07XG4gICAgcmV0dXJuIHJlc3VsdDtcbiAgfVxufVxuZnVuY3Rpb24gZ2V0Um91dGUobG9jYWxlLCBwYXRobmFtZSwgcGF0aG5hbWVzKSB7XG4gIGNvbnN0IHNvcnRlZFBhdGhuYW1lcyA9IHV0aWxzLmdldFNvcnRlZFBhdGhuYW1lcyhPYmplY3Qua2V5cyhwYXRobmFtZXMpKTtcbiAgY29uc3QgZGVjb2RlZCA9IGRlY29kZVVSSShwYXRobmFtZSk7XG4gIGZvciAoY29uc3QgaW50ZXJuYWxQYXRobmFtZSBvZiBzb3J0ZWRQYXRobmFtZXMpIHtcbiAgICBjb25zdCBsb2NhbGl6ZWRQYXRobmFtZXNPclBhdGhuYW1lID0gcGF0aG5hbWVzW2ludGVybmFsUGF0aG5hbWVdO1xuICAgIGlmICh0eXBlb2YgbG9jYWxpemVkUGF0aG5hbWVzT3JQYXRobmFtZSA9PT0gJ3N0cmluZycpIHtcbiAgICAgIGNvbnN0IGxvY2FsaXplZFBhdGhuYW1lID0gbG9jYWxpemVkUGF0aG5hbWVzT3JQYXRobmFtZTtcbiAgICAgIGlmICh1dGlscy5tYXRjaGVzUGF0aG5hbWUobG9jYWxpemVkUGF0aG5hbWUsIGRlY29kZWQpKSB7XG4gICAgICAgIHJldHVybiBpbnRlcm5hbFBhdGhuYW1lO1xuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICBpZiAodXRpbHMubWF0Y2hlc1BhdGhuYW1lKGxvY2FsaXplZFBhdGhuYW1lc09yUGF0aG5hbWVbbG9jYWxlXSwgZGVjb2RlZCkpIHtcbiAgICAgICAgcmV0dXJuIGludGVybmFsUGF0aG5hbWU7XG4gICAgICB9XG4gICAgfVxuICB9XG4gIHJldHVybiBwYXRobmFtZTtcbn1cbmZ1bmN0aW9uIGdldEJhc2VQYXRoKHBhdGhuYW1lKSB7XG4gIGxldCB3aW5kb3dQYXRobmFtZSA9IGFyZ3VtZW50cy5sZW5ndGggPiAxICYmIGFyZ3VtZW50c1sxXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzFdIDogd2luZG93LmxvY2F0aW9uLnBhdGhuYW1lO1xuICBpZiAocGF0aG5hbWUgPT09ICcvJykge1xuICAgIHJldHVybiB3aW5kb3dQYXRobmFtZTtcbiAgfSBlbHNlIHtcbiAgICByZXR1cm4gd2luZG93UGF0aG5hbWUucmVwbGFjZShwYXRobmFtZSwgJycpO1xuICB9XG59XG5mdW5jdGlvbiBhcHBseVBhdGhuYW1lUHJlZml4KHBhdGhuYW1lLCBsb2NhbGUsIHJvdXRpbmcsIGRvbWFpbiwgZm9yY2UpIHtcbiAgY29uc3Qge1xuICAgIG1vZGVcbiAgfSA9IHJvdXRpbmcubG9jYWxlUHJlZml4O1xuICBsZXQgc2hvdWxkUHJlZml4O1xuICBpZiAoZm9yY2UgIT09IHVuZGVmaW5lZCkge1xuICAgIHNob3VsZFByZWZpeCA9IGZvcmNlO1xuICB9IGVsc2UgaWYgKHV0aWxzLmlzTG9jYWxpemFibGVIcmVmKHBhdGhuYW1lKSkge1xuICAgIGlmIChtb2RlID09PSAnYWx3YXlzJykge1xuICAgICAgc2hvdWxkUHJlZml4ID0gdHJ1ZTtcbiAgICB9IGVsc2UgaWYgKG1vZGUgPT09ICdhcy1uZWVkZWQnKSB7XG4gICAgICBsZXQgZGVmYXVsdExvY2FsZSA9IHJvdXRpbmcuZGVmYXVsdExvY2FsZTtcbiAgICAgIGlmIChyb3V0aW5nLmRvbWFpbnMpIHtcbiAgICAgICAgY29uc3QgZG9tYWluQ29uZmlnID0gcm91dGluZy5kb21haW5zLmZpbmQoY3VyID0+IGN1ci5kb21haW4gPT09IGRvbWFpbik7XG4gICAgICAgIGlmIChkb21haW5Db25maWcpIHtcbiAgICAgICAgICBkZWZhdWx0TG9jYWxlID0gZG9tYWluQ29uZmlnLmRlZmF1bHRMb2NhbGU7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgaWYgKCFkb21haW4pIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJZb3UncmUgdXNpbmcgYSByb3V0aW5nIGNvbmZpZ3VyYXRpb24gd2l0aCBgbG9jYWxlUHJlZml4OiAnYXMtbmVlZGVkJ2AgaW4gY29tYmluYXRpb24gd2l0aCBgZG9tYWluc2AuIEluIG9yZGVyIHRvIGNvbXB1dGUgYSBjb3JyZWN0IHBhdGhuYW1lLCB5b3UgbmVlZCB0byBwcm92aWRlIGEgYGRvbWFpbmAgcGFyYW1ldGVyLlxcblxcblNlZTogaHR0cHM6Ly9uZXh0LWludGwuZGV2L2RvY3Mvcm91dGluZyNkb21haW5zLWxvY2FsZXByZWZpeC1hc25lZWRlZFwiKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIHNob3VsZFByZWZpeCA9IGRlZmF1bHRMb2NhbGUgIT09IGxvY2FsZTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHNob3VsZFByZWZpeCA/IHV0aWxzLnByZWZpeFBhdGhuYW1lKHV0aWxzLmdldExvY2FsZVByZWZpeChsb2NhbGUsIHJvdXRpbmcubG9jYWxlUHJlZml4KSwgcGF0aG5hbWUpIDogcGF0aG5hbWU7XG59XG5mdW5jdGlvbiB2YWxpZGF0ZVJlY2VpdmVkQ29uZmlnKGNvbmZpZykge1xuICB2YXIgX2NvbmZpZyRsb2NhbGVQcmVmaXg7XG4gIGlmICgoKF9jb25maWckbG9jYWxlUHJlZml4ID0gY29uZmlnLmxvY2FsZVByZWZpeCkgPT09IG51bGwgfHwgX2NvbmZpZyRsb2NhbGVQcmVmaXggPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9jb25maWckbG9jYWxlUHJlZml4Lm1vZGUpID09PSAnYXMtbmVlZGVkJyAmJiAhKCdkZWZhdWx0TG9jYWxlJyBpbiBjb25maWcpKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFwiYGxvY2FsZVByZWZpeDogJ2FzLW5lZWRlZCcgcmVxdWlyZXMgYSBgZGVmYXVsdExvY2FsZWAuXCIpO1xuICB9XG59XG5cbmV4cG9ydHMuYXBwbHlQYXRobmFtZVByZWZpeCA9IGFwcGx5UGF0aG5hbWVQcmVmaXg7XG5leHBvcnRzLmNvbXBpbGVMb2NhbGl6ZWRQYXRobmFtZSA9IGNvbXBpbGVMb2NhbGl6ZWRQYXRobmFtZTtcbmV4cG9ydHMuZ2V0QmFzZVBhdGggPSBnZXRCYXNlUGF0aDtcbmV4cG9ydHMuZ2V0Um91dGUgPSBnZXRSb3V0ZTtcbmV4cG9ydHMubm9ybWFsaXplTmFtZU9yTmFtZVdpdGhQYXJhbXMgPSBub3JtYWxpemVOYW1lT3JOYW1lV2l0aFBhcmFtcztcbmV4cG9ydHMuc2VyaWFsaXplU2VhcmNoUGFyYW1zID0gc2VyaWFsaXplU2VhcmNoUGFyYW1zO1xuZXhwb3J0cy52YWxpZGF0ZVJlY2VpdmVkQ29uZmlnID0gdmFsaWRhdGVSZWNlaXZlZENvbmZpZztcbiJdLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsInV0aWxzIiwicmVxdWlyZSIsIm5vcm1hbGl6ZU5hbWVPck5hbWVXaXRoUGFyYW1zIiwiaHJlZiIsInBhdGhuYW1lIiwic2VyaWFsaXplU2VhcmNoUGFyYW1zIiwic2VhcmNoUGFyYW1zIiwic2VyaWFsaXplVmFsdWUiLCJTdHJpbmciLCJ1cmxTZWFyY2hQYXJhbXMiLCJVUkxTZWFyY2hQYXJhbXMiLCJrZXkiLCJlbnRyaWVzIiwiQXJyYXkiLCJpc0FycmF5IiwiZm9yRWFjaCIsImN1ciIsImFwcGVuZCIsInNldCIsInRvU3RyaW5nIiwiY29tcGlsZUxvY2FsaXplZFBhdGhuYW1lIiwiX3JlZiIsImxvY2FsZSIsInBhcmFtcyIsInBhdGhuYW1lcyIsInF1ZXJ5IiwiZ2V0TmFtZWRQYXRoIiwibmFtZWRQYXRoIiwiY29tcGlsZVBhdGgiLCJ0ZW1wbGF0ZSIsImNvbXBpbGVkIiwiX3JlZjIiLCJyZWdleHAiLCJyZXBsYWNlciIsImNvbmNhdCIsIm1hcCIsInYiLCJqb2luIiwicmVwbGFjZSIsIlJlZ0V4cCIsIm5vcm1hbGl6ZVRyYWlsaW5nU2xhc2giLCJpbmNsdWRlcyIsIkVycm9yIiwiSlNPTiIsInN0cmluZ2lmeSIsInJlc3QiLCJyZXN1bHQiLCJnZXRSb3V0ZSIsInNvcnRlZFBhdGhuYW1lcyIsImdldFNvcnRlZFBhdGhuYW1lcyIsImtleXMiLCJkZWNvZGVkIiwiZGVjb2RlVVJJIiwiaW50ZXJuYWxQYXRobmFtZSIsImxvY2FsaXplZFBhdGhuYW1lc09yUGF0aG5hbWUiLCJsb2NhbGl6ZWRQYXRobmFtZSIsIm1hdGNoZXNQYXRobmFtZSIsImdldEJhc2VQYXRoIiwid2luZG93UGF0aG5hbWUiLCJhcmd1bWVudHMiLCJsZW5ndGgiLCJ1bmRlZmluZWQiLCJ3aW5kb3ciLCJsb2NhdGlvbiIsImFwcGx5UGF0aG5hbWVQcmVmaXgiLCJyb3V0aW5nIiwiZG9tYWluIiwiZm9yY2UiLCJtb2RlIiwibG9jYWxlUHJlZml4Iiwic2hvdWxkUHJlZml4IiwiaXNMb2NhbGl6YWJsZUhyZWYiLCJkZWZhdWx0TG9jYWxlIiwiZG9tYWlucyIsImRvbWFpbkNvbmZpZyIsImZpbmQiLCJjb25zb2xlIiwiZXJyb3IiLCJwcmVmaXhQYXRobmFtZSIsImdldExvY2FsZVByZWZpeCIsInZhbGlkYXRlUmVjZWl2ZWRDb25maWciLCJjb25maWciLCJfY29uZmlnJGxvY2FsZVByZWZpeCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/react-client/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/react-client/index.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar useIntl = __webpack_require__(/*! use-intl */ \"(ssr)/./node_modules/use-intl/dist/index.js\");\n/**\n * This is the main entry file when non-'react-server'\n * environments import from 'next-intl'.\n *\n * Maintainer notes:\n * - Make sure this mirrors the API from 'react-server'.\n * - Make sure everything exported from this module is\n *   supported in all Next.js versions that are supported.\n */ // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\nfunction callHook(name, hook) {\n    return function() {\n        try {\n            return hook(...arguments);\n        } catch (_unused) {\n            throw new Error(\"Failed to call `\".concat(name, \"` because the context from `NextIntlClientProvider` was not found.\\n\\nThis can happen because:\\n1) You intended to render this component as a Server Component, the render\\n   failed, and therefore React attempted to render the component on the client\\n   instead. If this is the case, check the console for server errors.\\n2) You intended to render this component on the client side, but no context was found.\\n   Learn more about this error here: https://next-intl.dev/docs/environments/server-client-components#missing-context\"));\n        }\n    };\n}\nconst useTranslations = callHook(\"useTranslations\", useIntl.useTranslations);\nconst useFormatter = callHook(\"useFormatter\", useIntl.useFormatter);\nexports.useFormatter = useFormatter;\nexports.useTranslations = useTranslations;\nObject.keys(useIntl).forEach(function(k) {\n    if (k !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {\n        enumerable: true,\n        get: function() {\n            return useIntl[k];\n        }\n    });\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/react-client/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js":
/*!***************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/react-client/useLocale.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\nvar _useLocale = __webpack_require__(/*! use-intl/_useLocale */ \"(ssr)/./node_modules/use-intl/dist/_useLocale.js\");\nvar constants = __webpack_require__(/*! ../shared/constants.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/constants.js\");\nlet hasWarnedForParams = false;\nfunction useLocale() {\n    // The types aren't entirely correct here. Outside of Next.js\n    // `useParams` can be called, but the return type is `null`.\n    const params = navigation.useParams();\n    let locale;\n    try {\n        // eslint-disable-next-line react-compiler/react-compiler\n        // eslint-disable-next-line react-hooks/rules-of-hooks, react-compiler/react-compiler -- False positive\n        locale = _useLocale.useLocale();\n    } catch (error) {\n        if (typeof (params === null || params === void 0 ? void 0 : params[constants.LOCALE_SEGMENT_NAME]) === \"string\") {\n            if (!hasWarnedForParams) {\n                console.warn(\"Deprecation warning: `useLocale` has returned a default from `useParams().locale` since no `NextIntlClientProvider` ancestor was found for the calling component. This behavior will be removed in the next major version. Please ensure all Client Components that use `next-intl` are wrapped in a `NextIntlClientProvider`.\");\n                hasWarnedForParams = true;\n            }\n            locale = params[constants.LOCALE_SEGMENT_NAME];\n        } else {\n            throw error;\n        }\n    }\n    return locale;\n}\nexports[\"default\"] = useLocale;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/routing.js":
/*!************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/routing.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar defineRouting = __webpack_require__(/*! ./routing/defineRouting.js */ \"(ssr)/./node_modules/next-intl/dist/development/routing/defineRouting.js\");\nexports.defineRouting = defineRouting.default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvcm91dGluZy5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUVBQSw4Q0FBNkM7SUFBRUcsT0FBTztBQUFLLENBQUMsRUFBQztBQUU3RCxJQUFJQyxnQkFBZ0JDLG1CQUFPQSxDQUFDO0FBSTVCSCxxQkFBcUIsR0FBR0UsY0FBY0UsT0FBTyIsInNvdXJjZXMiOlsid2VicGFjazovL3NtZS1hbmFseXRpY2EtZG9jcy8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9kZXZlbG9wbWVudC9yb3V0aW5nLmpzP2E3NDciXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgJ19fZXNNb2R1bGUnLCB7IHZhbHVlOiB0cnVlIH0pO1xuXG52YXIgZGVmaW5lUm91dGluZyA9IHJlcXVpcmUoJy4vcm91dGluZy9kZWZpbmVSb3V0aW5nLmpzJyk7XG5cblxuXG5leHBvcnRzLmRlZmluZVJvdXRpbmcgPSBkZWZpbmVSb3V0aW5nLmRlZmF1bHQ7XG4iXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJkZWZpbmVSb3V0aW5nIiwicmVxdWlyZSIsImRlZmF1bHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/routing.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/routing/config.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/routing/config.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nfunction receiveRoutingConfig(input) {\n    var _input$localeDetectio, _input$alternateLinks;\n    return {\n        ...input,\n        localePrefix: receiveLocalePrefixConfig(input.localePrefix),\n        localeCookie: receiveLocaleCookie(input.localeCookie),\n        localeDetection: (_input$localeDetectio = input.localeDetection) !== null && _input$localeDetectio !== void 0 ? _input$localeDetectio : true,\n        alternateLinks: (_input$alternateLinks = input.alternateLinks) !== null && _input$alternateLinks !== void 0 ? _input$alternateLinks : true\n    };\n}\nfunction receiveLocaleCookie(localeCookie) {\n    return (localeCookie !== null && localeCookie !== void 0 ? localeCookie : true) ? {\n        name: \"NEXT_LOCALE\",\n        maxAge: ********,\n        // 1 year\n        sameSite: \"lax\",\n        ...typeof localeCookie === \"object\" && localeCookie\n    } : false;\n}\nfunction receiveLocalePrefixConfig(localePrefix) {\n    return typeof localePrefix === \"object\" ? localePrefix : {\n        mode: localePrefix || \"always\"\n    };\n}\nexports.receiveLocaleCookie = receiveLocaleCookie;\nexports.receiveLocalePrefixConfig = receiveLocalePrefixConfig;\nexports.receiveRoutingConfig = receiveRoutingConfig;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/routing/config.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/routing/defineRouting.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/routing/defineRouting.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nfunction defineRouting(config) {\n    return config;\n}\nexports[\"default\"] = defineRouting;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvcm91dGluZy9kZWZpbmVSb3V0aW5nLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBRUFBLDhDQUE2QztJQUFFRyxPQUFPO0FBQUssQ0FBQyxFQUFDO0FBRTdELFNBQVNDLGNBQWNDLE1BQU07SUFDM0IsT0FBT0E7QUFDVDtBQUVBSCxrQkFBZSxHQUFHRSIsInNvdXJjZXMiOlsid2VicGFjazovL3NtZS1hbmFseXRpY2EtZG9jcy8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9kZXZlbG9wbWVudC9yb3V0aW5nL2RlZmluZVJvdXRpbmcuanM/Y2I4MiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAnX19lc01vZHVsZScsIHsgdmFsdWU6IHRydWUgfSk7XG5cbmZ1bmN0aW9uIGRlZmluZVJvdXRpbmcoY29uZmlnKSB7XG4gIHJldHVybiBjb25maWc7XG59XG5cbmV4cG9ydHMuZGVmYXVsdCA9IGRlZmluZVJvdXRpbmc7XG4iXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJkZWZpbmVSb3V0aW5nIiwiY29uZmlnIiwiZGVmYXVsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/routing/defineRouting.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/shared/NextIntlClientProvider.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/shared/NextIntlClientProvider.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar _IntlProvider = __webpack_require__(/*! use-intl/_IntlProvider */ \"(ssr)/./node_modules/use-intl/dist/_IntlProvider.js\");\nfunction _interopDefault(e) {\n    return e && e.__esModule ? e : {\n        default: e\n    };\n}\nvar React__default = /*#__PURE__*/ _interopDefault(React);\nfunction NextIntlClientProvider(_ref) {\n    let { locale, ...rest } = _ref;\n    // TODO: We could call `useParams` here to receive a default value\n    // for `locale`, but this would require dropping Next.js <13.\n    if (!locale) {\n        throw new Error(\"Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\\n\\nSee https://next-intl.dev/docs/configuration#locale\");\n    }\n    return /*#__PURE__*/ React__default.default.createElement(_IntlProvider.IntlProvider, _rollupPluginBabelHelpers.extends({\n        locale: locale\n    }, rest));\n}\nexports[\"default\"] = NextIntlClientProvider;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/shared/NextIntlClientProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/shared/constants.js":
/*!*********************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/shared/constants.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n// Should take precedence over the cookie\nconst HEADER_LOCALE_NAME = \"X-NEXT-INTL-LOCALE\";\n// In a URL like \"/en-US/about\", the locale segment is \"en-US\"\nconst LOCALE_SEGMENT_NAME = \"locale\";\nexports.HEADER_LOCALE_NAME = HEADER_LOCALE_NAME;\nexports.LOCALE_SEGMENT_NAME = LOCALE_SEGMENT_NAME;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvc2hhcmVkL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUVBQSw4Q0FBNkM7SUFBRUcsT0FBTztBQUFLLENBQUMsRUFBQztBQUU3RCx5Q0FBeUM7QUFDekMsTUFBTUMscUJBQXFCO0FBRTNCLDhEQUE4RDtBQUM5RCxNQUFNQyxzQkFBc0I7QUFFNUJILDBCQUEwQixHQUFHRTtBQUM3QkYsMkJBQTJCLEdBQUdHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc21lLWFuYWx5dGljYS1kb2NzLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2RldmVsb3BtZW50L3NoYXJlZC9jb25zdGFudHMuanM/ODdlYyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAnX19lc01vZHVsZScsIHsgdmFsdWU6IHRydWUgfSk7XG5cbi8vIFNob3VsZCB0YWtlIHByZWNlZGVuY2Ugb3ZlciB0aGUgY29va2llXG5jb25zdCBIRUFERVJfTE9DQUxFX05BTUUgPSAnWC1ORVhULUlOVEwtTE9DQUxFJztcblxuLy8gSW4gYSBVUkwgbGlrZSBcIi9lbi1VUy9hYm91dFwiLCB0aGUgbG9jYWxlIHNlZ21lbnQgaXMgXCJlbi1VU1wiXG5jb25zdCBMT0NBTEVfU0VHTUVOVF9OQU1FID0gJ2xvY2FsZSc7XG5cbmV4cG9ydHMuSEVBREVSX0xPQ0FMRV9OQU1FID0gSEVBREVSX0xPQ0FMRV9OQU1FO1xuZXhwb3J0cy5MT0NBTEVfU0VHTUVOVF9OQU1FID0gTE9DQUxFX1NFR01FTlRfTkFNRTtcbiJdLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsIkhFQURFUl9MT0NBTEVfTkFNRSIsIkxPQ0FMRV9TRUdNRU5UX05BTUUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/shared/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/shared/utils.js":
/*!*****************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/shared/utils.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nfunction isRelativeHref(href) {\n    const pathname = typeof href === \"object\" ? href.pathname : href;\n    return pathname != null && !pathname.startsWith(\"/\");\n}\nfunction isLocalHref(href) {\n    if (typeof href === \"object\") {\n        return href.host == null && href.hostname == null;\n    } else {\n        const hasProtocol = /^[a-z]+:/i.test(href);\n        return !hasProtocol;\n    }\n}\nfunction isLocalizableHref(href) {\n    return isLocalHref(href) && !isRelativeHref(href);\n}\nfunction localizeHref(href, locale) {\n    let curLocale = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : locale;\n    let curPathname = arguments.length > 3 ? arguments[3] : undefined;\n    let prefix = arguments.length > 4 ? arguments[4] : undefined;\n    if (!isLocalizableHref(href)) {\n        return href;\n    }\n    const isSwitchingLocale = locale !== curLocale;\n    const isPathnamePrefixed = hasPathnamePrefixed(prefix, curPathname);\n    const shouldPrefix = isSwitchingLocale || isPathnamePrefixed;\n    if (shouldPrefix && prefix != null) {\n        return prefixHref(href, prefix);\n    }\n    return href;\n}\nfunction prefixHref(href, prefix) {\n    let prefixedHref;\n    if (typeof href === \"string\") {\n        prefixedHref = prefixPathname(prefix, href);\n    } else {\n        prefixedHref = {\n            ...href\n        };\n        if (href.pathname) {\n            prefixedHref.pathname = prefixPathname(prefix, href.pathname);\n        }\n    }\n    return prefixedHref;\n}\nfunction unprefixPathname(pathname, prefix) {\n    return pathname.replace(new RegExp(\"^\".concat(prefix)), \"\") || \"/\";\n}\nfunction prefixPathname(prefix, pathname) {\n    let localizedHref = prefix;\n    // Avoid trailing slashes\n    if (/^\\/(\\?.*)?$/.test(pathname)) {\n        pathname = pathname.slice(1);\n    }\n    localizedHref += pathname;\n    return localizedHref;\n}\nfunction hasPathnamePrefixed(prefix, pathname) {\n    return pathname === prefix || pathname.startsWith(\"\".concat(prefix, \"/\"));\n}\nfunction hasTrailingSlash() {\n    try {\n        // Provided via `env` setting in `next.config.js` via the plugin\n        return process.env._next_intl_trailing_slash === \"true\";\n    } catch (_unused) {\n        return false;\n    }\n}\nfunction normalizeTrailingSlash(pathname) {\n    const trailingSlash = hasTrailingSlash();\n    if (pathname !== \"/\") {\n        const pathnameEndsWithSlash = pathname.endsWith(\"/\");\n        if (trailingSlash && !pathnameEndsWithSlash) {\n            pathname += \"/\";\n        } else if (!trailingSlash && pathnameEndsWithSlash) {\n            pathname = pathname.slice(0, -1);\n        }\n    }\n    return pathname;\n}\nfunction matchesPathname(/** E.g. `/users/[userId]-[userName]` */ template, /** E.g. `/users/23-jane` */ pathname) {\n    const normalizedTemplate = normalizeTrailingSlash(template);\n    const normalizedPathname = normalizeTrailingSlash(pathname);\n    const regex = templateToRegex(normalizedTemplate);\n    return regex.test(normalizedPathname);\n}\nfunction getLocalePrefix(locale, localePrefix) {\n    var _localePrefix$prefixe;\n    return localePrefix.mode !== \"never\" && ((_localePrefix$prefixe = localePrefix.prefixes) === null || _localePrefix$prefixe === void 0 ? void 0 : _localePrefix$prefixe[locale]) || // We return a prefix even if `mode: 'never'`. It's up to the consumer\n    // to decide to use it or not.\n    getLocaleAsPrefix(locale);\n}\nfunction getLocaleAsPrefix(locale) {\n    return \"/\" + locale;\n}\nfunction templateToRegex(template) {\n    const regexPattern = template// Replace optional catchall ('[[...slug]]')\n    .replace(/\\[\\[(\\.\\.\\.[^\\]]+)\\]\\]/g, \"?(.*)\")// Replace catchall ('[...slug]')\n    .replace(/\\[(\\.\\.\\.[^\\]]+)\\]/g, \"(.+)\")// Replace regular parameter ('[slug]')\n    .replace(/\\[([^\\]]+)\\]/g, \"([^/]+)\");\n    return new RegExp(\"^\".concat(regexPattern, \"$\"));\n}\nfunction isOptionalCatchAllSegment(pathname) {\n    return pathname.includes(\"[[...\");\n}\nfunction isCatchAllSegment(pathname) {\n    return pathname.includes(\"[...\");\n}\nfunction isDynamicSegment(pathname) {\n    return pathname.includes(\"[\");\n}\nfunction comparePathnamePairs(a, b) {\n    const pathA = a.split(\"/\");\n    const pathB = b.split(\"/\");\n    const maxLength = Math.max(pathA.length, pathB.length);\n    for(let i = 0; i < maxLength; i++){\n        const segmentA = pathA[i];\n        const segmentB = pathB[i];\n        // If one of the paths ends, prioritize the shorter path\n        if (!segmentA && segmentB) return -1;\n        if (segmentA && !segmentB) return 1;\n        if (!segmentA && !segmentB) continue;\n        // Prioritize static segments over dynamic segments\n        if (!isDynamicSegment(segmentA) && isDynamicSegment(segmentB)) return -1;\n        if (isDynamicSegment(segmentA) && !isDynamicSegment(segmentB)) return 1;\n        // Prioritize non-catch-all segments over catch-all segments\n        if (!isCatchAllSegment(segmentA) && isCatchAllSegment(segmentB)) return -1;\n        if (isCatchAllSegment(segmentA) && !isCatchAllSegment(segmentB)) return 1;\n        // Prioritize non-optional catch-all segments over optional catch-all segments\n        if (!isOptionalCatchAllSegment(segmentA) && isOptionalCatchAllSegment(segmentB)) {\n            return -1;\n        }\n        if (isOptionalCatchAllSegment(segmentA) && !isOptionalCatchAllSegment(segmentB)) {\n            return 1;\n        }\n        if (segmentA === segmentB) continue;\n    }\n    // Both pathnames are completely static\n    return 0;\n}\nfunction getSortedPathnames(pathnames) {\n    return pathnames.sort(comparePathnamePairs);\n}\nfunction isPromise(value) {\n    // https://github.com/amannn/next-intl/issues/1711\n    return typeof value.then === \"function\";\n}\nexports.getLocaleAsPrefix = getLocaleAsPrefix;\nexports.getLocalePrefix = getLocalePrefix;\nexports.getSortedPathnames = getSortedPathnames;\nexports.hasPathnamePrefixed = hasPathnamePrefixed;\nexports.isLocalizableHref = isLocalizableHref;\nexports.isPromise = isPromise;\nexports.localizeHref = localizeHref;\nexports.matchesPathname = matchesPathname;\nexports.normalizeTrailingSlash = normalizeTrailingSlash;\nexports.prefixHref = prefixHref;\nexports.prefixPathname = prefixPathname;\nexports.templateToRegex = templateToRegex;\nexports.unprefixPathname = unprefixPathname;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"extends\": () => (/* binding */ n)\n/* harmony export */ });\nfunction n() {\n    return n = Object.assign ? Object.assign.bind() : function(n) {\n        for(var r = 1; r < arguments.length; r++){\n            var t = arguments[r];\n            for(var a in t)({}).hasOwnProperty.call(t, a) && (n[a] = t[a]);\n        }\n        return n;\n    }, n.apply(null, arguments);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL192aXJ0dWFsL19yb2xsdXBQbHVnaW5CYWJlbEhlbHBlcnMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLFNBQVNBO0lBQUksT0FBT0EsSUFBRUMsT0FBT0MsTUFBTSxHQUFDRCxPQUFPQyxNQUFNLENBQUNDLElBQUksS0FBRyxTQUFTSCxDQUFDO1FBQUUsSUFBSSxJQUFJSSxJQUFFLEdBQUVBLElBQUVDLFVBQVVDLE1BQU0sRUFBQ0YsSUFBSTtZQUFDLElBQUlHLElBQUVGLFNBQVMsQ0FBQ0QsRUFBRTtZQUFDLElBQUksSUFBSUksS0FBS0QsRUFBRSxDQUFDLENBQUMsR0FBR0UsY0FBYyxDQUFDQyxJQUFJLENBQUNILEdBQUVDLE1BQUtSLENBQUFBLENBQUMsQ0FBQ1EsRUFBRSxHQUFDRCxDQUFDLENBQUNDLEVBQUU7UUFBQztRQUFDLE9BQU9SO0lBQUMsR0FBRUEsRUFBRVcsS0FBSyxDQUFDLE1BQUtOO0FBQVU7QUFBc0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zbWUtYW5hbHl0aWNhLWRvY3MvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL192aXJ0dWFsL19yb2xsdXBQbHVnaW5CYWJlbEhlbHBlcnMuanM/NDIxMCJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBuKCl7cmV0dXJuIG49T2JqZWN0LmFzc2lnbj9PYmplY3QuYXNzaWduLmJpbmQoKTpmdW5jdGlvbihuKXtmb3IodmFyIHI9MTtyPGFyZ3VtZW50cy5sZW5ndGg7cisrKXt2YXIgdD1hcmd1bWVudHNbcl07Zm9yKHZhciBhIGluIHQpKHt9KS5oYXNPd25Qcm9wZXJ0eS5jYWxsKHQsYSkmJihuW2FdPXRbYV0pfXJldHVybiBufSxuLmFwcGx5KG51bGwsYXJndW1lbnRzKX1leHBvcnR7biBhcyBleHRlbmRzfTtcbiJdLCJuYW1lcyI6WyJuIiwiT2JqZWN0IiwiYXNzaWduIiwiYmluZCIsInIiLCJhcmd1bWVudHMiLCJsZW5ndGgiLCJ0IiwiYSIsImhhc093blByb3BlcnR5IiwiY2FsbCIsImFwcGx5IiwiZXh0ZW5kcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ p)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _react_client_useLocale_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/esm/react-client/useLocale.js\");\n/* harmony import */ var _syncLocaleCookie_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./syncLocaleCookie.js */ \"(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/syncLocaleCookie.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction s(n, s) {\n    let { defaultLocale: p, href: f, locale: u, localeCookie: m, onClick: h, prefetch: d, unprefixed: k, ...x } = n;\n    const L = (0,_react_client_useLocale_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(), g = null != u && u !== L, j = u || L, v = function() {\n        const [e, o] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n        return (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n            o(window.location.host);\n        }, []), e;\n    }(), w = v && k && (k.domains[v] === j || !Object.keys(k.domains).includes(v) && L === p && !u) ? k.pathname : f, C = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    return g && (d && console.error(\"The `prefetch` prop is currently not supported when using the `locale` prop on `Link` to switch the locale.`\"), d = !1), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement((next_link__WEBPACK_IMPORTED_MODULE_0___default()), (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_4__[\"extends\"])({\n        ref: s,\n        href: w,\n        hrefLang: g ? u : void 0,\n        onClick: function(e) {\n            (0,_syncLocaleCookie_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(m, C, L, u), h && h(e);\n        },\n        prefetch: d\n    }, x));\n}\nvar p = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(s);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ p)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_client_useLocale_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/esm/react-client/useLocale.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/esm/shared/utils.js\");\n/* harmony import */ var _BaseLink_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./BaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction f(l, f) {\n    let { href: p, locale: u, localeCookie: d, localePrefixMode: x, prefix: j, ...k } = l;\n    const h = (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.usePathname)(), v = (0,_react_client_useLocale_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(), C = u !== v, [L, g] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.isLocalizableHref)(p) && (\"never\" !== x || C) ? (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.prefixHref)(p, j) : p);\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        h && g((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.localizeHref)(p, u, v, h, j));\n    }, [\n        v,\n        p,\n        u,\n        h,\n        j\n    ]), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(_BaseLink_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"], (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_5__[\"extends\"])({\n        ref: f,\n        href: L,\n        locale: u,\n        localeCookie: d\n    }, k));\n}\nconst p = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(f);\np.displayName = \"ClientLink\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/syncLocaleCookie.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/shared/syncLocaleCookie.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ o)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/utils.js\");\n\nfunction o(o, e, n, a) {\n    if (!o || !(a !== n && null != a) || !e) return;\n    const c = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.getBasePath)(e), f = \"\" !== c ? c : \"/\", { name: r, ...i } = o;\n    i.path || (i.path = f);\n    let l = \"\".concat(r, \"=\").concat(a, \";\");\n    for (const [t, o] of Object.entries(i)){\n        l += \"\".concat(\"maxAge\" === t ? \"max-age\" : t), \"boolean\" != typeof o && (l += \"=\" + o), l += \";\";\n    }\n    document.cookie = l;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL25hdmlnYXRpb24vc2hhcmVkL3N5bmNMb2NhbGVDb29raWUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBeUM7QUFBQSxTQUFTRSxFQUFFQSxDQUFDLEVBQUNDLENBQUMsRUFBQ0MsQ0FBQyxFQUFDQyxDQUFDO0lBQUUsSUFBRyxDQUFDSCxLQUFHLENBQUVHLENBQUFBLE1BQUlELEtBQUcsUUFBTUMsQ0FBQUEsS0FBSSxDQUFDRixHQUFFO0lBQU8sTUFBTUcsSUFBRUwsc0RBQUNBLENBQUNFLElBQUdJLElBQUUsT0FBS0QsSUFBRUEsSUFBRSxLQUFJLEVBQUNFLE1BQUtDLENBQUMsRUFBQyxHQUFHQyxHQUFFLEdBQUNSO0lBQUVRLEVBQUVDLElBQUksSUFBR0QsQ0FBQUEsRUFBRUMsSUFBSSxHQUFDSixDQUFBQTtJQUFHLElBQUlLLElBQUUsR0FBR0MsTUFBTSxDQUFDSixHQUFFLEtBQUtJLE1BQU0sQ0FBQ1IsR0FBRTtJQUFLLEtBQUksTUFBSyxDQUFDSixHQUFFQyxFQUFFLElBQUdZLE9BQU9DLE9BQU8sQ0FBQ0wsR0FBRztRQUFDRSxLQUFHLEdBQUdDLE1BQU0sQ0FBQyxhQUFXWixJQUFFLFlBQVVBLElBQUcsYUFBVyxPQUFPQyxLQUFJVSxDQUFBQSxLQUFHLE1BQUlWLENBQUFBLEdBQUdVLEtBQUc7SUFBRztJQUFDSSxTQUFTQyxNQUFNLEdBQUNMO0FBQUM7QUFBc0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zbWUtYW5hbHl0aWNhLWRvY3MvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL25hdmlnYXRpb24vc2hhcmVkL3N5bmNMb2NhbGVDb29raWUuanM/NmI0NiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7Z2V0QmFzZVBhdGggYXMgdH1mcm9tXCIuL3V0aWxzLmpzXCI7ZnVuY3Rpb24gbyhvLGUsbixhKXtpZighb3x8IShhIT09biYmbnVsbCE9YSl8fCFlKXJldHVybjtjb25zdCBjPXQoZSksZj1cIlwiIT09Yz9jOlwiL1wiLHtuYW1lOnIsLi4uaX09bztpLnBhdGh8fChpLnBhdGg9Zik7bGV0IGw9XCJcIi5jb25jYXQocixcIj1cIikuY29uY2F0KGEsXCI7XCIpO2Zvcihjb25zdFt0LG9db2YgT2JqZWN0LmVudHJpZXMoaSkpe2wrPVwiXCIuY29uY2F0KFwibWF4QWdlXCI9PT10P1wibWF4LWFnZVwiOnQpLFwiYm9vbGVhblwiIT10eXBlb2YgbyYmKGwrPVwiPVwiK28pLGwrPVwiO1wifWRvY3VtZW50LmNvb2tpZT1sfWV4cG9ydHtvIGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbImdldEJhc2VQYXRoIiwidCIsIm8iLCJlIiwibiIsImEiLCJjIiwiZiIsIm5hbWUiLCJyIiwiaSIsInBhdGgiLCJsIiwiY29uY2F0IiwiT2JqZWN0IiwiZW50cmllcyIsImRvY3VtZW50IiwiY29va2llIiwiZGVmYXVsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/syncLocaleCookie.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/utils.js":
/*!********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/shared/utils.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyPathnamePrefix: () => (/* binding */ d),\n/* harmony export */   compileLocalizedPathname: () => (/* binding */ f),\n/* harmony export */   getBasePath: () => (/* binding */ l),\n/* harmony export */   getRoute: () => (/* binding */ s),\n/* harmony export */   normalizeNameOrNameWithParams: () => (/* binding */ i),\n/* harmony export */   serializeSearchParams: () => (/* binding */ c),\n/* harmony export */   validateReceivedConfig: () => (/* binding */ u)\n/* harmony export */ });\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/esm/shared/utils.js\");\n\nfunction i(e) {\n    return \"string\" == typeof e ? {\n        pathname: e\n    } : e;\n}\nfunction c(e) {\n    function n(e) {\n        return String(e);\n    }\n    const t = new URLSearchParams;\n    for (const [r, o] of Object.entries(e))Array.isArray(o) ? o.forEach((e)=>{\n        t.append(r, n(e));\n    }) : t.set(r, n(o));\n    return \"?\" + t.toString();\n}\nfunction f(e) {\n    let { pathname: n, locale: t, params: r, pathnames: o, query: i } = e;\n    function f(e) {\n        let n = o[e];\n        return n || (n = e), n;\n    }\n    function s(e) {\n        const n = \"string\" == typeof e ? e : e[t];\n        let o = n;\n        if (r && Object.entries(r).forEach((e)=>{\n            let n, t, [r, a] = e;\n            Array.isArray(a) ? (n = \"(\\\\[)?\\\\[...\".concat(r, \"\\\\](\\\\])?\"), t = a.map((e)=>String(e)).join(\"/\")) : (n = \"\\\\[\".concat(r, \"\\\\]\"), t = String(a)), o = o.replace(new RegExp(n, \"g\"), t);\n        }), o = o.replace(/\\[\\[\\.\\.\\..+\\]\\]/g, \"\"), o = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.normalizeTrailingSlash)(o), o.includes(\"[\")) throw new Error(\"Insufficient params provided for localized pathname.\\nTemplate: \".concat(n, \"\\nParams: \").concat(JSON.stringify(r)));\n        return i && (o += c(i)), o;\n    }\n    if (\"string\" == typeof n) {\n        return s(f(n));\n    }\n    {\n        const { pathname: e, ...t } = n;\n        return {\n            ...t,\n            pathname: s(f(e))\n        };\n    }\n}\nfunction s(t, r, o) {\n    const a = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getSortedPathnames)(Object.keys(o)), i = decodeURI(r);\n    for (const e of a){\n        const r = o[e];\n        if (\"string\" == typeof r) {\n            if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.matchesPathname)(r, i)) return e;\n        } else if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.matchesPathname)(r[t], i)) return e;\n    }\n    return r;\n}\nfunction l(e) {\n    let n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : window.location.pathname;\n    return \"/\" === e ? n : n.replace(e, \"\");\n}\nfunction d(e, n, a, i, c) {\n    const { mode: f } = a.localePrefix;\n    let s;\n    if (void 0 !== c) s = c;\n    else if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.isLocalizableHref)(e)) {\n        if (\"always\" === f) s = !0;\n        else if (\"as-needed\" === f) {\n            let e = a.defaultLocale;\n            if (a.domains) {\n                const n = a.domains.find((e)=>e.domain === i);\n                n ? e = n.defaultLocale : i || console.error(\"You're using a routing configuration with `localePrefix: 'as-needed'` in combination with `domains`. In order to compute a correct pathname, you need to provide a `domain` parameter.\\n\\nSee: https://next-intl.dev/docs/routing#domains-localeprefix-asneeded\");\n            }\n            s = e !== n;\n        }\n    }\n    return s ? (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.prefixPathname)((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getLocalePrefix)(n, a.localePrefix), e) : e;\n}\nfunction u(e) {\n    var n;\n    if (\"as-needed\" === (null === (n = e.localePrefix) || void 0 === n ? void 0 : n.mode) && !(\"defaultLocale\" in e)) throw new Error(\"`localePrefix: 'as-needed' requires a `defaultLocale`.\");\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/react-client/useLocale.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/react-client/useLocale.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var use_intl_useLocale__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-intl/_useLocale */ \"(ssr)/./node_modules/use-intl/dist/development/_useLocale.js\");\n/* harmony import */ var _shared_constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../shared/constants.js */ \"(ssr)/./node_modules/next-intl/dist/esm/shared/constants.js\");\n\n\n\nlet o = !1;\nfunction r() {\n    const r = (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.useParams)();\n    let a;\n    try {\n        a = (0,use_intl_useLocale__WEBPACK_IMPORTED_MODULE_1__.useLocale)();\n    } catch (e) {\n        if (\"string\" != typeof (null == r ? void 0 : r[_shared_constants_js__WEBPACK_IMPORTED_MODULE_2__.LOCALE_SEGMENT_NAME])) throw e;\n        o || (console.warn(\"Deprecation warning: `useLocale` has returned a default from `useParams().locale` since no `NextIntlClientProvider` ancestor was found for the calling component. This behavior will be removed in the next major version. Please ensure all Client Components that use `next-intl` are wrapped in a `NextIntlClientProvider`.\"), o = !0), a = r[_shared_constants_js__WEBPACK_IMPORTED_MODULE_2__.LOCALE_SEGMENT_NAME];\n    }\n    return a;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LWNsaWVudC91c2VMb2NhbGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBNEM7QUFBZ0Q7QUFBNkQ7QUFBQSxJQUFJTSxJQUFFLENBQUM7QUFBRSxTQUFTQztJQUFJLE1BQU1BLElBQUVOLDBEQUFDQTtJQUFHLElBQUlPO0lBQUUsSUFBRztRQUFDQSxJQUFFTCw2REFBQ0E7SUFBRSxFQUFDLE9BQU1GLEdBQUU7UUFBQyxJQUFHLFlBQVUsT0FBTyxTQUFNTSxJQUFFLEtBQUssSUFBRUEsQ0FBQyxDQUFDRixxRUFBQ0EsQ0FBQyxHQUFFLE1BQU1KO1FBQUVLLEtBQUlHLENBQUFBLFFBQVFDLElBQUksQ0FBQyxtVUFBa1VKLElBQUUsQ0FBQyxJQUFHRSxJQUFFRCxDQUFDLENBQUNGLHFFQUFDQSxDQUFDO0lBQUE7SUFBQyxPQUFPRztBQUFDO0FBQXNCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc21lLWFuYWx5dGljYS1kb2NzLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9yZWFjdC1jbGllbnQvdXNlTG9jYWxlLmpzPzdhNWUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0e3VzZVBhcmFtcyBhcyBlfWZyb21cIm5leHQvbmF2aWdhdGlvblwiO2ltcG9ydHt1c2VMb2NhbGUgYXMgdH1mcm9tXCJ1c2UtaW50bC9fdXNlTG9jYWxlXCI7aW1wb3J0e0xPQ0FMRV9TRUdNRU5UX05BTUUgYXMgbn1mcm9tXCIuLi9zaGFyZWQvY29uc3RhbnRzLmpzXCI7bGV0IG89ITE7ZnVuY3Rpb24gcigpe2NvbnN0IHI9ZSgpO2xldCBhO3RyeXthPXQoKX1jYXRjaChlKXtpZihcInN0cmluZ1wiIT10eXBlb2YobnVsbD09cj92b2lkIDA6cltuXSkpdGhyb3cgZTtvfHwoY29uc29sZS53YXJuKFwiRGVwcmVjYXRpb24gd2FybmluZzogYHVzZUxvY2FsZWAgaGFzIHJldHVybmVkIGEgZGVmYXVsdCBmcm9tIGB1c2VQYXJhbXMoKS5sb2NhbGVgIHNpbmNlIG5vIGBOZXh0SW50bENsaWVudFByb3ZpZGVyYCBhbmNlc3RvciB3YXMgZm91bmQgZm9yIHRoZSBjYWxsaW5nIGNvbXBvbmVudC4gVGhpcyBiZWhhdmlvciB3aWxsIGJlIHJlbW92ZWQgaW4gdGhlIG5leHQgbWFqb3IgdmVyc2lvbi4gUGxlYXNlIGVuc3VyZSBhbGwgQ2xpZW50IENvbXBvbmVudHMgdGhhdCB1c2UgYG5leHQtaW50bGAgYXJlIHdyYXBwZWQgaW4gYSBgTmV4dEludGxDbGllbnRQcm92aWRlcmAuXCIpLG89ITApLGE9cltuXX1yZXR1cm4gYX1leHBvcnR7ciBhcyBkZWZhdWx0fTtcbiJdLCJuYW1lcyI6WyJ1c2VQYXJhbXMiLCJlIiwidXNlTG9jYWxlIiwidCIsIkxPQ0FMRV9TRUdNRU5UX05BTUUiLCJuIiwibyIsInIiLCJhIiwiY29uc29sZSIsIndhcm4iLCJkZWZhdWx0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/react-client/useLocale.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var use_intl_IntlProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-intl/_IntlProvider */ \"(ssr)/./node_modules/use-intl/dist/development/_IntlProvider.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction r(r) {\n    let { locale: o, ...i } = r;\n    if (!o) throw new Error(\"Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\\n\\nSee https://next-intl.dev/docs/configuration#locale\");\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(use_intl_IntlProvider__WEBPACK_IMPORTED_MODULE_1__.IntlProvider, (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_2__[\"extends\"])({\n        locale: o\n    }, i));\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NoYXJlZC9OZXh0SW50bENsaWVudFByb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OzZEQUNtRTtBQUFxQjtBQUFzRDtBQUFBLFNBQVNLLEVBQUVBLENBQUM7SUFBRSxJQUFHLEVBQUNDLFFBQU9DLENBQUMsRUFBQyxHQUFHQyxHQUFFLEdBQUNIO0lBQUUsSUFBRyxDQUFDRSxHQUFFLE1BQU0sSUFBSUUsTUFBTTtJQUErSixxQkFBT1AsMERBQWUsQ0FBQ0UsK0RBQUNBLEVBQUNILGdGQUFDQSxDQUFDO1FBQUNLLFFBQU9DO0lBQUMsR0FBRUM7QUFBRztBQUFzQiIsInNvdXJjZXMiOlsid2VicGFjazovL3NtZS1hbmFseXRpY2EtZG9jcy8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vc2hhcmVkL05leHRJbnRsQ2xpZW50UHJvdmlkZXIuanM/ZGNlNCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcbmltcG9ydHtleHRlbmRzIGFzIGV9ZnJvbVwiLi4vX3ZpcnR1YWwvX3JvbGx1cFBsdWdpbkJhYmVsSGVscGVycy5qc1wiO2ltcG9ydCBsIGZyb21cInJlYWN0XCI7aW1wb3J0e0ludGxQcm92aWRlciBhcyB0fWZyb21cInVzZS1pbnRsL19JbnRsUHJvdmlkZXJcIjtmdW5jdGlvbiByKHIpe2xldHtsb2NhbGU6bywuLi5pfT1yO2lmKCFvKXRocm93IG5ldyBFcnJvcihcIkZhaWxlZCB0byBkZXRlcm1pbmUgbG9jYWxlIGluIGBOZXh0SW50bENsaWVudFByb3ZpZGVyYCwgcGxlYXNlIHByb3ZpZGUgdGhlIGBsb2NhbGVgIHByb3AgZXhwbGljaXRseS5cXG5cXG5TZWUgaHR0cHM6Ly9uZXh0LWludGwuZGV2L2RvY3MvY29uZmlndXJhdGlvbiNsb2NhbGVcIik7cmV0dXJuIGwuY3JlYXRlRWxlbWVudCh0LGUoe2xvY2FsZTpvfSxpKSl9ZXhwb3J0e3IgYXMgZGVmYXVsdH07XG4iXSwibmFtZXMiOlsiZXh0ZW5kcyIsImUiLCJsIiwiSW50bFByb3ZpZGVyIiwidCIsInIiLCJsb2NhbGUiLCJvIiwiaSIsIkVycm9yIiwiY3JlYXRlRWxlbWVudCIsImRlZmF1bHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/shared/constants.js":
/*!*************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/constants.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HEADER_LOCALE_NAME: () => (/* binding */ o),\n/* harmony export */   LOCALE_SEGMENT_NAME: () => (/* binding */ L)\n/* harmony export */ });\nconst o = \"X-NEXT-INTL-LOCALE\", L = \"locale\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NoYXJlZC9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSxNQUFNQSxJQUFFLHNCQUFxQkMsSUFBRTtBQUFrRSIsInNvdXJjZXMiOlsid2VicGFjazovL3NtZS1hbmFseXRpY2EtZG9jcy8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vc2hhcmVkL2NvbnN0YW50cy5qcz9iZTZjIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IG89XCJYLU5FWFQtSU5UTC1MT0NBTEVcIixMPVwibG9jYWxlXCI7ZXhwb3J0e28gYXMgSEVBREVSX0xPQ0FMRV9OQU1FLEwgYXMgTE9DQUxFX1NFR01FTlRfTkFNRX07XG4iXSwibmFtZXMiOlsibyIsIkwiLCJIRUFERVJfTE9DQUxFX05BTUUiLCJMT0NBTEVfU0VHTUVOVF9OQU1FIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/shared/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/shared/utils.js":
/*!*********************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/utils.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLocaleAsPrefix: () => (/* binding */ l),\n/* harmony export */   getLocalePrefix: () => (/* binding */ f),\n/* harmony export */   getSortedPathnames: () => (/* binding */ d),\n/* harmony export */   hasPathnamePrefixed: () => (/* binding */ i),\n/* harmony export */   isLocalizableHref: () => (/* binding */ n),\n/* harmony export */   isPromise: () => (/* binding */ v),\n/* harmony export */   localizeHref: () => (/* binding */ t),\n/* harmony export */   matchesPathname: () => (/* binding */ c),\n/* harmony export */   normalizeTrailingSlash: () => (/* binding */ o),\n/* harmony export */   prefixHref: () => (/* binding */ e),\n/* harmony export */   prefixPathname: () => (/* binding */ u),\n/* harmony export */   templateToRegex: () => (/* binding */ s),\n/* harmony export */   unprefixPathname: () => (/* binding */ r)\n/* harmony export */ });\nfunction n(n) {\n    return function(n) {\n        return \"object\" == typeof n ? null == n.host && null == n.hostname : !/^[a-z]+:/i.test(n);\n    }(n) && !function(n) {\n        const t = \"object\" == typeof n ? n.pathname : n;\n        return null != t && !t.startsWith(\"/\");\n    }(n);\n}\nfunction t(t, r) {\n    let u = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : r, o = arguments.length > 3 ? arguments[3] : void 0, c = arguments.length > 4 ? arguments[4] : void 0;\n    if (!n(t)) return t;\n    const f = r !== u, l = i(c, o);\n    return (f || l) && null != c ? e(t, c) : t;\n}\nfunction e(n, t) {\n    let e;\n    return \"string\" == typeof n ? e = u(t, n) : (e = {\n        ...n\n    }, n.pathname && (e.pathname = u(t, n.pathname))), e;\n}\nfunction r(n, t) {\n    return n.replace(new RegExp(\"^\".concat(t)), \"\") || \"/\";\n}\nfunction u(n, t) {\n    let e = n;\n    return /^\\/(\\?.*)?$/.test(t) && (t = t.slice(1)), e += t, e;\n}\nfunction i(n, t) {\n    return t === n || t.startsWith(\"\".concat(n, \"/\"));\n}\nfunction o(n) {\n    const t = function() {\n        try {\n            return \"true\" === process.env._next_intl_trailing_slash;\n        } catch (n) {\n            return !1;\n        }\n    }();\n    if (\"/\" !== n) {\n        const e = n.endsWith(\"/\");\n        t && !e ? n += \"/\" : !t && e && (n = n.slice(0, -1));\n    }\n    return n;\n}\nfunction c(n, t) {\n    const e = o(n), r = o(t);\n    return s(e).test(r);\n}\nfunction f(n, t) {\n    var e;\n    return \"never\" !== t.mode && (null === (e = t.prefixes) || void 0 === e ? void 0 : e[n]) || l(n);\n}\nfunction l(n) {\n    return \"/\" + n;\n}\nfunction s(n) {\n    const t = n.replace(/\\[\\[(\\.\\.\\.[^\\]]+)\\]\\]/g, \"?(.*)\").replace(/\\[(\\.\\.\\.[^\\]]+)\\]/g, \"(.+)\").replace(/\\[([^\\]]+)\\]/g, \"([^/]+)\");\n    return new RegExp(\"^\".concat(t, \"$\"));\n}\nfunction a(n) {\n    return n.includes(\"[[...\");\n}\nfunction p(n) {\n    return n.includes(\"[...\");\n}\nfunction h(n) {\n    return n.includes(\"[\");\n}\nfunction g(n, t) {\n    const e = n.split(\"/\"), r = t.split(\"/\"), u = Math.max(e.length, r.length);\n    for(let n = 0; n < u; n++){\n        const t = e[n], u = r[n];\n        if (!t && u) return -1;\n        if (t && !u) return 1;\n        if (t || u) {\n            if (!h(t) && h(u)) return -1;\n            if (h(t) && !h(u)) return 1;\n            if (!p(t) && p(u)) return -1;\n            if (p(t) && !p(u)) return 1;\n            if (!a(t) && a(u)) return -1;\n            if (a(t) && !a(u)) return 1;\n        }\n    }\n    return 0;\n}\nfunction d(n) {\n    return n.sort(g);\n}\nfunction v(n) {\n    return \"function\" == typeof n.then;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/shared/utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/development/routing.js":
/*!************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/routing.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar defineRouting = __webpack_require__(/*! ./routing/defineRouting.js */ \"(rsc)/./node_modules/next-intl/dist/development/routing/defineRouting.js\");\nexports.defineRouting = defineRouting.default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvcm91dGluZy5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUVBQSw4Q0FBNkM7SUFBRUcsT0FBTztBQUFLLENBQUMsRUFBQztBQUU3RCxJQUFJQyxnQkFBZ0JDLG1CQUFPQSxDQUFDO0FBSTVCSCxxQkFBcUIsR0FBR0UsY0FBY0UsT0FBTyIsInNvdXJjZXMiOlsid2VicGFjazovL3NtZS1hbmFseXRpY2EtZG9jcy8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9kZXZlbG9wbWVudC9yb3V0aW5nLmpzP2E3NDciXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgJ19fZXNNb2R1bGUnLCB7IHZhbHVlOiB0cnVlIH0pO1xuXG52YXIgZGVmaW5lUm91dGluZyA9IHJlcXVpcmUoJy4vcm91dGluZy9kZWZpbmVSb3V0aW5nLmpzJyk7XG5cblxuXG5leHBvcnRzLmRlZmluZVJvdXRpbmcgPSBkZWZpbmVSb3V0aW5nLmRlZmF1bHQ7XG4iXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJkZWZpbmVSb3V0aW5nIiwicmVxdWlyZSIsImRlZmF1bHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/development/routing.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/development/routing/defineRouting.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/routing/defineRouting.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nfunction defineRouting(config) {\n    return config;\n}\nexports[\"default\"] = defineRouting;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvcm91dGluZy9kZWZpbmVSb3V0aW5nLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBRUFBLDhDQUE2QztJQUFFRyxPQUFPO0FBQUssQ0FBQyxFQUFDO0FBRTdELFNBQVNDLGNBQWNDLE1BQU07SUFDM0IsT0FBT0E7QUFDVDtBQUVBSCxrQkFBZSxHQUFHRSIsInNvdXJjZXMiOlsid2VicGFjazovL3NtZS1hbmFseXRpY2EtZG9jcy8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9kZXZlbG9wbWVudC9yb3V0aW5nL2RlZmluZVJvdXRpbmcuanM/Y2I4MiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAnX19lc01vZHVsZScsIHsgdmFsdWU6IHRydWUgfSk7XG5cbmZ1bmN0aW9uIGRlZmluZVJvdXRpbmcoY29uZmlnKSB7XG4gIHJldHVybiBjb25maWc7XG59XG5cbmV4cG9ydHMuZGVmYXVsdCA9IGRlZmluZVJvdXRpbmc7XG4iXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJkZWZpbmVSb3V0aW5nIiwiY29uZmlnIiwiZGVmYXVsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/development/routing/defineRouting.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"extends\": () => (/* binding */ n)\n/* harmony export */ });\nfunction n() {\n    return n = Object.assign ? Object.assign.bind() : function(n) {\n        for(var r = 1; r < arguments.length; r++){\n            var t = arguments[r];\n            for(var a in t)({}).hasOwnProperty.call(t, a) && (n[a] = t[a]);\n        }\n        return n;\n    }, n.apply(null, arguments);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL192aXJ0dWFsL19yb2xsdXBQbHVnaW5CYWJlbEhlbHBlcnMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLFNBQVNBO0lBQUksT0FBT0EsSUFBRUMsT0FBT0MsTUFBTSxHQUFDRCxPQUFPQyxNQUFNLENBQUNDLElBQUksS0FBRyxTQUFTSCxDQUFDO1FBQUUsSUFBSSxJQUFJSSxJQUFFLEdBQUVBLElBQUVDLFVBQVVDLE1BQU0sRUFBQ0YsSUFBSTtZQUFDLElBQUlHLElBQUVGLFNBQVMsQ0FBQ0QsRUFBRTtZQUFDLElBQUksSUFBSUksS0FBS0QsRUFBRSxDQUFDLENBQUMsR0FBR0UsY0FBYyxDQUFDQyxJQUFJLENBQUNILEdBQUVDLE1BQUtSLENBQUFBLENBQUMsQ0FBQ1EsRUFBRSxHQUFDRCxDQUFDLENBQUNDLEVBQUU7UUFBQztRQUFDLE9BQU9SO0lBQUMsR0FBRUEsRUFBRVcsS0FBSyxDQUFDLE1BQUtOO0FBQVU7QUFBc0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zbWUtYW5hbHl0aWNhLWRvY3MvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL192aXJ0dWFsL19yb2xsdXBQbHVnaW5CYWJlbEhlbHBlcnMuanM/NDIxMCJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBuKCl7cmV0dXJuIG49T2JqZWN0LmFzc2lnbj9PYmplY3QuYXNzaWduLmJpbmQoKTpmdW5jdGlvbihuKXtmb3IodmFyIHI9MTtyPGFyZ3VtZW50cy5sZW5ndGg7cisrKXt2YXIgdD1hcmd1bWVudHNbcl07Zm9yKHZhciBhIGluIHQpKHt9KS5oYXNPd25Qcm9wZXJ0eS5jYWxsKHQsYSkmJihuW2FdPXRbYV0pfXJldHVybiBufSxuLmFwcGx5KG51bGwsYXJndW1lbnRzKX1leHBvcnR7biBhcyBleHRlbmRzfTtcbiJdLCJuYW1lcyI6WyJuIiwiT2JqZWN0IiwiYXNzaWduIiwiYmluZCIsInIiLCJhcmd1bWVudHMiLCJsZW5ndGgiLCJ0IiwiYSIsImhhc093blByb3BlcnR5IiwiY2FsbCIsImFwcGx5IiwiZXh0ZW5kcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/navigation/react-server/createNavigation.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/react-server/createNavigation.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ t)\n/* harmony export */ });\n/* harmony import */ var _shared_createSharedNavigationFns_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../shared/createSharedNavigationFns.js */ \"(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/createSharedNavigationFns.js\");\n/* harmony import */ var _getServerLocale_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getServerLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/navigation/react-server/getServerLocale.js\");\n\n\nfunction t(t) {\n    const { config: n, ...r } = (0,_shared_createSharedNavigationFns_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(function() {\n        return (0,_getServerLocale_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n    }, t);\n    function u(e) {\n        return ()=>{\n            throw new Error(\"`\".concat(e, \"` is not supported in Server Components. You can use this hook if you convert the calling component to a Client Component.\"));\n        };\n    }\n    return {\n        ...r,\n        usePathname: u(\"usePathname\"),\n        useRouter: u(\"useRouter\")\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL25hdmlnYXRpb24vcmVhY3Qtc2VydmVyL2NyZWF0ZU5hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXNEO0FBQW9DO0FBQUEsU0FBU0UsRUFBRUEsQ0FBQztJQUFFLE1BQUssRUFBQ0MsUUFBT0MsQ0FBQyxFQUFDLEdBQUdDLEdBQUUsR0FBQ0wsZ0ZBQUNBLENBQUU7UUFBVyxPQUFPQywrREFBQ0E7SUFBRSxHQUFHQztJQUFHLFNBQVNJLEVBQUVOLENBQUM7UUFBRSxPQUFNO1lBQUssTUFBTSxJQUFJTyxNQUFNLElBQUlDLE1BQU0sQ0FBQ1IsR0FBRTtRQUE4SDtJQUFDO0lBQUMsT0FBTTtRQUFDLEdBQUdLLENBQUM7UUFBQ0ksYUFBWUgsRUFBRTtRQUFlSSxXQUFVSixFQUFFO0lBQVk7QUFBQztBQUFzQiIsInNvdXJjZXMiOlsid2VicGFjazovL3NtZS1hbmFseXRpY2EtZG9jcy8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vbmF2aWdhdGlvbi9yZWFjdC1zZXJ2ZXIvY3JlYXRlTmF2aWdhdGlvbi5qcz84NjNjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBlIGZyb21cIi4uL3NoYXJlZC9jcmVhdGVTaGFyZWROYXZpZ2F0aW9uRm5zLmpzXCI7aW1wb3J0IG8gZnJvbVwiLi9nZXRTZXJ2ZXJMb2NhbGUuanNcIjtmdW5jdGlvbiB0KHQpe2NvbnN0e2NvbmZpZzpuLC4uLnJ9PWUoKGZ1bmN0aW9uKCl7cmV0dXJuIG8oKX0pLHQpO2Z1bmN0aW9uIHUoZSl7cmV0dXJuKCk9Pnt0aHJvdyBuZXcgRXJyb3IoXCJgXCIuY29uY2F0KGUsXCJgIGlzIG5vdCBzdXBwb3J0ZWQgaW4gU2VydmVyIENvbXBvbmVudHMuIFlvdSBjYW4gdXNlIHRoaXMgaG9vayBpZiB5b3UgY29udmVydCB0aGUgY2FsbGluZyBjb21wb25lbnQgdG8gYSBDbGllbnQgQ29tcG9uZW50LlwiKSl9fXJldHVybnsuLi5yLHVzZVBhdGhuYW1lOnUoXCJ1c2VQYXRobmFtZVwiKSx1c2VSb3V0ZXI6dShcInVzZVJvdXRlclwiKX19ZXhwb3J0e3QgYXMgZGVmYXVsdH07XG4iXSwibmFtZXMiOlsiZSIsIm8iLCJ0IiwiY29uZmlnIiwibiIsInIiLCJ1IiwiRXJyb3IiLCJjb25jYXQiLCJ1c2VQYXRobmFtZSIsInVzZVJvdXRlciIsImRlZmF1bHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/navigation/react-server/createNavigation.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/navigation/react-server/getServerLocale.js":
/*!************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/react-server/getServerLocale.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var _server_react_server_getConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../server/react-server/getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\n\nasync function r() {\n    return (await (0,_server_react_server_getConfig_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])()).locale;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL25hdmlnYXRpb24vcmVhY3Qtc2VydmVyL2dldFNlcnZlckxvY2FsZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFzRDtBQUFBLGVBQWVDO0lBQUksT0FBTSxDQUFDLE1BQU1ELDZFQUFDQSxFQUFDLEVBQUdFLE1BQU07QUFBQTtBQUFzQiIsInNvdXJjZXMiOlsid2VicGFjazovL3NtZS1hbmFseXRpY2EtZG9jcy8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vbmF2aWdhdGlvbi9yZWFjdC1zZXJ2ZXIvZ2V0U2VydmVyTG9jYWxlLmpzPzIyZWQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGUgZnJvbVwiLi4vLi4vc2VydmVyL3JlYWN0LXNlcnZlci9nZXRDb25maWcuanNcIjthc3luYyBmdW5jdGlvbiByKCl7cmV0dXJuKGF3YWl0IGUoKSkubG9jYWxlfWV4cG9ydHtyIGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbImUiLCJyIiwibG9jYWxlIiwiZGVmYXVsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/navigation/react-server/getServerLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Analytica/docs/node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/createSharedNavigationFns.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/shared/createSharedNavigationFns.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ h)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(rsc)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _routing_config_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../routing/config.js */ \"(rsc)/./node_modules/next-intl/dist/esm/routing/config.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/utils.js\");\n/* harmony import */ var _BaseLink_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./BaseLink.js */ \"(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/utils.js\");\n\n\n\n\n\n\n\nfunction h(h, y) {\n    const j = (0,_routing_config_js__WEBPACK_IMPORTED_MODULE_2__.receiveRoutingConfig)(y || {});\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.validateReceivedConfig)(j);\n    const g = j.pathnames, v = \"as-needed\" === j.localePrefix.mode && j.domains || void 0;\n    function q(o, a) {\n        let n, l, u, { href: f, locale: s, ...p } = o;\n        \"object\" == typeof f ? (n = f.pathname, u = f.query, l = f.params) : n = f;\n        const d = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_4__.isLocalizableHref)(f), y = h(), q = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_4__.isPromise)(y) ? (0,react__WEBPACK_IMPORTED_MODULE_1__.use)(y) : y, x = d ? L({\n            locale: s || q,\n            href: null == g ? n : {\n                pathname: n,\n                params: l\n            }\n        }, null != s || v || void 0) : n;\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(_BaseLink_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"], (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_6__[\"extends\"])({\n            ref: a,\n            defaultLocale: j.defaultLocale,\n            href: \"object\" == typeof f ? {\n                ...f,\n                pathname: x\n            } : x,\n            locale: s,\n            localeCookie: j.localeCookie,\n            unprefixed: v && d ? {\n                domains: j.domains.reduce((e, o)=>(e[o.domain] = o.defaultLocale, e), {}),\n                pathname: L({\n                    locale: q,\n                    href: null == g ? {\n                        pathname: n,\n                        query: u\n                    } : {\n                        pathname: n,\n                        query: u,\n                        params: l\n                    }\n                }, !1)\n            } : void 0\n        }, p));\n    }\n    const x = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(q);\n    function L(e, o) {\n        const { href: a, locale: t } = e;\n        let n;\n        return null == g ? \"object\" == typeof a ? (n = a.pathname, a.query && (n += (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.serializeSearchParams)(a.query))) : n = a : n = (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.compileLocalizedPathname)({\n            locale: t,\n            ...(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.normalizeNameOrNameWithParams)(a),\n            pathnames: j.pathnames\n        }), (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.applyPathnamePrefix)(n, t, j, e.domain, o);\n    }\n    function b(e) {\n        return function(o) {\n            for(var a = arguments.length, t = new Array(a > 1 ? a - 1 : 0), n = 1; n < a; n++)t[n - 1] = arguments[n];\n            return e(L(o, o.domain ? void 0 : v), ...t);\n        };\n    }\n    const k = b(next_navigation__WEBPACK_IMPORTED_MODULE_0__.redirect), P = b(next_navigation__WEBPACK_IMPORTED_MODULE_0__.permanentRedirect);\n    return {\n        config: j,\n        Link: x,\n        redirect: k,\n        permanentRedirect: P,\n        getPathname: L\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/createSharedNavigationFns.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/utils.js":
/*!********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/shared/utils.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyPathnamePrefix: () => (/* binding */ d),\n/* harmony export */   compileLocalizedPathname: () => (/* binding */ f),\n/* harmony export */   getBasePath: () => (/* binding */ l),\n/* harmony export */   getRoute: () => (/* binding */ s),\n/* harmony export */   normalizeNameOrNameWithParams: () => (/* binding */ i),\n/* harmony export */   serializeSearchParams: () => (/* binding */ c),\n/* harmony export */   validateReceivedConfig: () => (/* binding */ u)\n/* harmony export */ });\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/utils.js\");\n\nfunction i(e) {\n    return \"string\" == typeof e ? {\n        pathname: e\n    } : e;\n}\nfunction c(e) {\n    function n(e) {\n        return String(e);\n    }\n    const t = new URLSearchParams;\n    for (const [r, o] of Object.entries(e))Array.isArray(o) ? o.forEach((e)=>{\n        t.append(r, n(e));\n    }) : t.set(r, n(o));\n    return \"?\" + t.toString();\n}\nfunction f(e) {\n    let { pathname: n, locale: t, params: r, pathnames: o, query: i } = e;\n    function f(e) {\n        let n = o[e];\n        return n || (n = e), n;\n    }\n    function s(e) {\n        const n = \"string\" == typeof e ? e : e[t];\n        let o = n;\n        if (r && Object.entries(r).forEach((e)=>{\n            let n, t, [r, a] = e;\n            Array.isArray(a) ? (n = \"(\\\\[)?\\\\[...\".concat(r, \"\\\\](\\\\])?\"), t = a.map((e)=>String(e)).join(\"/\")) : (n = \"\\\\[\".concat(r, \"\\\\]\"), t = String(a)), o = o.replace(new RegExp(n, \"g\"), t);\n        }), o = o.replace(/\\[\\[\\.\\.\\..+\\]\\]/g, \"\"), o = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.normalizeTrailingSlash)(o), o.includes(\"[\")) throw new Error(\"Insufficient params provided for localized pathname.\\nTemplate: \".concat(n, \"\\nParams: \").concat(JSON.stringify(r)));\n        return i && (o += c(i)), o;\n    }\n    if (\"string\" == typeof n) {\n        return s(f(n));\n    }\n    {\n        const { pathname: e, ...t } = n;\n        return {\n            ...t,\n            pathname: s(f(e))\n        };\n    }\n}\nfunction s(t, r, o) {\n    const a = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getSortedPathnames)(Object.keys(o)), i = decodeURI(r);\n    for (const e of a){\n        const r = o[e];\n        if (\"string\" == typeof r) {\n            if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.matchesPathname)(r, i)) return e;\n        } else if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.matchesPathname)(r[t], i)) return e;\n    }\n    return r;\n}\nfunction l(e) {\n    let n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : window.location.pathname;\n    return \"/\" === e ? n : n.replace(e, \"\");\n}\nfunction d(e, n, a, i, c) {\n    const { mode: f } = a.localePrefix;\n    let s;\n    if (void 0 !== c) s = c;\n    else if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.isLocalizableHref)(e)) {\n        if (\"always\" === f) s = !0;\n        else if (\"as-needed\" === f) {\n            let e = a.defaultLocale;\n            if (a.domains) {\n                const n = a.domains.find((e)=>e.domain === i);\n                n ? e = n.defaultLocale : i || console.error(\"You're using a routing configuration with `localePrefix: 'as-needed'` in combination with `domains`. In order to compute a correct pathname, you need to provide a `domain` parameter.\\n\\nSee: https://next-intl.dev/docs/routing#domains-localeprefix-asneeded\");\n            }\n            s = e !== n;\n        }\n    }\n    return s ? (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.prefixPathname)((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getLocalePrefix)(n, a.localePrefix), e) : e;\n}\nfunction u(e) {\n    var n;\n    if (\"as-needed\" === (null === (n = e.localePrefix) || void 0 === n ? void 0 : n.mode) && !(\"defaultLocale\" in e)) throw new Error(\"`localePrefix: 'as-needed' requires a `defaultLocale`.\");\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ i)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../_virtual/_rollupPluginBabelHelpers.js */ \"(rsc)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _shared_NextIntlClientProvider_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../shared/NextIntlClientProvider.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\");\n/* harmony import */ var _server_react_server_getLocale_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../server/react-server/getLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getLocale.js\");\n/* harmony import */ var _server_react_server_getNow_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../server/react-server/getNow.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getNow.js\");\n/* harmony import */ var _server_react_server_getTimeZone_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../server/react-server/getTimeZone.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getTimeZone.js\");\n\n\n\n\n\n\nasync function i(i) {\n    let { locale: n, now: s, timeZone: m, ...c } = i;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_shared_NextIntlClientProvider_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"], (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_2__[\"extends\"])({\n        locale: null != n ? n : await (0,_server_react_server_getLocale_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(),\n        now: null != s ? s : await (0,_server_react_server_getNow_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(),\n        timeZone: null != m ? m : await (0,_server_react_server_getTimeZone_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n    }, c));\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LXNlcnZlci9OZXh0SW50bENsaWVudFByb3ZpZGVyU2VydmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQW1FO0FBQXFCO0FBQW1EO0FBQW1EO0FBQWdEO0FBQXFEO0FBQUEsZUFBZU8sRUFBRUEsQ0FBQztJQUFFLElBQUcsRUFBQ0MsUUFBT0MsQ0FBQyxFQUFDQyxLQUFJQyxDQUFDLEVBQUNDLFVBQVNDLENBQUMsRUFBQyxHQUFHQyxHQUFFLEdBQUNQO0lBQUUscUJBQU9MLDBEQUFlLENBQUNDLHlFQUFDQSxFQUFDRixnRkFBQ0EsQ0FBQztRQUFDTyxRQUFPLFFBQU1DLElBQUVBLElBQUUsTUFBTUwsNkVBQUNBO1FBQUdNLEtBQUksUUFBTUMsSUFBRUEsSUFBRSxNQUFNTiwwRUFBQ0E7UUFBR08sVUFBUyxRQUFNQyxJQUFFQSxJQUFFLE1BQU1QLCtFQUFDQTtJQUFFLEdBQUVRO0FBQUc7QUFBc0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zbWUtYW5hbHl0aWNhLWRvY3MvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LXNlcnZlci9OZXh0SW50bENsaWVudFByb3ZpZGVyU2VydmVyLmpzP2U0NjAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0e2V4dGVuZHMgYXMgZX1mcm9tXCIuLi9fdmlydHVhbC9fcm9sbHVwUGx1Z2luQmFiZWxIZWxwZXJzLmpzXCI7aW1wb3J0IHIgZnJvbVwicmVhY3RcIjtpbXBvcnQgdCBmcm9tXCIuLi9zaGFyZWQvTmV4dEludGxDbGllbnRQcm92aWRlci5qc1wiO2ltcG9ydCBvIGZyb21cIi4uL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0TG9jYWxlLmpzXCI7aW1wb3J0IGwgZnJvbVwiLi4vc2VydmVyL3JlYWN0LXNlcnZlci9nZXROb3cuanNcIjtpbXBvcnQgYSBmcm9tXCIuLi9zZXJ2ZXIvcmVhY3Qtc2VydmVyL2dldFRpbWVab25lLmpzXCI7YXN5bmMgZnVuY3Rpb24gaShpKXtsZXR7bG9jYWxlOm4sbm93OnMsdGltZVpvbmU6bSwuLi5jfT1pO3JldHVybiByLmNyZWF0ZUVsZW1lbnQodCxlKHtsb2NhbGU6bnVsbCE9bj9uOmF3YWl0IG8oKSxub3c6bnVsbCE9cz9zOmF3YWl0IGwoKSx0aW1lWm9uZTpudWxsIT1tP206YXdhaXQgYSgpfSxjKSl9ZXhwb3J0e2kgYXMgZGVmYXVsdH07XG4iXSwibmFtZXMiOlsiZXh0ZW5kcyIsImUiLCJyIiwidCIsIm8iLCJsIiwiYSIsImkiLCJsb2NhbGUiLCJuIiwibm93IiwicyIsInRpbWVab25lIiwibSIsImMiLCJjcmVhdGVFbGVtZW50IiwiZGVmYXVsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/react-server/getTranslator.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/react-server/getTranslator.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ t)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var use_intl_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-intl/core */ \"(rsc)/./node_modules/use-intl/dist/development/core.js\");\n\n\nvar t = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(function(r, t) {\n    return (0,use_intl_core__WEBPACK_IMPORTED_MODULE_1__.createTranslator)({\n        ...r,\n        namespace: t\n    });\n});\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LXNlcnZlci9nZXRUcmFuc2xhdG9yLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBOEI7QUFBaUQ7QUFBQSxJQUFJSSxJQUFFSCw0Q0FBQ0EsQ0FBRSxTQUFTQSxDQUFDLEVBQUNHLENBQUM7SUFBRSxPQUFPRCwrREFBQ0EsQ0FBQztRQUFDLEdBQUdGLENBQUM7UUFBQ0ksV0FBVUQ7SUFBQztBQUFFO0FBQXlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc21lLWFuYWx5dGljYS1kb2NzLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9yZWFjdC1zZXJ2ZXIvZ2V0VHJhbnNsYXRvci5qcz8wNjU3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHtjYWNoZSBhcyByfWZyb21cInJlYWN0XCI7aW1wb3J0e2NyZWF0ZVRyYW5zbGF0b3IgYXMgZX1mcm9tXCJ1c2UtaW50bC9jb3JlXCI7dmFyIHQ9cigoZnVuY3Rpb24ocix0KXtyZXR1cm4gZSh7Li4ucixuYW1lc3BhY2U6dH0pfSkpO2V4cG9ydHt0IGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbImNhY2hlIiwiciIsImNyZWF0ZVRyYW5zbGF0b3IiLCJlIiwidCIsIm5hbWVzcGFjZSIsImRlZmF1bHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/getTranslator.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/react-server/useConfig.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/react-server/useConfig.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _server_react_server_getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../server/react-server/getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\n\n\nfunction r(r) {\n    return function(n, r) {\n        try {\n            return (0,react__WEBPACK_IMPORTED_MODULE_0__.use)(r);\n        } catch (e) {\n            throw e instanceof TypeError && e.message.includes(\"Cannot read properties of null (reading 'use')\") ? new Error(\"`\".concat(n, \"` is not callable within an async component. Please refer to https://next-intl.dev/docs/environments/server-client-components#async-components\"), {\n                cause: e\n            }) : e;\n        }\n    }(r, (0,_server_react_server_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])());\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LXNlcnZlci91c2VDb25maWcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE0QjtBQUFtRDtBQUFBLFNBQVNHLEVBQUVBLENBQUM7SUFBRSxPQUFPLFNBQVNELENBQUMsRUFBQ0MsQ0FBQztRQUFFLElBQUc7WUFBQyxPQUFPRiwwQ0FBQ0EsQ0FBQ0U7UUFBRSxFQUFDLE9BQU1GLEdBQUU7WUFBQyxNQUFNQSxhQUFhRyxhQUFXSCxFQUFFSSxPQUFPLENBQUNDLFFBQVEsQ0FBQyxvREFBa0QsSUFBSUMsTUFBTSxJQUFJQyxNQUFNLENBQUNOLEdBQUUsbUpBQWtKO2dCQUFDTyxPQUFNUjtZQUFDLEtBQUdBO1FBQUM7SUFBQyxFQUFFRSxHQUFFRCw2RUFBQ0E7QUFBRztBQUFzQiIsInNvdXJjZXMiOlsid2VicGFjazovL3NtZS1hbmFseXRpY2EtZG9jcy8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vcmVhY3Qtc2VydmVyL3VzZUNvbmZpZy5qcz8xNzYzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHt1c2UgYXMgZX1mcm9tXCJyZWFjdFwiO2ltcG9ydCBuIGZyb21cIi4uL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Q29uZmlnLmpzXCI7ZnVuY3Rpb24gcihyKXtyZXR1cm4gZnVuY3Rpb24obixyKXt0cnl7cmV0dXJuIGUocil9Y2F0Y2goZSl7dGhyb3cgZSBpbnN0YW5jZW9mIFR5cGVFcnJvciYmZS5tZXNzYWdlLmluY2x1ZGVzKFwiQ2Fubm90IHJlYWQgcHJvcGVydGllcyBvZiBudWxsIChyZWFkaW5nICd1c2UnKVwiKT9uZXcgRXJyb3IoXCJgXCIuY29uY2F0KG4sXCJgIGlzIG5vdCBjYWxsYWJsZSB3aXRoaW4gYW4gYXN5bmMgY29tcG9uZW50LiBQbGVhc2UgcmVmZXIgdG8gaHR0cHM6Ly9uZXh0LWludGwuZGV2L2RvY3MvZW52aXJvbm1lbnRzL3NlcnZlci1jbGllbnQtY29tcG9uZW50cyNhc3luYy1jb21wb25lbnRzXCIpLHtjYXVzZTplfSk6ZX19KHIsbigpKX1leHBvcnR7ciBhcyBkZWZhdWx0fTtcbiJdLCJuYW1lcyI6WyJ1c2UiLCJlIiwibiIsInIiLCJUeXBlRXJyb3IiLCJtZXNzYWdlIiwiaW5jbHVkZXMiLCJFcnJvciIsImNvbmNhdCIsImNhdXNlIiwiZGVmYXVsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/react-server/useTranslations.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/react-server/useTranslations.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ o)\n/* harmony export */ });\n/* harmony import */ var _getTranslator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getTranslator.js */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/getTranslator.js\");\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/useConfig.js\");\n\n\nfunction o() {\n    for(var o = arguments.length, n = new Array(o), e = 0; e < o; e++)n[e] = arguments[e];\n    let [s] = n;\n    const a = (0,_useConfig_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"useTranslations\");\n    return (0,_getTranslator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(a, s);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LXNlcnZlci91c2VUcmFuc2xhdGlvbnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWtDO0FBQThCO0FBQUEsU0FBU0U7SUFBSSxJQUFJLElBQUlBLElBQUVDLFVBQVVDLE1BQU0sRUFBQ0MsSUFBRSxJQUFJQyxNQUFNSixJQUFHSyxJQUFFLEdBQUVBLElBQUVMLEdBQUVLLElBQUlGLENBQUMsQ0FBQ0UsRUFBRSxHQUFDSixTQUFTLENBQUNJLEVBQUU7SUFBQyxJQUFHLENBQUNDLEVBQUUsR0FBQ0g7SUFBRSxNQUFNSSxJQUFFUix5REFBQ0EsQ0FBQztJQUFtQixPQUFPRCw2REFBQ0EsQ0FBQ1MsR0FBRUQ7QUFBRTtBQUFzQiIsInNvdXJjZXMiOlsid2VicGFjazovL3NtZS1hbmFseXRpY2EtZG9jcy8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vcmVhY3Qtc2VydmVyL3VzZVRyYW5zbGF0aW9ucy5qcz84NzJiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCByIGZyb21cIi4vZ2V0VHJhbnNsYXRvci5qc1wiO2ltcG9ydCB0IGZyb21cIi4vdXNlQ29uZmlnLmpzXCI7ZnVuY3Rpb24gbygpe2Zvcih2YXIgbz1hcmd1bWVudHMubGVuZ3RoLG49bmV3IEFycmF5KG8pLGU9MDtlPG87ZSsrKW5bZV09YXJndW1lbnRzW2VdO2xldFtzXT1uO2NvbnN0IGE9dChcInVzZVRyYW5zbGF0aW9uc1wiKTtyZXR1cm4gcihhLHMpfWV4cG9ydHtvIGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbInIiLCJ0IiwibyIsImFyZ3VtZW50cyIsImxlbmd0aCIsIm4iLCJBcnJheSIsImUiLCJzIiwiYSIsImRlZmF1bHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useTranslations.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/routing/config.js":
/*!***********************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/routing/config.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   receiveLocaleCookie: () => (/* binding */ o),\n/* harmony export */   receiveLocalePrefixConfig: () => (/* binding */ l),\n/* harmony export */   receiveRoutingConfig: () => (/* binding */ e)\n/* harmony export */ });\nfunction e(e) {\n    var t, n;\n    return {\n        ...e,\n        localePrefix: l(e.localePrefix),\n        localeCookie: o(e.localeCookie),\n        localeDetection: null === (t = e.localeDetection) || void 0 === t || t,\n        alternateLinks: null === (n = e.alternateLinks) || void 0 === n || n\n    };\n}\nfunction o(e) {\n    return !(null != e && !e) && {\n        name: \"NEXT_LOCALE\",\n        maxAge: 31536e3,\n        sameSite: \"lax\",\n        ...\"object\" == typeof e && e\n    };\n}\nfunction l(e) {\n    return \"object\" == typeof e ? e : {\n        mode: e || \"always\"\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JvdXRpbmcvY29uZmlnLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLFNBQVNBLEVBQUVBLENBQUM7SUFBRSxJQUFJQyxHQUFFQztJQUFFLE9BQU07UUFBQyxHQUFHRixDQUFDO1FBQUNHLGNBQWFDLEVBQUVKLEVBQUVHLFlBQVk7UUFBRUUsY0FBYUMsRUFBRU4sRUFBRUssWUFBWTtRQUFFRSxpQkFBZ0IsU0FBUU4sQ0FBQUEsSUFBRUQsRUFBRU8sZUFBZSxLQUFHLEtBQUssTUFBSU4sS0FBR0E7UUFBRU8sZ0JBQWUsU0FBUU4sQ0FBQUEsSUFBRUYsRUFBRVEsY0FBYyxLQUFHLEtBQUssTUFBSU4sS0FBR0E7SUFBQztBQUFDO0FBQUMsU0FBU0ksRUFBRU4sQ0FBQztJQUFFLE9BQU0sQ0FBRSxTQUFNQSxLQUFHLENBQUNBLENBQUFBLEtBQUk7UUFBQ1MsTUFBSztRQUFjQyxRQUFPO1FBQVFDLFVBQVM7UUFBTSxHQUFHLFlBQVUsT0FBT1gsS0FBR0EsQ0FBQztJQUFBO0FBQUM7QUFBQyxTQUFTSSxFQUFFSixDQUFDO0lBQUUsT0FBTSxZQUFVLE9BQU9BLElBQUVBLElBQUU7UUFBQ1ksTUFBS1osS0FBRztJQUFRO0FBQUM7QUFBMkYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zbWUtYW5hbHl0aWNhLWRvY3MvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JvdXRpbmcvY29uZmlnLmpzPzQyMzUiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gZShlKXt2YXIgdCxuO3JldHVybnsuLi5lLGxvY2FsZVByZWZpeDpsKGUubG9jYWxlUHJlZml4KSxsb2NhbGVDb29raWU6byhlLmxvY2FsZUNvb2tpZSksbG9jYWxlRGV0ZWN0aW9uOm51bGw9PT0odD1lLmxvY2FsZURldGVjdGlvbil8fHZvaWQgMD09PXR8fHQsYWx0ZXJuYXRlTGlua3M6bnVsbD09PShuPWUuYWx0ZXJuYXRlTGlua3MpfHx2b2lkIDA9PT1ufHxufX1mdW5jdGlvbiBvKGUpe3JldHVybiEobnVsbCE9ZSYmIWUpJiZ7bmFtZTpcIk5FWFRfTE9DQUxFXCIsbWF4QWdlOjMxNTM2ZTMsc2FtZVNpdGU6XCJsYXhcIiwuLi5cIm9iamVjdFwiPT10eXBlb2YgZSYmZX19ZnVuY3Rpb24gbChlKXtyZXR1cm5cIm9iamVjdFwiPT10eXBlb2YgZT9lOnttb2RlOmV8fFwiYWx3YXlzXCJ9fWV4cG9ydHtvIGFzIHJlY2VpdmVMb2NhbGVDb29raWUsbCBhcyByZWNlaXZlTG9jYWxlUHJlZml4Q29uZmlnLGUgYXMgcmVjZWl2ZVJvdXRpbmdDb25maWd9O1xuIl0sIm5hbWVzIjpbImUiLCJ0IiwibiIsImxvY2FsZVByZWZpeCIsImwiLCJsb2NhbGVDb29raWUiLCJvIiwibG9jYWxlRGV0ZWN0aW9uIiwiYWx0ZXJuYXRlTGlua3MiLCJuYW1lIiwibWF4QWdlIiwic2FtZVNpdGUiLCJtb2RlIiwicmVjZWl2ZUxvY2FsZUNvb2tpZSIsInJlY2VpdmVMb2NhbGVQcmVmaXhDb25maWciLCJyZWNlaXZlUm91dGluZ0NvbmZpZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/routing/config.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocale.js":
/*!******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/RequestLocale.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRequestLocale: () => (/* binding */ a)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/headers.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_headers__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _shared_constants_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared/constants.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/constants.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/utils.js\");\n/* harmony import */ var _RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RequestLocaleCache.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js\");\n\n\n\n\n\nconst i = (0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(async function() {\n    const e = (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.headers)();\n    return (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_2__.isPromise)(e) ? await e : e;\n});\nconst s = (0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(async function() {\n    let t;\n    try {\n        t = (await i()).get(_shared_constants_js__WEBPACK_IMPORTED_MODULE_3__.HEADER_LOCALE_NAME) || void 0;\n    } catch (t) {\n        if (t instanceof Error && \"DYNAMIC_SERVER_USAGE\" === t.digest) {\n            const e = new Error(\"Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering\", {\n                cause: t\n            });\n            throw e.digest = t.digest, e;\n        }\n        throw t;\n    }\n    return t;\n});\nasync function a() {\n    return (0,_RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_4__.getCachedRequestLocale)() || await s();\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCachedRequestLocale: () => (/* binding */ t),\n/* harmony export */   setCachedRequestLocale: () => (/* binding */ c)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst n = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(function() {\n    return {\n        locale: void 0\n    };\n});\nfunction t() {\n    return n().locale;\n}\nfunction c(o) {\n    n().locale = o;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvUmVxdWVzdExvY2FsZUNhY2hlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBOEI7QUFBQSxNQUFNRSxJQUFFRCw0Q0FBQ0EsQ0FBRTtJQUFXLE9BQU07UUFBQ0UsUUFBTyxLQUFLO0lBQUM7QUFBQztBQUFJLFNBQVNDO0lBQUksT0FBT0YsSUFBSUMsTUFBTTtBQUFBO0FBQUMsU0FBU0UsRUFBRUosQ0FBQztJQUFFQyxJQUFJQyxNQUFNLEdBQUNGO0FBQUM7QUFBaUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zbWUtYW5hbHl0aWNhLWRvY3MvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvUmVxdWVzdExvY2FsZUNhY2hlLmpzPzFmOWIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0e2NhY2hlIGFzIG99ZnJvbVwicmVhY3RcIjtjb25zdCBuPW8oKGZ1bmN0aW9uKCl7cmV0dXJue2xvY2FsZTp2b2lkIDB9fSkpO2Z1bmN0aW9uIHQoKXtyZXR1cm4gbigpLmxvY2FsZX1mdW5jdGlvbiBjKG8pe24oKS5sb2NhbGU9b31leHBvcnR7dCBhcyBnZXRDYWNoZWRSZXF1ZXN0TG9jYWxlLGMgYXMgc2V0Q2FjaGVkUmVxdWVzdExvY2FsZX07XG4iXSwibmFtZXMiOlsiY2FjaGUiLCJvIiwibiIsImxvY2FsZSIsInQiLCJjIiwiZ2V0Q2FjaGVkUmVxdWVzdExvY2FsZSIsInNldENhY2hlZFJlcXVlc3RMb2NhbGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleLegacy.js":
/*!************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleLegacy.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRequestLocale: () => (/* binding */ s)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/headers.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_headers__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _shared_constants_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared/constants.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/constants.js\");\n/* harmony import */ var _RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RequestLocaleCache.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js\");\n\n\n\n\n\nconst i = (0,react__WEBPACK_IMPORTED_MODULE_2__.cache)(function() {\n    let n;\n    try {\n        n = (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.headers)().get(_shared_constants_js__WEBPACK_IMPORTED_MODULE_3__.HEADER_LOCALE_NAME);\n    } catch (e) {\n        throw e instanceof Error && \"DYNAMIC_SERVER_USAGE\" === e.digest ? new Error(\"Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering\", {\n            cause: e\n        }) : e;\n    }\n    return n || (console.error(\"\\nUnable to find `next-intl` locale because the middleware didn't run on this request. See https://next-intl.dev/docs/routing/middleware#unable-to-find-locale. The `notFound()` function will be called as a result.\\n\"), (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.notFound)()), n;\n});\nfunction s() {\n    return (0,_RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_4__.getCachedRequestLocale)() || i();\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleLegacy.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getConfig.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ w)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var use_intl_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! use-intl/core */ \"(rsc)/./node_modules/use-intl/dist/development/core.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/utils.js\");\n/* harmony import */ var _RequestLocale_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RequestLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocale.js\");\n/* harmony import */ var _RequestLocaleLegacy_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./RequestLocaleLegacy.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleLegacy.js\");\n/* harmony import */ var next_intl_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl/config */ \"(rsc)/./i18n/request.ts\");\n\n\n\n\n\n\n\nlet c = !1, u = !1;\nconst f = (0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(function() {\n    return new Date;\n});\nconst d = (0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(function() {\n    return Intl.DateTimeFormat().resolvedOptions().timeZone;\n});\nconst m = (0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(async function(t, n) {\n    if (\"function\" != typeof t) throw new Error(\"Invalid i18n request configuration detected.\\n\\nPlease verify that:\\n1. In case you've specified a custom location in your Next.js config, make sure that the path is correct.\\n2. You have a default export in your i18n request configuration file.\\n\\nSee also: https://next-intl.dev/docs/usage/configuration#i18n-request\\n\");\n    const o = {\n        get locale () {\n            return u || (console.warn(\"\\nThe `locale` parameter in `getRequestConfig` is deprecated, please switch to `await requestLocale`. See https://next-intl.dev/blog/next-intl-3-22#await-request-locale\\n\"), u = !0), n || (0,_RequestLocaleLegacy_js__WEBPACK_IMPORTED_MODULE_3__.getRequestLocale)();\n        },\n        get requestLocale () {\n            return n ? Promise.resolve(n) : (0,_RequestLocale_js__WEBPACK_IMPORTED_MODULE_4__.getRequestLocale)();\n        }\n    };\n    let r = t(o);\n    (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_5__.isPromise)(r) && (r = await r);\n    let s = r.locale;\n    return s || (c || (console.error(\"\\nA `locale` is expected to be returned from `getRequestConfig`, but none was returned. This will be an error in the next major version of next-intl.\\n\\nSee: https://next-intl.dev/blog/next-intl-3-22#await-request-locale\\n\"), c = !0), s = await o.requestLocale, s || (console.error(\"\\nUnable to find `next-intl` locale because the middleware didn't run on this request and no `locale` was returned in `getRequestConfig`. See https://next-intl.dev/docs/routing/middleware#unable-to-find-locale. The `notFound()` function will be called as a result.\\n\"), (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.notFound)())), {\n        ...r,\n        locale: s,\n        now: r.now || f(),\n        timeZone: r.timeZone || d()\n    };\n}), p = (0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(use_intl_core__WEBPACK_IMPORTED_MODULE_6__._createIntlFormatters), g = (0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(use_intl_core__WEBPACK_IMPORTED_MODULE_6__._createCache);\nconst w = (0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(async function(e) {\n    const t = await m(next_intl_config__WEBPACK_IMPORTED_MODULE_2__[\"default\"], e);\n    return {\n        ...(0,use_intl_core__WEBPACK_IMPORTED_MODULE_6__.initializeConfig)(t),\n        _formatters: p(g())\n    };\n});\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getLocale.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getLocale.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\n\n\nconst r = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(async function() {\n    const o = await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n    return Promise.resolve(o.locale);\n});\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0TG9jYWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBOEI7QUFBOEI7QUFBQSxNQUFNRyxJQUFFRiw0Q0FBQ0EsQ0FBRTtJQUFpQixNQUFNQSxJQUFFLE1BQU1DLHlEQUFDQTtJQUFHLE9BQU9FLFFBQVFDLE9BQU8sQ0FBQ0osRUFBRUssTUFBTTtBQUFDO0FBQXlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc21lLWFuYWx5dGljYS1kb2NzLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9zZXJ2ZXIvcmVhY3Qtc2VydmVyL2dldExvY2FsZS5qcz9jZDgxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHtjYWNoZSBhcyBvfWZyb21cInJlYWN0XCI7aW1wb3J0IHQgZnJvbVwiLi9nZXRDb25maWcuanNcIjtjb25zdCByPW8oKGFzeW5jIGZ1bmN0aW9uKCl7Y29uc3Qgbz1hd2FpdCB0KCk7cmV0dXJuIFByb21pc2UucmVzb2x2ZShvLmxvY2FsZSl9KSk7ZXhwb3J0e3IgYXMgZGVmYXVsdH07XG4iXSwibmFtZXMiOlsiY2FjaGUiLCJvIiwidCIsInIiLCJQcm9taXNlIiwicmVzb2x2ZSIsImxvY2FsZSIsImRlZmF1bHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getNow.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getNow.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\n\n\nconst t = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(async function(n) {\n    return (await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(n)).now;\n});\nasync function r(n) {\n    return t(null == n ? void 0 : n.locale);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Tm93LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBOEI7QUFBOEI7QUFBQSxNQUFNRyxJQUFFRiw0Q0FBQ0EsQ0FBRSxlQUFlQSxDQUFDO0lBQUUsT0FBTSxDQUFDLE1BQU1DLHlEQUFDQSxDQUFDRCxFQUFDLEVBQUdHLEdBQUc7QUFBQTtBQUFJLGVBQWVDLEVBQUVKLENBQUM7SUFBRSxPQUFPRSxFQUFFLFFBQU1GLElBQUUsS0FBSyxJQUFFQSxFQUFFSyxNQUFNO0FBQUM7QUFBc0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zbWUtYW5hbHl0aWNhLWRvY3MvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Tm93LmpzPzNjMmIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0e2NhY2hlIGFzIG59ZnJvbVwicmVhY3RcIjtpbXBvcnQgbyBmcm9tXCIuL2dldENvbmZpZy5qc1wiO2NvbnN0IHQ9bigoYXN5bmMgZnVuY3Rpb24obil7cmV0dXJuKGF3YWl0IG8obikpLm5vd30pKTthc3luYyBmdW5jdGlvbiByKG4pe3JldHVybiB0KG51bGw9PW4/dm9pZCAwOm4ubG9jYWxlKX1leHBvcnR7ciBhcyBkZWZhdWx0fTtcbiJdLCJuYW1lcyI6WyJjYWNoZSIsIm4iLCJvIiwidCIsIm5vdyIsInIiLCJsb2NhbGUiLCJkZWZhdWx0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getNow.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ t)\n/* harmony export */ });\nfunction t(t) {\n    return t;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0UmVxdWVzdENvbmZpZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsU0FBU0EsRUFBRUEsQ0FBQztJQUFFLE9BQU9BO0FBQUM7QUFBc0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zbWUtYW5hbHl0aWNhLWRvY3MvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0UmVxdWVzdENvbmZpZy5qcz8yODNiIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIHQodCl7cmV0dXJuIHR9ZXhwb3J0e3QgYXMgZGVmYXVsdH07XG4iXSwibmFtZXMiOlsidCIsImRlZmF1bHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getTimeZone.js":
/*!****************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getTimeZone.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\n\n\nconst o = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(async function(t) {\n    return (await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(t)).timeZone;\n});\nasync function r(t) {\n    return o(null == t ? void 0 : t.locale);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0VGltZVpvbmUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE4QjtBQUE4QjtBQUFBLE1BQU1HLElBQUVGLDRDQUFDQSxDQUFFLGVBQWVBLENBQUM7SUFBRSxPQUFNLENBQUMsTUFBTUMseURBQUNBLENBQUNELEVBQUMsRUFBR0csUUFBUTtBQUFBO0FBQUksZUFBZUMsRUFBRUosQ0FBQztJQUFFLE9BQU9FLEVBQUUsUUFBTUYsSUFBRSxLQUFLLElBQUVBLEVBQUVLLE1BQU07QUFBQztBQUFzQiIsInNvdXJjZXMiOlsid2VicGFjazovL3NtZS1hbmFseXRpY2EtZG9jcy8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vc2VydmVyL3JlYWN0LXNlcnZlci9nZXRUaW1lWm9uZS5qcz8yNmU1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHtjYWNoZSBhcyB0fWZyb21cInJlYWN0XCI7aW1wb3J0IG4gZnJvbVwiLi9nZXRDb25maWcuanNcIjtjb25zdCBvPXQoKGFzeW5jIGZ1bmN0aW9uKHQpe3JldHVybihhd2FpdCBuKHQpKS50aW1lWm9uZX0pKTthc3luYyBmdW5jdGlvbiByKHQpe3JldHVybiBvKG51bGw9PXQ/dm9pZCAwOnQubG9jYWxlKX1leHBvcnR7ciBhcyBkZWZhdWx0fTtcbiJdLCJuYW1lcyI6WyJjYWNoZSIsInQiLCJuIiwibyIsInRpbWVab25lIiwiciIsImxvY2FsZSIsImRlZmF1bHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getTimeZone.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Analytica/docs/node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/shared/constants.js":
/*!*************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/constants.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HEADER_LOCALE_NAME: () => (/* binding */ o),\n/* harmony export */   LOCALE_SEGMENT_NAME: () => (/* binding */ L)\n/* harmony export */ });\nconst o = \"X-NEXT-INTL-LOCALE\", L = \"locale\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NoYXJlZC9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSxNQUFNQSxJQUFFLHNCQUFxQkMsSUFBRTtBQUFrRSIsInNvdXJjZXMiOlsid2VicGFjazovL3NtZS1hbmFseXRpY2EtZG9jcy8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vc2hhcmVkL2NvbnN0YW50cy5qcz9iZTZjIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IG89XCJYLU5FWFQtSU5UTC1MT0NBTEVcIixMPVwibG9jYWxlXCI7ZXhwb3J0e28gYXMgSEVBREVSX0xPQ0FMRV9OQU1FLEwgYXMgTE9DQUxFX1NFR01FTlRfTkFNRX07XG4iXSwibmFtZXMiOlsibyIsIkwiLCJIRUFERVJfTE9DQUxFX05BTUUiLCJMT0NBTEVfU0VHTUVOVF9OQU1FIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/shared/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/shared/utils.js":
/*!*********************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/utils.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLocaleAsPrefix: () => (/* binding */ l),\n/* harmony export */   getLocalePrefix: () => (/* binding */ f),\n/* harmony export */   getSortedPathnames: () => (/* binding */ d),\n/* harmony export */   hasPathnamePrefixed: () => (/* binding */ i),\n/* harmony export */   isLocalizableHref: () => (/* binding */ n),\n/* harmony export */   isPromise: () => (/* binding */ v),\n/* harmony export */   localizeHref: () => (/* binding */ t),\n/* harmony export */   matchesPathname: () => (/* binding */ c),\n/* harmony export */   normalizeTrailingSlash: () => (/* binding */ o),\n/* harmony export */   prefixHref: () => (/* binding */ e),\n/* harmony export */   prefixPathname: () => (/* binding */ u),\n/* harmony export */   templateToRegex: () => (/* binding */ s),\n/* harmony export */   unprefixPathname: () => (/* binding */ r)\n/* harmony export */ });\nfunction n(n) {\n    return function(n) {\n        return \"object\" == typeof n ? null == n.host && null == n.hostname : !/^[a-z]+:/i.test(n);\n    }(n) && !function(n) {\n        const t = \"object\" == typeof n ? n.pathname : n;\n        return null != t && !t.startsWith(\"/\");\n    }(n);\n}\nfunction t(t, r) {\n    let u = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : r, o = arguments.length > 3 ? arguments[3] : void 0, c = arguments.length > 4 ? arguments[4] : void 0;\n    if (!n(t)) return t;\n    const f = r !== u, l = i(c, o);\n    return (f || l) && null != c ? e(t, c) : t;\n}\nfunction e(n, t) {\n    let e;\n    return \"string\" == typeof n ? e = u(t, n) : (e = {\n        ...n\n    }, n.pathname && (e.pathname = u(t, n.pathname))), e;\n}\nfunction r(n, t) {\n    return n.replace(new RegExp(\"^\".concat(t)), \"\") || \"/\";\n}\nfunction u(n, t) {\n    let e = n;\n    return /^\\/(\\?.*)?$/.test(t) && (t = t.slice(1)), e += t, e;\n}\nfunction i(n, t) {\n    return t === n || t.startsWith(\"\".concat(n, \"/\"));\n}\nfunction o(n) {\n    const t = function() {\n        try {\n            return \"true\" === process.env._next_intl_trailing_slash;\n        } catch (n) {\n            return !1;\n        }\n    }();\n    if (\"/\" !== n) {\n        const e = n.endsWith(\"/\");\n        t && !e ? n += \"/\" : !t && e && (n = n.slice(0, -1));\n    }\n    return n;\n}\nfunction c(n, t) {\n    const e = o(n), r = o(t);\n    return s(e).test(r);\n}\nfunction f(n, t) {\n    var e;\n    return \"never\" !== t.mode && (null === (e = t.prefixes) || void 0 === e ? void 0 : e[n]) || l(n);\n}\nfunction l(n) {\n    return \"/\" + n;\n}\nfunction s(n) {\n    const t = n.replace(/\\[\\[(\\.\\.\\.[^\\]]+)\\]\\]/g, \"?(.*)\").replace(/\\[(\\.\\.\\.[^\\]]+)\\]/g, \"(.+)\").replace(/\\[([^\\]]+)\\]/g, \"([^/]+)\");\n    return new RegExp(\"^\".concat(t, \"$\"));\n}\nfunction a(n) {\n    return n.includes(\"[[...\");\n}\nfunction p(n) {\n    return n.includes(\"[...\");\n}\nfunction h(n) {\n    return n.includes(\"[\");\n}\nfunction g(n, t) {\n    const e = n.split(\"/\"), r = t.split(\"/\"), u = Math.max(e.length, r.length);\n    for(let n = 0; n < u; n++){\n        const t = e[n], u = r[n];\n        if (!t && u) return -1;\n        if (t && !u) return 1;\n        if (t || u) {\n            if (!h(t) && h(u)) return -1;\n            if (h(t) && !h(u)) return 1;\n            if (!p(t) && p(u)) return -1;\n            if (p(t) && !p(u)) return 1;\n            if (!a(t) && a(u)) return -1;\n            if (a(t) && !a(u)) return 1;\n        }\n    }\n    return 0;\n}\nfunction d(n) {\n    return n.sort(g);\n}\nfunction v(n) {\n    return \"function\" == typeof n.then;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/shared/utils.js\n");

/***/ })

};
;