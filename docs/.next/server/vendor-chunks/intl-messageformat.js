"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/intl-messageformat";
exports.ids = ["vendor-chunks/intl-messageformat"];
exports.modules = {

/***/ "(ssr)/./node_modules/intl-messageformat/lib/index.js":
/*!******************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorCode: () => (/* reexport safe */ _src_error__WEBPACK_IMPORTED_MODULE_0__.ErrorCode),\n/* harmony export */   FormatError: () => (/* reexport safe */ _src_error__WEBPACK_IMPORTED_MODULE_0__.FormatError),\n/* harmony export */   IntlMessageFormat: () => (/* reexport safe */ _src_core__WEBPACK_IMPORTED_MODULE_2__.IntlMessageFormat),\n/* harmony export */   InvalidValueError: () => (/* reexport safe */ _src_error__WEBPACK_IMPORTED_MODULE_0__.InvalidValueError),\n/* harmony export */   InvalidValueTypeError: () => (/* reexport safe */ _src_error__WEBPACK_IMPORTED_MODULE_0__.InvalidValueTypeError),\n/* harmony export */   MissingValueError: () => (/* reexport safe */ _src_error__WEBPACK_IMPORTED_MODULE_0__.MissingValueError),\n/* harmony export */   PART_TYPE: () => (/* reexport safe */ _src_formatters__WEBPACK_IMPORTED_MODULE_1__.PART_TYPE),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   formatToParts: () => (/* reexport safe */ _src_formatters__WEBPACK_IMPORTED_MODULE_1__.formatToParts),\n/* harmony export */   isFormatXMLElementFn: () => (/* reexport safe */ _src_formatters__WEBPACK_IMPORTED_MODULE_1__.isFormatXMLElementFn)\n/* harmony export */ });\n/* harmony import */ var _src_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./src/core */ \"(ssr)/./node_modules/intl-messageformat/lib/src/core.js\");\n/* harmony import */ var _src_error__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./src/error */ \"(ssr)/./node_modules/intl-messageformat/lib/src/error.js\");\n/* harmony import */ var _src_formatters__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./src/formatters */ \"(ssr)/./node_modules/intl-messageformat/lib/src/formatters.js\");\n/*\nCopyright (c) 2014, Yahoo! Inc. All rights reserved.\nCopyrights licensed under the New BSD License.\nSee the accompanying LICENSE file for terms.\n*/ \n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_src_core__WEBPACK_IMPORTED_MODULE_2__.IntlMessageFormat);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaW50bC1tZXNzYWdlZm9ybWF0L2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7Ozs7QUFJQSxHQUMrQztBQUNwQjtBQUNDO0FBQ0s7QUFDSjtBQUM3QixpRUFBZUEsd0RBQWlCQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc21lLWFuYWx5dGljYS1kb2NzLy4vbm9kZV9tb2R1bGVzL2ludGwtbWVzc2FnZWZvcm1hdC9saWIvaW5kZXguanM/ZTEyYiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKlxuQ29weXJpZ2h0IChjKSAyMDE0LCBZYWhvbyEgSW5jLiBBbGwgcmlnaHRzIHJlc2VydmVkLlxuQ29weXJpZ2h0cyBsaWNlbnNlZCB1bmRlciB0aGUgTmV3IEJTRCBMaWNlbnNlLlxuU2VlIHRoZSBhY2NvbXBhbnlpbmcgTElDRU5TRSBmaWxlIGZvciB0ZXJtcy5cbiovXG5pbXBvcnQgeyBJbnRsTWVzc2FnZUZvcm1hdCB9IGZyb20gJy4vc3JjL2NvcmUnO1xuZXhwb3J0ICogZnJvbSAnLi9zcmMvY29yZSc7XG5leHBvcnQgKiBmcm9tICcuL3NyYy9lcnJvcic7XG5leHBvcnQgKiBmcm9tICcuL3NyYy9mb3JtYXR0ZXJzJztcbmV4cG9ydCB7IEludGxNZXNzYWdlRm9ybWF0IH07XG5leHBvcnQgZGVmYXVsdCBJbnRsTWVzc2FnZUZvcm1hdDtcbiJdLCJuYW1lcyI6WyJJbnRsTWVzc2FnZUZvcm1hdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/intl-messageformat/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/intl-messageformat/lib/src/core.js":
/*!*********************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/src/core.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntlMessageFormat: () => (/* binding */ IntlMessageFormat)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @formatjs/fast-memoize */ \"(ssr)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n/* harmony import */ var _formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @formatjs/icu-messageformat-parser */ \"(ssr)/./node_modules/@formatjs/icu-messageformat-parser/lib/index.js\");\n/* harmony import */ var _formatters__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./formatters */ \"(ssr)/./node_modules/intl-messageformat/lib/src/formatters.js\");\n/*\nCopyright (c) 2014, Yahoo! Inc. All rights reserved.\nCopyrights licensed under the New BSD License.\nSee the accompanying LICENSE file for terms.\n*/ \n\n\n\n// -- MessageFormat --------------------------------------------------------\nfunction mergeConfig(c1, c2) {\n    if (!c2) {\n        return c1;\n    }\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, c1 || {}), c2 || {}), Object.keys(c1).reduce(function(all, k) {\n        all[k] = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, c1[k]), c2[k] || {});\n        return all;\n    }, {}));\n}\nfunction mergeConfigs(defaultConfig, configs) {\n    if (!configs) {\n        return defaultConfig;\n    }\n    return Object.keys(defaultConfig).reduce(function(all, k) {\n        all[k] = mergeConfig(defaultConfig[k], configs[k]);\n        return all;\n    }, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, defaultConfig));\n}\nfunction createFastMemoizeCache(store) {\n    return {\n        create: function() {\n            return {\n                get: function(key) {\n                    return store[key];\n                },\n                set: function(key, value) {\n                    store[key] = value;\n                }\n            };\n        }\n    };\n}\nfunction createDefaultFormatters(cache) {\n    if (cache === void 0) {\n        cache = {\n            number: {},\n            dateTime: {},\n            pluralRules: {}\n        };\n    }\n    return {\n        getNumberFormat: (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.memoize)(function() {\n            var _a;\n            var args = [];\n            for(var _i = 0; _i < arguments.length; _i++){\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.NumberFormat).bind.apply(_a, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__spreadArray)([\n                void 0\n            ], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.number),\n            strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.strategies.variadic\n        }),\n        getDateTimeFormat: (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.memoize)(function() {\n            var _a;\n            var args = [];\n            for(var _i = 0; _i < arguments.length; _i++){\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.DateTimeFormat).bind.apply(_a, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__spreadArray)([\n                void 0\n            ], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.dateTime),\n            strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.strategies.variadic\n        }),\n        getPluralRules: (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.memoize)(function() {\n            var _a;\n            var args = [];\n            for(var _i = 0; _i < arguments.length; _i++){\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.PluralRules).bind.apply(_a, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__spreadArray)([\n                void 0\n            ], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.pluralRules),\n            strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.strategies.variadic\n        })\n    };\n}\nvar IntlMessageFormat = /** @class */ function() {\n    function IntlMessageFormat(message, locales, overrideFormats, opts) {\n        if (locales === void 0) {\n            locales = IntlMessageFormat.defaultLocale;\n        }\n        var _this = this;\n        this.formatterCache = {\n            number: {},\n            dateTime: {},\n            pluralRules: {}\n        };\n        this.format = function(values) {\n            var parts = _this.formatToParts(values);\n            // Hot path for straight simple msg translations\n            if (parts.length === 1) {\n                return parts[0].value;\n            }\n            var result = parts.reduce(function(all, part) {\n                if (!all.length || part.type !== _formatters__WEBPACK_IMPORTED_MODULE_3__.PART_TYPE.literal || typeof all[all.length - 1] !== \"string\") {\n                    all.push(part.value);\n                } else {\n                    all[all.length - 1] += part.value;\n                }\n                return all;\n            }, []);\n            if (result.length <= 1) {\n                return result[0] || \"\";\n            }\n            return result;\n        };\n        this.formatToParts = function(values) {\n            return (0,_formatters__WEBPACK_IMPORTED_MODULE_3__.formatToParts)(_this.ast, _this.locales, _this.formatters, _this.formats, values, undefined, _this.message);\n        };\n        this.resolvedOptions = function() {\n            var _a;\n            return {\n                locale: ((_a = _this.resolvedLocale) === null || _a === void 0 ? void 0 : _a.toString()) || Intl.NumberFormat.supportedLocalesOf(_this.locales)[0]\n            };\n        };\n        this.getAst = function() {\n            return _this.ast;\n        };\n        // Defined first because it's used to build the format pattern.\n        this.locales = locales;\n        this.resolvedLocale = IntlMessageFormat.resolveLocale(locales);\n        if (typeof message === \"string\") {\n            this.message = message;\n            if (!IntlMessageFormat.__parse) {\n                throw new TypeError(\"IntlMessageFormat.__parse must be set to process `message` of type `string`\");\n            }\n            var _a = opts || {}, formatters = _a.formatters, parseOpts = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__rest)(_a, [\n                \"formatters\"\n            ]);\n            // Parse string messages into an AST.\n            this.ast = IntlMessageFormat.__parse(message, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, parseOpts), {\n                locale: this.resolvedLocale\n            }));\n        } else {\n            this.ast = message;\n        }\n        if (!Array.isArray(this.ast)) {\n            throw new TypeError(\"A message must be provided as a String or AST.\");\n        }\n        // Creates a new object with the specified `formats` merged with the default\n        // formats.\n        this.formats = mergeConfigs(IntlMessageFormat.formats, overrideFormats);\n        this.formatters = opts && opts.formatters || createDefaultFormatters(this.formatterCache);\n    }\n    Object.defineProperty(IntlMessageFormat, \"defaultLocale\", {\n        get: function() {\n            if (!IntlMessageFormat.memoizedDefaultLocale) {\n                IntlMessageFormat.memoizedDefaultLocale = new Intl.NumberFormat().resolvedOptions().locale;\n            }\n            return IntlMessageFormat.memoizedDefaultLocale;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    IntlMessageFormat.memoizedDefaultLocale = null;\n    IntlMessageFormat.resolveLocale = function(locales) {\n        if (typeof Intl.Locale === \"undefined\") {\n            return;\n        }\n        var supportedLocales = Intl.NumberFormat.supportedLocalesOf(locales);\n        if (supportedLocales.length > 0) {\n            return new Intl.Locale(supportedLocales[0]);\n        }\n        return new Intl.Locale(typeof locales === \"string\" ? locales : locales[0]);\n    };\n    IntlMessageFormat.__parse = _formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_1__.parse;\n    // Default format options used as the prototype of the `formats` provided to the\n    // constructor. These are used when constructing the internal Intl.NumberFormat\n    // and Intl.DateTimeFormat instances.\n    IntlMessageFormat.formats = {\n        number: {\n            integer: {\n                maximumFractionDigits: 0\n            },\n            currency: {\n                style: \"currency\"\n            },\n            percent: {\n                style: \"percent\"\n            }\n        },\n        date: {\n            short: {\n                month: \"numeric\",\n                day: \"numeric\",\n                year: \"2-digit\"\n            },\n            medium: {\n                month: \"short\",\n                day: \"numeric\",\n                year: \"numeric\"\n            },\n            long: {\n                month: \"long\",\n                day: \"numeric\",\n                year: \"numeric\"\n            },\n            full: {\n                weekday: \"long\",\n                month: \"long\",\n                day: \"numeric\",\n                year: \"numeric\"\n            }\n        },\n        time: {\n            short: {\n                hour: \"numeric\",\n                minute: \"numeric\"\n            },\n            medium: {\n                hour: \"numeric\",\n                minute: \"numeric\",\n                second: \"numeric\"\n            },\n            long: {\n                hour: \"numeric\",\n                minute: \"numeric\",\n                second: \"numeric\",\n                timeZoneName: \"short\"\n            },\n            full: {\n                hour: \"numeric\",\n                minute: \"numeric\",\n                second: \"numeric\",\n                timeZoneName: \"short\"\n            }\n        }\n    };\n    return IntlMessageFormat;\n}();\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/intl-messageformat/lib/src/core.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/intl-messageformat/lib/src/error.js":
/*!**********************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/src/error.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorCode: () => (/* binding */ ErrorCode),\n/* harmony export */   FormatError: () => (/* binding */ FormatError),\n/* harmony export */   InvalidValueError: () => (/* binding */ InvalidValueError),\n/* harmony export */   InvalidValueTypeError: () => (/* binding */ InvalidValueTypeError),\n/* harmony export */   MissingValueError: () => (/* binding */ MissingValueError)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\n\nvar ErrorCode;\n(function(ErrorCode) {\n    // When we have a placeholder but no value to format\n    ErrorCode[\"MISSING_VALUE\"] = \"MISSING_VALUE\";\n    // When value supplied is invalid\n    ErrorCode[\"INVALID_VALUE\"] = \"INVALID_VALUE\";\n    // When we need specific Intl API but it's not available\n    ErrorCode[\"MISSING_INTL_API\"] = \"MISSING_INTL_API\";\n})(ErrorCode || (ErrorCode = {}));\nvar FormatError = /** @class */ function(_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(FormatError, _super);\n    function FormatError(msg, code, originalMessage) {\n        var _this = _super.call(this, msg) || this;\n        _this.code = code;\n        _this.originalMessage = originalMessage;\n        return _this;\n    }\n    FormatError.prototype.toString = function() {\n        return \"[formatjs Error: \".concat(this.code, \"] \").concat(this.message);\n    };\n    return FormatError;\n}(Error);\n\nvar InvalidValueError = /** @class */ function(_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(InvalidValueError, _super);\n    function InvalidValueError(variableId, value, options, originalMessage) {\n        return _super.call(this, 'Invalid values for \"'.concat(variableId, '\": \"').concat(value, '\". Options are \"').concat(Object.keys(options).join('\", \"'), '\"'), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueError;\n}(FormatError);\n\nvar InvalidValueTypeError = /** @class */ function(_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(InvalidValueTypeError, _super);\n    function InvalidValueTypeError(value, type, originalMessage) {\n        return _super.call(this, 'Value for \"'.concat(value, '\" must be of type ').concat(type), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueTypeError;\n}(FormatError);\n\nvar MissingValueError = /** @class */ function(_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(MissingValueError, _super);\n    function MissingValueError(variableId, originalMessage) {\n        return _super.call(this, 'The intl string context variable \"'.concat(variableId, '\" was not provided to the string \"').concat(originalMessage, '\"'), ErrorCode.MISSING_VALUE, originalMessage) || this;\n    }\n    return MissingValueError;\n}(FormatError);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/intl-messageformat/lib/src/error.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/intl-messageformat/lib/src/formatters.js":
/*!***************************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/src/formatters.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PART_TYPE: () => (/* binding */ PART_TYPE),\n/* harmony export */   formatToParts: () => (/* binding */ formatToParts),\n/* harmony export */   isFormatXMLElementFn: () => (/* binding */ isFormatXMLElementFn)\n/* harmony export */ });\n/* harmony import */ var _formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @formatjs/icu-messageformat-parser */ \"(ssr)/./node_modules/@formatjs/icu-messageformat-parser/lib/index.js\");\n/* harmony import */ var _error__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./error */ \"(ssr)/./node_modules/intl-messageformat/lib/src/error.js\");\n\n\nvar PART_TYPE;\n(function(PART_TYPE) {\n    PART_TYPE[PART_TYPE[\"literal\"] = 0] = \"literal\";\n    PART_TYPE[PART_TYPE[\"object\"] = 1] = \"object\";\n})(PART_TYPE || (PART_TYPE = {}));\nfunction mergeLiteral(parts) {\n    if (parts.length < 2) {\n        return parts;\n    }\n    return parts.reduce(function(all, part) {\n        var lastPart = all[all.length - 1];\n        if (!lastPart || lastPart.type !== PART_TYPE.literal || part.type !== PART_TYPE.literal) {\n            all.push(part);\n        } else {\n            lastPart.value += part.value;\n        }\n        return all;\n    }, []);\n}\nfunction isFormatXMLElementFn(el) {\n    return typeof el === \"function\";\n}\n// TODO(skeleton): add skeleton support\nfunction formatToParts(els, locales, formatters, formats, values, currentPluralValue, // For debugging\noriginalMessage) {\n    // Hot path for straight simple msg translations\n    if (els.length === 1 && (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isLiteralElement)(els[0])) {\n        return [\n            {\n                type: PART_TYPE.literal,\n                value: els[0].value\n            }\n        ];\n    }\n    var result = [];\n    for(var _i = 0, els_1 = els; _i < els_1.length; _i++){\n        var el = els_1[_i];\n        // Exit early for string parts.\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isLiteralElement)(el)) {\n            result.push({\n                type: PART_TYPE.literal,\n                value: el.value\n            });\n            continue;\n        }\n        // TODO: should this part be literal type?\n        // Replace `#` in plural rules with the actual numeric value.\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isPoundElement)(el)) {\n            if (typeof currentPluralValue === \"number\") {\n                result.push({\n                    type: PART_TYPE.literal,\n                    value: formatters.getNumberFormat(locales).format(currentPluralValue)\n                });\n            }\n            continue;\n        }\n        var varName = el.value;\n        // Enforce that all required values are provided by the caller.\n        if (!(values && varName in values)) {\n            throw new _error__WEBPACK_IMPORTED_MODULE_1__.MissingValueError(varName, originalMessage);\n        }\n        var value = values[varName];\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isArgumentElement)(el)) {\n            if (!value || typeof value === \"string\" || typeof value === \"number\") {\n                value = typeof value === \"string\" || typeof value === \"number\" ? String(value) : \"\";\n            }\n            result.push({\n                type: typeof value === \"string\" ? PART_TYPE.literal : PART_TYPE.object,\n                value: value\n            });\n            continue;\n        }\n        // Recursively format plural and select parts' option — which can be a\n        // nested pattern structure. The choosing of the option to use is\n        // abstracted-by and delegated-to the part helper object.\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isDateElement)(el)) {\n            var style = typeof el.style === \"string\" ? formats.date[el.style] : (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isDateTimeSkeleton)(el.style) ? el.style.parsedOptions : undefined;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters.getDateTimeFormat(locales, style).format(value)\n            });\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isTimeElement)(el)) {\n            var style = typeof el.style === \"string\" ? formats.time[el.style] : (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isDateTimeSkeleton)(el.style) ? el.style.parsedOptions : formats.time.medium;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters.getDateTimeFormat(locales, style).format(value)\n            });\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isNumberElement)(el)) {\n            var style = typeof el.style === \"string\" ? formats.number[el.style] : (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isNumberSkeleton)(el.style) ? el.style.parsedOptions : undefined;\n            if (style && style.scale) {\n                value = value * (style.scale || 1);\n            }\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters.getNumberFormat(locales, style).format(value)\n            });\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isTagElement)(el)) {\n            var children = el.children, value_1 = el.value;\n            var formatFn = values[value_1];\n            if (!isFormatXMLElementFn(formatFn)) {\n                throw new _error__WEBPACK_IMPORTED_MODULE_1__.InvalidValueTypeError(value_1, \"function\", originalMessage);\n            }\n            var parts = formatToParts(children, locales, formatters, formats, values, currentPluralValue);\n            var chunks = formatFn(parts.map(function(p) {\n                return p.value;\n            }));\n            if (!Array.isArray(chunks)) {\n                chunks = [\n                    chunks\n                ];\n            }\n            result.push.apply(result, chunks.map(function(c) {\n                return {\n                    type: typeof c === \"string\" ? PART_TYPE.literal : PART_TYPE.object,\n                    value: c\n                };\n            }));\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isSelectElement)(el)) {\n            var opt = el.options[value] || el.options.other;\n            if (!opt) {\n                throw new _error__WEBPACK_IMPORTED_MODULE_1__.InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values));\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isPluralElement)(el)) {\n            var opt = el.options[\"=\".concat(value)];\n            if (!opt) {\n                if (!Intl.PluralRules) {\n                    throw new _error__WEBPACK_IMPORTED_MODULE_1__.FormatError('Intl.PluralRules is not available in this environment.\\nTry polyfilling it using \"@formatjs/intl-pluralrules\"\\n', _error__WEBPACK_IMPORTED_MODULE_1__.ErrorCode.MISSING_INTL_API, originalMessage);\n                }\n                var rule = formatters.getPluralRules(locales, {\n                    type: el.pluralType\n                }).select(value - (el.offset || 0));\n                opt = el.options[rule] || el.options.other;\n            }\n            if (!opt) {\n                throw new _error__WEBPACK_IMPORTED_MODULE_1__.InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values, value - (el.offset || 0)));\n            continue;\n        }\n    }\n    return mergeLiteral(result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaW50bC1tZXNzYWdlZm9ybWF0L2xpYi9zcmMvZm9ybWF0dGVycy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUErTztBQUNoSTtBQUN4RyxJQUFJZ0IsVUFBVTtBQUNwQixVQUFVQSxTQUFTO0lBQ2hCQSxTQUFTLENBQUNBLFNBQVMsQ0FBQyxVQUFVLEdBQUcsRUFBRSxHQUFHO0lBQ3RDQSxTQUFTLENBQUNBLFNBQVMsQ0FBQyxTQUFTLEdBQUcsRUFBRSxHQUFHO0FBQ3pDLEdBQUdBLGFBQWNBLENBQUFBLFlBQVksQ0FBQztBQUM5QixTQUFTQyxhQUFhQyxLQUFLO0lBQ3ZCLElBQUlBLE1BQU1DLE1BQU0sR0FBRyxHQUFHO1FBQ2xCLE9BQU9EO0lBQ1g7SUFDQSxPQUFPQSxNQUFNRSxNQUFNLENBQUMsU0FBVUMsR0FBRyxFQUFFQyxJQUFJO1FBQ25DLElBQUlDLFdBQVdGLEdBQUcsQ0FBQ0EsSUFBSUYsTUFBTSxHQUFHLEVBQUU7UUFDbEMsSUFBSSxDQUFDSSxZQUNEQSxTQUFTQyxJQUFJLEtBQUtSLFVBQVVTLE9BQU8sSUFDbkNILEtBQUtFLElBQUksS0FBS1IsVUFBVVMsT0FBTyxFQUFFO1lBQ2pDSixJQUFJSyxJQUFJLENBQUNKO1FBQ2IsT0FDSztZQUNEQyxTQUFTSSxLQUFLLElBQUlMLEtBQUtLLEtBQUs7UUFDaEM7UUFDQSxPQUFPTjtJQUNYLEdBQUcsRUFBRTtBQUNUO0FBQ08sU0FBU08scUJBQXFCQyxFQUFFO0lBQ25DLE9BQU8sT0FBT0EsT0FBTztBQUN6QjtBQUNBLHVDQUF1QztBQUNoQyxTQUFTQyxjQUFjQyxHQUFHLEVBQUVDLE9BQU8sRUFBRUMsVUFBVSxFQUFFQyxPQUFPLEVBQUVDLE1BQU0sRUFBRUMsa0JBQWtCLEVBQzNGLGdCQUFnQjtBQUNoQkMsZUFBZTtJQUNYLGdEQUFnRDtJQUNoRCxJQUFJTixJQUFJWixNQUFNLEtBQUssS0FBS2hCLG9GQUFnQkEsQ0FBQzRCLEdBQUcsQ0FBQyxFQUFFLEdBQUc7UUFDOUMsT0FBTztZQUNIO2dCQUNJUCxNQUFNUixVQUFVUyxPQUFPO2dCQUN2QkUsT0FBT0ksR0FBRyxDQUFDLEVBQUUsQ0FBQ0osS0FBSztZQUN2QjtTQUNIO0lBQ0w7SUFDQSxJQUFJVyxTQUFTLEVBQUU7SUFDZixJQUFLLElBQUlDLEtBQUssR0FBR0MsUUFBUVQsS0FBS1EsS0FBS0MsTUFBTXJCLE1BQU0sRUFBRW9CLEtBQU07UUFDbkQsSUFBSVYsS0FBS1csS0FBSyxDQUFDRCxHQUFHO1FBQ2xCLCtCQUErQjtRQUMvQixJQUFJcEMsb0ZBQWdCQSxDQUFDMEIsS0FBSztZQUN0QlMsT0FBT1osSUFBSSxDQUFDO2dCQUNSRixNQUFNUixVQUFVUyxPQUFPO2dCQUN2QkUsT0FBT0UsR0FBR0YsS0FBSztZQUNuQjtZQUNBO1FBQ0o7UUFDQSwwQ0FBMEM7UUFDMUMsNkRBQTZEO1FBQzdELElBQUlwQixrRkFBY0EsQ0FBQ3NCLEtBQUs7WUFDcEIsSUFBSSxPQUFPTyx1QkFBdUIsVUFBVTtnQkFDeENFLE9BQU9aLElBQUksQ0FBQztvQkFDUkYsTUFBTVIsVUFBVVMsT0FBTztvQkFDdkJFLE9BQU9NLFdBQVdRLGVBQWUsQ0FBQ1QsU0FBU1UsTUFBTSxDQUFDTjtnQkFDdEQ7WUFDSjtZQUNBO1FBQ0o7UUFDQSxJQUFJTyxVQUFVZCxHQUFHRixLQUFLO1FBQ3RCLCtEQUErRDtRQUMvRCxJQUFJLENBQUVRLENBQUFBLFVBQVVRLFdBQVdSLE1BQUssR0FBSTtZQUNoQyxNQUFNLElBQUlwQixxREFBaUJBLENBQUM0QixTQUFTTjtRQUN6QztRQUNBLElBQUlWLFFBQVFRLE1BQU0sQ0FBQ1EsUUFBUTtRQUMzQixJQUFJM0MscUZBQWlCQSxDQUFDNkIsS0FBSztZQUN2QixJQUFJLENBQUNGLFNBQVMsT0FBT0EsVUFBVSxZQUFZLE9BQU9BLFVBQVUsVUFBVTtnQkFDbEVBLFFBQ0ksT0FBT0EsVUFBVSxZQUFZLE9BQU9BLFVBQVUsV0FDeENpQixPQUFPakIsU0FDUDtZQUNkO1lBQ0FXLE9BQU9aLElBQUksQ0FBQztnQkFDUkYsTUFBTSxPQUFPRyxVQUFVLFdBQVdYLFVBQVVTLE9BQU8sR0FBR1QsVUFBVTZCLE1BQU07Z0JBQ3RFbEIsT0FBT0E7WUFDWDtZQUNBO1FBQ0o7UUFDQSxzRUFBc0U7UUFDdEUsaUVBQWlFO1FBQ2pFLHlEQUF5RDtRQUN6RCxJQUFJMUIsaUZBQWFBLENBQUM0QixLQUFLO1lBQ25CLElBQUlpQixRQUFRLE9BQU9qQixHQUFHaUIsS0FBSyxLQUFLLFdBQzFCWixRQUFRYSxJQUFJLENBQUNsQixHQUFHaUIsS0FBSyxDQUFDLEdBQ3RCNUMsc0ZBQWtCQSxDQUFDMkIsR0FBR2lCLEtBQUssSUFDdkJqQixHQUFHaUIsS0FBSyxDQUFDRSxhQUFhLEdBQ3RCQztZQUNWWCxPQUFPWixJQUFJLENBQUM7Z0JBQ1JGLE1BQU1SLFVBQVVTLE9BQU87Z0JBQ3ZCRSxPQUFPTSxXQUNGaUIsaUJBQWlCLENBQUNsQixTQUFTYyxPQUMzQkosTUFBTSxDQUFDZjtZQUNoQjtZQUNBO1FBQ0o7UUFDQSxJQUFJakIsaUZBQWFBLENBQUNtQixLQUFLO1lBQ25CLElBQUlpQixRQUFRLE9BQU9qQixHQUFHaUIsS0FBSyxLQUFLLFdBQzFCWixRQUFRaUIsSUFBSSxDQUFDdEIsR0FBR2lCLEtBQUssQ0FBQyxHQUN0QjVDLHNGQUFrQkEsQ0FBQzJCLEdBQUdpQixLQUFLLElBQ3ZCakIsR0FBR2lCLEtBQUssQ0FBQ0UsYUFBYSxHQUN0QmQsUUFBUWlCLElBQUksQ0FBQ0MsTUFBTTtZQUM3QmQsT0FBT1osSUFBSSxDQUFDO2dCQUNSRixNQUFNUixVQUFVUyxPQUFPO2dCQUN2QkUsT0FBT00sV0FDRmlCLGlCQUFpQixDQUFDbEIsU0FBU2MsT0FDM0JKLE1BQU0sQ0FBQ2Y7WUFDaEI7WUFDQTtRQUNKO1FBQ0EsSUFBSXZCLG1GQUFlQSxDQUFDeUIsS0FBSztZQUNyQixJQUFJaUIsUUFBUSxPQUFPakIsR0FBR2lCLEtBQUssS0FBSyxXQUMxQlosUUFBUW1CLE1BQU0sQ0FBQ3hCLEdBQUdpQixLQUFLLENBQUMsR0FDeEJ6QyxvRkFBZ0JBLENBQUN3QixHQUFHaUIsS0FBSyxJQUNyQmpCLEdBQUdpQixLQUFLLENBQUNFLGFBQWEsR0FDdEJDO1lBQ1YsSUFBSUgsU0FBU0EsTUFBTVEsS0FBSyxFQUFFO2dCQUN0QjNCLFFBQ0lBLFFBQ0ttQixDQUFBQSxNQUFNUSxLQUFLLElBQUk7WUFDNUI7WUFDQWhCLE9BQU9aLElBQUksQ0FBQztnQkFDUkYsTUFBTVIsVUFBVVMsT0FBTztnQkFDdkJFLE9BQU9NLFdBQ0ZRLGVBQWUsQ0FBQ1QsU0FBU2MsT0FDekJKLE1BQU0sQ0FBQ2Y7WUFDaEI7WUFDQTtRQUNKO1FBQ0EsSUFBSWxCLGdGQUFZQSxDQUFDb0IsS0FBSztZQUNsQixJQUFJMEIsV0FBVzFCLEdBQUcwQixRQUFRLEVBQUVDLFVBQVUzQixHQUFHRixLQUFLO1lBQzlDLElBQUk4QixXQUFXdEIsTUFBTSxDQUFDcUIsUUFBUTtZQUM5QixJQUFJLENBQUM1QixxQkFBcUI2QixXQUFXO2dCQUNqQyxNQUFNLElBQUkzQyx5REFBcUJBLENBQUMwQyxTQUFTLFlBQVluQjtZQUN6RDtZQUNBLElBQUluQixRQUFRWSxjQUFjeUIsVUFBVXZCLFNBQVNDLFlBQVlDLFNBQVNDLFFBQVFDO1lBQzFFLElBQUlzQixTQUFTRCxTQUFTdkMsTUFBTXlDLEdBQUcsQ0FBQyxTQUFVQyxDQUFDO2dCQUFJLE9BQU9BLEVBQUVqQyxLQUFLO1lBQUU7WUFDL0QsSUFBSSxDQUFDa0MsTUFBTUMsT0FBTyxDQUFDSixTQUFTO2dCQUN4QkEsU0FBUztvQkFBQ0E7aUJBQU87WUFDckI7WUFDQXBCLE9BQU9aLElBQUksQ0FBQ3FDLEtBQUssQ0FBQ3pCLFFBQVFvQixPQUFPQyxHQUFHLENBQUMsU0FBVUssQ0FBQztnQkFDNUMsT0FBTztvQkFDSHhDLE1BQU0sT0FBT3dDLE1BQU0sV0FBV2hELFVBQVVTLE9BQU8sR0FBR1QsVUFBVTZCLE1BQU07b0JBQ2xFbEIsT0FBT3FDO2dCQUNYO1lBQ0o7UUFDSjtRQUNBLElBQUl4RCxtRkFBZUEsQ0FBQ3FCLEtBQUs7WUFDckIsSUFBSW9DLE1BQU1wQyxHQUFHcUMsT0FBTyxDQUFDdkMsTUFBTSxJQUFJRSxHQUFHcUMsT0FBTyxDQUFDQyxLQUFLO1lBQy9DLElBQUksQ0FBQ0YsS0FBSztnQkFDTixNQUFNLElBQUlwRCxxREFBaUJBLENBQUNnQixHQUFHRixLQUFLLEVBQUVBLE9BQU95QyxPQUFPQyxJQUFJLENBQUN4QyxHQUFHcUMsT0FBTyxHQUFHN0I7WUFDMUU7WUFDQUMsT0FBT1osSUFBSSxDQUFDcUMsS0FBSyxDQUFDekIsUUFBUVIsY0FBY21DLElBQUl0QyxLQUFLLEVBQUVLLFNBQVNDLFlBQVlDLFNBQVNDO1lBQ2pGO1FBQ0o7UUFDQSxJQUFJN0IsbUZBQWVBLENBQUN1QixLQUFLO1lBQ3JCLElBQUlvQyxNQUFNcEMsR0FBR3FDLE9BQU8sQ0FBQyxJQUFJSSxNQUFNLENBQUMzQyxPQUFPO1lBQ3ZDLElBQUksQ0FBQ3NDLEtBQUs7Z0JBQ04sSUFBSSxDQUFDTSxLQUFLQyxXQUFXLEVBQUU7b0JBQ25CLE1BQU0sSUFBSTVELCtDQUFXQSxDQUFDLG1IQUFxSEQsNkNBQVNBLENBQUM4RCxnQkFBZ0IsRUFBRXBDO2dCQUMzSztnQkFDQSxJQUFJcUMsT0FBT3pDLFdBQ04wQyxjQUFjLENBQUMzQyxTQUFTO29CQUFFUixNQUFNSyxHQUFHK0MsVUFBVTtnQkFBQyxHQUM5Q0MsTUFBTSxDQUFDbEQsUUFBU0UsQ0FBQUEsR0FBR2lELE1BQU0sSUFBSTtnQkFDbENiLE1BQU1wQyxHQUFHcUMsT0FBTyxDQUFDUSxLQUFLLElBQUk3QyxHQUFHcUMsT0FBTyxDQUFDQyxLQUFLO1lBQzlDO1lBQ0EsSUFBSSxDQUFDRixLQUFLO2dCQUNOLE1BQU0sSUFBSXBELHFEQUFpQkEsQ0FBQ2dCLEdBQUdGLEtBQUssRUFBRUEsT0FBT3lDLE9BQU9DLElBQUksQ0FBQ3hDLEdBQUdxQyxPQUFPLEdBQUc3QjtZQUMxRTtZQUNBQyxPQUFPWixJQUFJLENBQUNxQyxLQUFLLENBQUN6QixRQUFRUixjQUFjbUMsSUFBSXRDLEtBQUssRUFBRUssU0FBU0MsWUFBWUMsU0FBU0MsUUFBUVIsUUFBU0UsQ0FBQUEsR0FBR2lELE1BQU0sSUFBSTtZQUMvRztRQUNKO0lBQ0o7SUFDQSxPQUFPN0QsYUFBYXFCO0FBQ3hCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc21lLWFuYWx5dGljYS1kb2NzLy4vbm9kZV9tb2R1bGVzL2ludGwtbWVzc2FnZWZvcm1hdC9saWIvc3JjL2Zvcm1hdHRlcnMuanM/ZDI0ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpc0FyZ3VtZW50RWxlbWVudCwgaXNEYXRlRWxlbWVudCwgaXNEYXRlVGltZVNrZWxldG9uLCBpc0xpdGVyYWxFbGVtZW50LCBpc051bWJlckVsZW1lbnQsIGlzTnVtYmVyU2tlbGV0b24sIGlzUGx1cmFsRWxlbWVudCwgaXNQb3VuZEVsZW1lbnQsIGlzU2VsZWN0RWxlbWVudCwgaXNUYWdFbGVtZW50LCBpc1RpbWVFbGVtZW50LCB9IGZyb20gJ0Bmb3JtYXRqcy9pY3UtbWVzc2FnZWZvcm1hdC1wYXJzZXInO1xuaW1wb3J0IHsgRXJyb3JDb2RlLCBGb3JtYXRFcnJvciwgSW52YWxpZFZhbHVlRXJyb3IsIEludmFsaWRWYWx1ZVR5cGVFcnJvciwgTWlzc2luZ1ZhbHVlRXJyb3IsIH0gZnJvbSAnLi9lcnJvcic7XG5leHBvcnQgdmFyIFBBUlRfVFlQRTtcbihmdW5jdGlvbiAoUEFSVF9UWVBFKSB7XG4gICAgUEFSVF9UWVBFW1BBUlRfVFlQRVtcImxpdGVyYWxcIl0gPSAwXSA9IFwibGl0ZXJhbFwiO1xuICAgIFBBUlRfVFlQRVtQQVJUX1RZUEVbXCJvYmplY3RcIl0gPSAxXSA9IFwib2JqZWN0XCI7XG59KShQQVJUX1RZUEUgfHwgKFBBUlRfVFlQRSA9IHt9KSk7XG5mdW5jdGlvbiBtZXJnZUxpdGVyYWwocGFydHMpIHtcbiAgICBpZiAocGFydHMubGVuZ3RoIDwgMikge1xuICAgICAgICByZXR1cm4gcGFydHM7XG4gICAgfVxuICAgIHJldHVybiBwYXJ0cy5yZWR1Y2UoZnVuY3Rpb24gKGFsbCwgcGFydCkge1xuICAgICAgICB2YXIgbGFzdFBhcnQgPSBhbGxbYWxsLmxlbmd0aCAtIDFdO1xuICAgICAgICBpZiAoIWxhc3RQYXJ0IHx8XG4gICAgICAgICAgICBsYXN0UGFydC50eXBlICE9PSBQQVJUX1RZUEUubGl0ZXJhbCB8fFxuICAgICAgICAgICAgcGFydC50eXBlICE9PSBQQVJUX1RZUEUubGl0ZXJhbCkge1xuICAgICAgICAgICAgYWxsLnB1c2gocGFydCk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBsYXN0UGFydC52YWx1ZSArPSBwYXJ0LnZhbHVlO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBhbGw7XG4gICAgfSwgW10pO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGlzRm9ybWF0WE1MRWxlbWVudEZuKGVsKSB7XG4gICAgcmV0dXJuIHR5cGVvZiBlbCA9PT0gJ2Z1bmN0aW9uJztcbn1cbi8vIFRPRE8oc2tlbGV0b24pOiBhZGQgc2tlbGV0b24gc3VwcG9ydFxuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdFRvUGFydHMoZWxzLCBsb2NhbGVzLCBmb3JtYXR0ZXJzLCBmb3JtYXRzLCB2YWx1ZXMsIGN1cnJlbnRQbHVyYWxWYWx1ZSwgXG4vLyBGb3IgZGVidWdnaW5nXG5vcmlnaW5hbE1lc3NhZ2UpIHtcbiAgICAvLyBIb3QgcGF0aCBmb3Igc3RyYWlnaHQgc2ltcGxlIG1zZyB0cmFuc2xhdGlvbnNcbiAgICBpZiAoZWxzLmxlbmd0aCA9PT0gMSAmJiBpc0xpdGVyYWxFbGVtZW50KGVsc1swXSkpIHtcbiAgICAgICAgcmV0dXJuIFtcbiAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICB0eXBlOiBQQVJUX1RZUEUubGl0ZXJhbCxcbiAgICAgICAgICAgICAgICB2YWx1ZTogZWxzWzBdLnZhbHVlLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgXTtcbiAgICB9XG4gICAgdmFyIHJlc3VsdCA9IFtdO1xuICAgIGZvciAodmFyIF9pID0gMCwgZWxzXzEgPSBlbHM7IF9pIDwgZWxzXzEubGVuZ3RoOyBfaSsrKSB7XG4gICAgICAgIHZhciBlbCA9IGVsc18xW19pXTtcbiAgICAgICAgLy8gRXhpdCBlYXJseSBmb3Igc3RyaW5nIHBhcnRzLlxuICAgICAgICBpZiAoaXNMaXRlcmFsRWxlbWVudChlbCkpIHtcbiAgICAgICAgICAgIHJlc3VsdC5wdXNoKHtcbiAgICAgICAgICAgICAgICB0eXBlOiBQQVJUX1RZUEUubGl0ZXJhbCxcbiAgICAgICAgICAgICAgICB2YWx1ZTogZWwudmFsdWUsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICB9XG4gICAgICAgIC8vIFRPRE86IHNob3VsZCB0aGlzIHBhcnQgYmUgbGl0ZXJhbCB0eXBlP1xuICAgICAgICAvLyBSZXBsYWNlIGAjYCBpbiBwbHVyYWwgcnVsZXMgd2l0aCB0aGUgYWN0dWFsIG51bWVyaWMgdmFsdWUuXG4gICAgICAgIGlmIChpc1BvdW5kRWxlbWVudChlbCkpIHtcbiAgICAgICAgICAgIGlmICh0eXBlb2YgY3VycmVudFBsdXJhbFZhbHVlID09PSAnbnVtYmVyJykge1xuICAgICAgICAgICAgICAgIHJlc3VsdC5wdXNoKHtcbiAgICAgICAgICAgICAgICAgICAgdHlwZTogUEFSVF9UWVBFLmxpdGVyYWwsXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlOiBmb3JtYXR0ZXJzLmdldE51bWJlckZvcm1hdChsb2NhbGVzKS5mb3JtYXQoY3VycmVudFBsdXJhbFZhbHVlKSxcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICB9XG4gICAgICAgIHZhciB2YXJOYW1lID0gZWwudmFsdWU7XG4gICAgICAgIC8vIEVuZm9yY2UgdGhhdCBhbGwgcmVxdWlyZWQgdmFsdWVzIGFyZSBwcm92aWRlZCBieSB0aGUgY2FsbGVyLlxuICAgICAgICBpZiAoISh2YWx1ZXMgJiYgdmFyTmFtZSBpbiB2YWx1ZXMpKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgTWlzc2luZ1ZhbHVlRXJyb3IodmFyTmFtZSwgb3JpZ2luYWxNZXNzYWdlKTtcbiAgICAgICAgfVxuICAgICAgICB2YXIgdmFsdWUgPSB2YWx1ZXNbdmFyTmFtZV07XG4gICAgICAgIGlmIChpc0FyZ3VtZW50RWxlbWVudChlbCkpIHtcbiAgICAgICAgICAgIGlmICghdmFsdWUgfHwgdHlwZW9mIHZhbHVlID09PSAnc3RyaW5nJyB8fCB0eXBlb2YgdmFsdWUgPT09ICdudW1iZXInKSB7XG4gICAgICAgICAgICAgICAgdmFsdWUgPVxuICAgICAgICAgICAgICAgICAgICB0eXBlb2YgdmFsdWUgPT09ICdzdHJpbmcnIHx8IHR5cGVvZiB2YWx1ZSA9PT0gJ251bWJlcidcbiAgICAgICAgICAgICAgICAgICAgICAgID8gU3RyaW5nKHZhbHVlKVxuICAgICAgICAgICAgICAgICAgICAgICAgOiAnJztcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJlc3VsdC5wdXNoKHtcbiAgICAgICAgICAgICAgICB0eXBlOiB0eXBlb2YgdmFsdWUgPT09ICdzdHJpbmcnID8gUEFSVF9UWVBFLmxpdGVyYWwgOiBQQVJUX1RZUEUub2JqZWN0LFxuICAgICAgICAgICAgICAgIHZhbHVlOiB2YWx1ZSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgIH1cbiAgICAgICAgLy8gUmVjdXJzaXZlbHkgZm9ybWF0IHBsdXJhbCBhbmQgc2VsZWN0IHBhcnRzJyBvcHRpb24g4oCUIHdoaWNoIGNhbiBiZSBhXG4gICAgICAgIC8vIG5lc3RlZCBwYXR0ZXJuIHN0cnVjdHVyZS4gVGhlIGNob29zaW5nIG9mIHRoZSBvcHRpb24gdG8gdXNlIGlzXG4gICAgICAgIC8vIGFic3RyYWN0ZWQtYnkgYW5kIGRlbGVnYXRlZC10byB0aGUgcGFydCBoZWxwZXIgb2JqZWN0LlxuICAgICAgICBpZiAoaXNEYXRlRWxlbWVudChlbCkpIHtcbiAgICAgICAgICAgIHZhciBzdHlsZSA9IHR5cGVvZiBlbC5zdHlsZSA9PT0gJ3N0cmluZydcbiAgICAgICAgICAgICAgICA/IGZvcm1hdHMuZGF0ZVtlbC5zdHlsZV1cbiAgICAgICAgICAgICAgICA6IGlzRGF0ZVRpbWVTa2VsZXRvbihlbC5zdHlsZSlcbiAgICAgICAgICAgICAgICAgICAgPyBlbC5zdHlsZS5wYXJzZWRPcHRpb25zXG4gICAgICAgICAgICAgICAgICAgIDogdW5kZWZpbmVkO1xuICAgICAgICAgICAgcmVzdWx0LnB1c2goe1xuICAgICAgICAgICAgICAgIHR5cGU6IFBBUlRfVFlQRS5saXRlcmFsLFxuICAgICAgICAgICAgICAgIHZhbHVlOiBmb3JtYXR0ZXJzXG4gICAgICAgICAgICAgICAgICAgIC5nZXREYXRlVGltZUZvcm1hdChsb2NhbGVzLCBzdHlsZSlcbiAgICAgICAgICAgICAgICAgICAgLmZvcm1hdCh2YWx1ZSksXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICB9XG4gICAgICAgIGlmIChpc1RpbWVFbGVtZW50KGVsKSkge1xuICAgICAgICAgICAgdmFyIHN0eWxlID0gdHlwZW9mIGVsLnN0eWxlID09PSAnc3RyaW5nJ1xuICAgICAgICAgICAgICAgID8gZm9ybWF0cy50aW1lW2VsLnN0eWxlXVxuICAgICAgICAgICAgICAgIDogaXNEYXRlVGltZVNrZWxldG9uKGVsLnN0eWxlKVxuICAgICAgICAgICAgICAgICAgICA/IGVsLnN0eWxlLnBhcnNlZE9wdGlvbnNcbiAgICAgICAgICAgICAgICAgICAgOiBmb3JtYXRzLnRpbWUubWVkaXVtO1xuICAgICAgICAgICAgcmVzdWx0LnB1c2goe1xuICAgICAgICAgICAgICAgIHR5cGU6IFBBUlRfVFlQRS5saXRlcmFsLFxuICAgICAgICAgICAgICAgIHZhbHVlOiBmb3JtYXR0ZXJzXG4gICAgICAgICAgICAgICAgICAgIC5nZXREYXRlVGltZUZvcm1hdChsb2NhbGVzLCBzdHlsZSlcbiAgICAgICAgICAgICAgICAgICAgLmZvcm1hdCh2YWx1ZSksXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICB9XG4gICAgICAgIGlmIChpc051bWJlckVsZW1lbnQoZWwpKSB7XG4gICAgICAgICAgICB2YXIgc3R5bGUgPSB0eXBlb2YgZWwuc3R5bGUgPT09ICdzdHJpbmcnXG4gICAgICAgICAgICAgICAgPyBmb3JtYXRzLm51bWJlcltlbC5zdHlsZV1cbiAgICAgICAgICAgICAgICA6IGlzTnVtYmVyU2tlbGV0b24oZWwuc3R5bGUpXG4gICAgICAgICAgICAgICAgICAgID8gZWwuc3R5bGUucGFyc2VkT3B0aW9uc1xuICAgICAgICAgICAgICAgICAgICA6IHVuZGVmaW5lZDtcbiAgICAgICAgICAgIGlmIChzdHlsZSAmJiBzdHlsZS5zY2FsZSkge1xuICAgICAgICAgICAgICAgIHZhbHVlID1cbiAgICAgICAgICAgICAgICAgICAgdmFsdWUgKlxuICAgICAgICAgICAgICAgICAgICAgICAgKHN0eWxlLnNjYWxlIHx8IDEpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmVzdWx0LnB1c2goe1xuICAgICAgICAgICAgICAgIHR5cGU6IFBBUlRfVFlQRS5saXRlcmFsLFxuICAgICAgICAgICAgICAgIHZhbHVlOiBmb3JtYXR0ZXJzXG4gICAgICAgICAgICAgICAgICAgIC5nZXROdW1iZXJGb3JtYXQobG9jYWxlcywgc3R5bGUpXG4gICAgICAgICAgICAgICAgICAgIC5mb3JtYXQodmFsdWUpLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoaXNUYWdFbGVtZW50KGVsKSkge1xuICAgICAgICAgICAgdmFyIGNoaWxkcmVuID0gZWwuY2hpbGRyZW4sIHZhbHVlXzEgPSBlbC52YWx1ZTtcbiAgICAgICAgICAgIHZhciBmb3JtYXRGbiA9IHZhbHVlc1t2YWx1ZV8xXTtcbiAgICAgICAgICAgIGlmICghaXNGb3JtYXRYTUxFbGVtZW50Rm4oZm9ybWF0Rm4pKSB7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEludmFsaWRWYWx1ZVR5cGVFcnJvcih2YWx1ZV8xLCAnZnVuY3Rpb24nLCBvcmlnaW5hbE1lc3NhZ2UpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdmFyIHBhcnRzID0gZm9ybWF0VG9QYXJ0cyhjaGlsZHJlbiwgbG9jYWxlcywgZm9ybWF0dGVycywgZm9ybWF0cywgdmFsdWVzLCBjdXJyZW50UGx1cmFsVmFsdWUpO1xuICAgICAgICAgICAgdmFyIGNodW5rcyA9IGZvcm1hdEZuKHBhcnRzLm1hcChmdW5jdGlvbiAocCkgeyByZXR1cm4gcC52YWx1ZTsgfSkpO1xuICAgICAgICAgICAgaWYgKCFBcnJheS5pc0FycmF5KGNodW5rcykpIHtcbiAgICAgICAgICAgICAgICBjaHVua3MgPSBbY2h1bmtzXTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJlc3VsdC5wdXNoLmFwcGx5KHJlc3VsdCwgY2h1bmtzLm1hcChmdW5jdGlvbiAoYykge1xuICAgICAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgICAgIHR5cGU6IHR5cGVvZiBjID09PSAnc3RyaW5nJyA/IFBBUlRfVFlQRS5saXRlcmFsIDogUEFSVF9UWVBFLm9iamVjdCxcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU6IGMsXG4gICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIH0pKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoaXNTZWxlY3RFbGVtZW50KGVsKSkge1xuICAgICAgICAgICAgdmFyIG9wdCA9IGVsLm9wdGlvbnNbdmFsdWVdIHx8IGVsLm9wdGlvbnMub3RoZXI7XG4gICAgICAgICAgICBpZiAoIW9wdCkge1xuICAgICAgICAgICAgICAgIHRocm93IG5ldyBJbnZhbGlkVmFsdWVFcnJvcihlbC52YWx1ZSwgdmFsdWUsIE9iamVjdC5rZXlzKGVsLm9wdGlvbnMpLCBvcmlnaW5hbE1lc3NhZ2UpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmVzdWx0LnB1c2guYXBwbHkocmVzdWx0LCBmb3JtYXRUb1BhcnRzKG9wdC52YWx1ZSwgbG9jYWxlcywgZm9ybWF0dGVycywgZm9ybWF0cywgdmFsdWVzKSk7XG4gICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoaXNQbHVyYWxFbGVtZW50KGVsKSkge1xuICAgICAgICAgICAgdmFyIG9wdCA9IGVsLm9wdGlvbnNbXCI9XCIuY29uY2F0KHZhbHVlKV07XG4gICAgICAgICAgICBpZiAoIW9wdCkge1xuICAgICAgICAgICAgICAgIGlmICghSW50bC5QbHVyYWxSdWxlcykge1xuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRm9ybWF0RXJyb3IoXCJJbnRsLlBsdXJhbFJ1bGVzIGlzIG5vdCBhdmFpbGFibGUgaW4gdGhpcyBlbnZpcm9ubWVudC5cXG5UcnkgcG9seWZpbGxpbmcgaXQgdXNpbmcgXFxcIkBmb3JtYXRqcy9pbnRsLXBsdXJhbHJ1bGVzXFxcIlxcblwiLCBFcnJvckNvZGUuTUlTU0lOR19JTlRMX0FQSSwgb3JpZ2luYWxNZXNzYWdlKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgdmFyIHJ1bGUgPSBmb3JtYXR0ZXJzXG4gICAgICAgICAgICAgICAgICAgIC5nZXRQbHVyYWxSdWxlcyhsb2NhbGVzLCB7IHR5cGU6IGVsLnBsdXJhbFR5cGUgfSlcbiAgICAgICAgICAgICAgICAgICAgLnNlbGVjdCh2YWx1ZSAtIChlbC5vZmZzZXQgfHwgMCkpO1xuICAgICAgICAgICAgICAgIG9wdCA9IGVsLm9wdGlvbnNbcnVsZV0gfHwgZWwub3B0aW9ucy5vdGhlcjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICghb3B0KSB7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEludmFsaWRWYWx1ZUVycm9yKGVsLnZhbHVlLCB2YWx1ZSwgT2JqZWN0LmtleXMoZWwub3B0aW9ucyksIG9yaWdpbmFsTWVzc2FnZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXN1bHQucHVzaC5hcHBseShyZXN1bHQsIGZvcm1hdFRvUGFydHMob3B0LnZhbHVlLCBsb2NhbGVzLCBmb3JtYXR0ZXJzLCBmb3JtYXRzLCB2YWx1ZXMsIHZhbHVlIC0gKGVsLm9mZnNldCB8fCAwKSkpO1xuICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIG1lcmdlTGl0ZXJhbChyZXN1bHQpO1xufVxuIl0sIm5hbWVzIjpbImlzQXJndW1lbnRFbGVtZW50IiwiaXNEYXRlRWxlbWVudCIsImlzRGF0ZVRpbWVTa2VsZXRvbiIsImlzTGl0ZXJhbEVsZW1lbnQiLCJpc051bWJlckVsZW1lbnQiLCJpc051bWJlclNrZWxldG9uIiwiaXNQbHVyYWxFbGVtZW50IiwiaXNQb3VuZEVsZW1lbnQiLCJpc1NlbGVjdEVsZW1lbnQiLCJpc1RhZ0VsZW1lbnQiLCJpc1RpbWVFbGVtZW50IiwiRXJyb3JDb2RlIiwiRm9ybWF0RXJyb3IiLCJJbnZhbGlkVmFsdWVFcnJvciIsIkludmFsaWRWYWx1ZVR5cGVFcnJvciIsIk1pc3NpbmdWYWx1ZUVycm9yIiwiUEFSVF9UWVBFIiwibWVyZ2VMaXRlcmFsIiwicGFydHMiLCJsZW5ndGgiLCJyZWR1Y2UiLCJhbGwiLCJwYXJ0IiwibGFzdFBhcnQiLCJ0eXBlIiwibGl0ZXJhbCIsInB1c2giLCJ2YWx1ZSIsImlzRm9ybWF0WE1MRWxlbWVudEZuIiwiZWwiLCJmb3JtYXRUb1BhcnRzIiwiZWxzIiwibG9jYWxlcyIsImZvcm1hdHRlcnMiLCJmb3JtYXRzIiwidmFsdWVzIiwiY3VycmVudFBsdXJhbFZhbHVlIiwib3JpZ2luYWxNZXNzYWdlIiwicmVzdWx0IiwiX2kiLCJlbHNfMSIsImdldE51bWJlckZvcm1hdCIsImZvcm1hdCIsInZhck5hbWUiLCJTdHJpbmciLCJvYmplY3QiLCJzdHlsZSIsImRhdGUiLCJwYXJzZWRPcHRpb25zIiwidW5kZWZpbmVkIiwiZ2V0RGF0ZVRpbWVGb3JtYXQiLCJ0aW1lIiwibWVkaXVtIiwibnVtYmVyIiwic2NhbGUiLCJjaGlsZHJlbiIsInZhbHVlXzEiLCJmb3JtYXRGbiIsImNodW5rcyIsIm1hcCIsInAiLCJBcnJheSIsImlzQXJyYXkiLCJhcHBseSIsImMiLCJvcHQiLCJvcHRpb25zIiwib3RoZXIiLCJPYmplY3QiLCJrZXlzIiwiY29uY2F0IiwiSW50bCIsIlBsdXJhbFJ1bGVzIiwiTUlTU0lOR19JTlRMX0FQSSIsInJ1bGUiLCJnZXRQbHVyYWxSdWxlcyIsInBsdXJhbFR5cGUiLCJzZWxlY3QiLCJvZmZzZXQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/intl-messageformat/lib/src/formatters.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/intl-messageformat/lib/index.js":
/*!******************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorCode: () => (/* reexport safe */ _src_error__WEBPACK_IMPORTED_MODULE_0__.ErrorCode),\n/* harmony export */   FormatError: () => (/* reexport safe */ _src_error__WEBPACK_IMPORTED_MODULE_0__.FormatError),\n/* harmony export */   IntlMessageFormat: () => (/* reexport safe */ _src_core__WEBPACK_IMPORTED_MODULE_2__.IntlMessageFormat),\n/* harmony export */   InvalidValueError: () => (/* reexport safe */ _src_error__WEBPACK_IMPORTED_MODULE_0__.InvalidValueError),\n/* harmony export */   InvalidValueTypeError: () => (/* reexport safe */ _src_error__WEBPACK_IMPORTED_MODULE_0__.InvalidValueTypeError),\n/* harmony export */   MissingValueError: () => (/* reexport safe */ _src_error__WEBPACK_IMPORTED_MODULE_0__.MissingValueError),\n/* harmony export */   PART_TYPE: () => (/* reexport safe */ _src_formatters__WEBPACK_IMPORTED_MODULE_1__.PART_TYPE),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   formatToParts: () => (/* reexport safe */ _src_formatters__WEBPACK_IMPORTED_MODULE_1__.formatToParts),\n/* harmony export */   isFormatXMLElementFn: () => (/* reexport safe */ _src_formatters__WEBPACK_IMPORTED_MODULE_1__.isFormatXMLElementFn)\n/* harmony export */ });\n/* harmony import */ var _src_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./src/core */ \"(rsc)/./node_modules/intl-messageformat/lib/src/core.js\");\n/* harmony import */ var _src_error__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./src/error */ \"(rsc)/./node_modules/intl-messageformat/lib/src/error.js\");\n/* harmony import */ var _src_formatters__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./src/formatters */ \"(rsc)/./node_modules/intl-messageformat/lib/src/formatters.js\");\n/*\nCopyright (c) 2014, Yahoo! Inc. All rights reserved.\nCopyrights licensed under the New BSD License.\nSee the accompanying LICENSE file for terms.\n*/ \n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_src_core__WEBPACK_IMPORTED_MODULE_2__.IntlMessageFormat);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaW50bC1tZXNzYWdlZm9ybWF0L2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7Ozs7QUFJQSxHQUMrQztBQUNwQjtBQUNDO0FBQ0s7QUFDSjtBQUM3QixpRUFBZUEsd0RBQWlCQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc21lLWFuYWx5dGljYS1kb2NzLy4vbm9kZV9tb2R1bGVzL2ludGwtbWVzc2FnZWZvcm1hdC9saWIvaW5kZXguanM/ZTEyYiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKlxuQ29weXJpZ2h0IChjKSAyMDE0LCBZYWhvbyEgSW5jLiBBbGwgcmlnaHRzIHJlc2VydmVkLlxuQ29weXJpZ2h0cyBsaWNlbnNlZCB1bmRlciB0aGUgTmV3IEJTRCBMaWNlbnNlLlxuU2VlIHRoZSBhY2NvbXBhbnlpbmcgTElDRU5TRSBmaWxlIGZvciB0ZXJtcy5cbiovXG5pbXBvcnQgeyBJbnRsTWVzc2FnZUZvcm1hdCB9IGZyb20gJy4vc3JjL2NvcmUnO1xuZXhwb3J0ICogZnJvbSAnLi9zcmMvY29yZSc7XG5leHBvcnQgKiBmcm9tICcuL3NyYy9lcnJvcic7XG5leHBvcnQgKiBmcm9tICcuL3NyYy9mb3JtYXR0ZXJzJztcbmV4cG9ydCB7IEludGxNZXNzYWdlRm9ybWF0IH07XG5leHBvcnQgZGVmYXVsdCBJbnRsTWVzc2FnZUZvcm1hdDtcbiJdLCJuYW1lcyI6WyJJbnRsTWVzc2FnZUZvcm1hdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/intl-messageformat/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/intl-messageformat/lib/src/core.js":
/*!*********************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/src/core.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntlMessageFormat: () => (/* binding */ IntlMessageFormat)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @formatjs/fast-memoize */ \"(rsc)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n/* harmony import */ var _formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @formatjs/icu-messageformat-parser */ \"(rsc)/./node_modules/@formatjs/icu-messageformat-parser/lib/index.js\");\n/* harmony import */ var _formatters__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./formatters */ \"(rsc)/./node_modules/intl-messageformat/lib/src/formatters.js\");\n/*\nCopyright (c) 2014, Yahoo! Inc. All rights reserved.\nCopyrights licensed under the New BSD License.\nSee the accompanying LICENSE file for terms.\n*/ \n\n\n\n// -- MessageFormat --------------------------------------------------------\nfunction mergeConfig(c1, c2) {\n    if (!c2) {\n        return c1;\n    }\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, c1 || {}), c2 || {}), Object.keys(c1).reduce(function(all, k) {\n        all[k] = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, c1[k]), c2[k] || {});\n        return all;\n    }, {}));\n}\nfunction mergeConfigs(defaultConfig, configs) {\n    if (!configs) {\n        return defaultConfig;\n    }\n    return Object.keys(defaultConfig).reduce(function(all, k) {\n        all[k] = mergeConfig(defaultConfig[k], configs[k]);\n        return all;\n    }, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, defaultConfig));\n}\nfunction createFastMemoizeCache(store) {\n    return {\n        create: function() {\n            return {\n                get: function(key) {\n                    return store[key];\n                },\n                set: function(key, value) {\n                    store[key] = value;\n                }\n            };\n        }\n    };\n}\nfunction createDefaultFormatters(cache) {\n    if (cache === void 0) {\n        cache = {\n            number: {},\n            dateTime: {},\n            pluralRules: {}\n        };\n    }\n    return {\n        getNumberFormat: (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.memoize)(function() {\n            var _a;\n            var args = [];\n            for(var _i = 0; _i < arguments.length; _i++){\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.NumberFormat).bind.apply(_a, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__spreadArray)([\n                void 0\n            ], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.number),\n            strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.strategies.variadic\n        }),\n        getDateTimeFormat: (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.memoize)(function() {\n            var _a;\n            var args = [];\n            for(var _i = 0; _i < arguments.length; _i++){\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.DateTimeFormat).bind.apply(_a, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__spreadArray)([\n                void 0\n            ], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.dateTime),\n            strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.strategies.variadic\n        }),\n        getPluralRules: (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.memoize)(function() {\n            var _a;\n            var args = [];\n            for(var _i = 0; _i < arguments.length; _i++){\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.PluralRules).bind.apply(_a, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__spreadArray)([\n                void 0\n            ], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.pluralRules),\n            strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.strategies.variadic\n        })\n    };\n}\nvar IntlMessageFormat = /** @class */ function() {\n    function IntlMessageFormat(message, locales, overrideFormats, opts) {\n        if (locales === void 0) {\n            locales = IntlMessageFormat.defaultLocale;\n        }\n        var _this = this;\n        this.formatterCache = {\n            number: {},\n            dateTime: {},\n            pluralRules: {}\n        };\n        this.format = function(values) {\n            var parts = _this.formatToParts(values);\n            // Hot path for straight simple msg translations\n            if (parts.length === 1) {\n                return parts[0].value;\n            }\n            var result = parts.reduce(function(all, part) {\n                if (!all.length || part.type !== _formatters__WEBPACK_IMPORTED_MODULE_3__.PART_TYPE.literal || typeof all[all.length - 1] !== \"string\") {\n                    all.push(part.value);\n                } else {\n                    all[all.length - 1] += part.value;\n                }\n                return all;\n            }, []);\n            if (result.length <= 1) {\n                return result[0] || \"\";\n            }\n            return result;\n        };\n        this.formatToParts = function(values) {\n            return (0,_formatters__WEBPACK_IMPORTED_MODULE_3__.formatToParts)(_this.ast, _this.locales, _this.formatters, _this.formats, values, undefined, _this.message);\n        };\n        this.resolvedOptions = function() {\n            var _a;\n            return {\n                locale: ((_a = _this.resolvedLocale) === null || _a === void 0 ? void 0 : _a.toString()) || Intl.NumberFormat.supportedLocalesOf(_this.locales)[0]\n            };\n        };\n        this.getAst = function() {\n            return _this.ast;\n        };\n        // Defined first because it's used to build the format pattern.\n        this.locales = locales;\n        this.resolvedLocale = IntlMessageFormat.resolveLocale(locales);\n        if (typeof message === \"string\") {\n            this.message = message;\n            if (!IntlMessageFormat.__parse) {\n                throw new TypeError(\"IntlMessageFormat.__parse must be set to process `message` of type `string`\");\n            }\n            var _a = opts || {}, formatters = _a.formatters, parseOpts = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__rest)(_a, [\n                \"formatters\"\n            ]);\n            // Parse string messages into an AST.\n            this.ast = IntlMessageFormat.__parse(message, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, parseOpts), {\n                locale: this.resolvedLocale\n            }));\n        } else {\n            this.ast = message;\n        }\n        if (!Array.isArray(this.ast)) {\n            throw new TypeError(\"A message must be provided as a String or AST.\");\n        }\n        // Creates a new object with the specified `formats` merged with the default\n        // formats.\n        this.formats = mergeConfigs(IntlMessageFormat.formats, overrideFormats);\n        this.formatters = opts && opts.formatters || createDefaultFormatters(this.formatterCache);\n    }\n    Object.defineProperty(IntlMessageFormat, \"defaultLocale\", {\n        get: function() {\n            if (!IntlMessageFormat.memoizedDefaultLocale) {\n                IntlMessageFormat.memoizedDefaultLocale = new Intl.NumberFormat().resolvedOptions().locale;\n            }\n            return IntlMessageFormat.memoizedDefaultLocale;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    IntlMessageFormat.memoizedDefaultLocale = null;\n    IntlMessageFormat.resolveLocale = function(locales) {\n        if (typeof Intl.Locale === \"undefined\") {\n            return;\n        }\n        var supportedLocales = Intl.NumberFormat.supportedLocalesOf(locales);\n        if (supportedLocales.length > 0) {\n            return new Intl.Locale(supportedLocales[0]);\n        }\n        return new Intl.Locale(typeof locales === \"string\" ? locales : locales[0]);\n    };\n    IntlMessageFormat.__parse = _formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_1__.parse;\n    // Default format options used as the prototype of the `formats` provided to the\n    // constructor. These are used when constructing the internal Intl.NumberFormat\n    // and Intl.DateTimeFormat instances.\n    IntlMessageFormat.formats = {\n        number: {\n            integer: {\n                maximumFractionDigits: 0\n            },\n            currency: {\n                style: \"currency\"\n            },\n            percent: {\n                style: \"percent\"\n            }\n        },\n        date: {\n            short: {\n                month: \"numeric\",\n                day: \"numeric\",\n                year: \"2-digit\"\n            },\n            medium: {\n                month: \"short\",\n                day: \"numeric\",\n                year: \"numeric\"\n            },\n            long: {\n                month: \"long\",\n                day: \"numeric\",\n                year: \"numeric\"\n            },\n            full: {\n                weekday: \"long\",\n                month: \"long\",\n                day: \"numeric\",\n                year: \"numeric\"\n            }\n        },\n        time: {\n            short: {\n                hour: \"numeric\",\n                minute: \"numeric\"\n            },\n            medium: {\n                hour: \"numeric\",\n                minute: \"numeric\",\n                second: \"numeric\"\n            },\n            long: {\n                hour: \"numeric\",\n                minute: \"numeric\",\n                second: \"numeric\",\n                timeZoneName: \"short\"\n            },\n            full: {\n                hour: \"numeric\",\n                minute: \"numeric\",\n                second: \"numeric\",\n                timeZoneName: \"short\"\n            }\n        }\n    };\n    return IntlMessageFormat;\n}();\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/intl-messageformat/lib/src/core.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/intl-messageformat/lib/src/error.js":
/*!**********************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/src/error.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorCode: () => (/* binding */ ErrorCode),\n/* harmony export */   FormatError: () => (/* binding */ FormatError),\n/* harmony export */   InvalidValueError: () => (/* binding */ InvalidValueError),\n/* harmony export */   InvalidValueTypeError: () => (/* binding */ InvalidValueTypeError),\n/* harmony export */   MissingValueError: () => (/* binding */ MissingValueError)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/tslib/tslib.es6.mjs\");\n\nvar ErrorCode;\n(function(ErrorCode) {\n    // When we have a placeholder but no value to format\n    ErrorCode[\"MISSING_VALUE\"] = \"MISSING_VALUE\";\n    // When value supplied is invalid\n    ErrorCode[\"INVALID_VALUE\"] = \"INVALID_VALUE\";\n    // When we need specific Intl API but it's not available\n    ErrorCode[\"MISSING_INTL_API\"] = \"MISSING_INTL_API\";\n})(ErrorCode || (ErrorCode = {}));\nvar FormatError = /** @class */ function(_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(FormatError, _super);\n    function FormatError(msg, code, originalMessage) {\n        var _this = _super.call(this, msg) || this;\n        _this.code = code;\n        _this.originalMessage = originalMessage;\n        return _this;\n    }\n    FormatError.prototype.toString = function() {\n        return \"[formatjs Error: \".concat(this.code, \"] \").concat(this.message);\n    };\n    return FormatError;\n}(Error);\n\nvar InvalidValueError = /** @class */ function(_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(InvalidValueError, _super);\n    function InvalidValueError(variableId, value, options, originalMessage) {\n        return _super.call(this, 'Invalid values for \"'.concat(variableId, '\": \"').concat(value, '\". Options are \"').concat(Object.keys(options).join('\", \"'), '\"'), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueError;\n}(FormatError);\n\nvar InvalidValueTypeError = /** @class */ function(_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(InvalidValueTypeError, _super);\n    function InvalidValueTypeError(value, type, originalMessage) {\n        return _super.call(this, 'Value for \"'.concat(value, '\" must be of type ').concat(type), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueTypeError;\n}(FormatError);\n\nvar MissingValueError = /** @class */ function(_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(MissingValueError, _super);\n    function MissingValueError(variableId, originalMessage) {\n        return _super.call(this, 'The intl string context variable \"'.concat(variableId, '\" was not provided to the string \"').concat(originalMessage, '\"'), ErrorCode.MISSING_VALUE, originalMessage) || this;\n    }\n    return MissingValueError;\n}(FormatError);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaW50bC1tZXNzYWdlZm9ybWF0L2xpYi9zcmMvZXJyb3IuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQWtDO0FBQzNCLElBQUlDLFVBQVU7QUFDcEIsVUFBVUEsU0FBUztJQUNoQixvREFBb0Q7SUFDcERBLFNBQVMsQ0FBQyxnQkFBZ0IsR0FBRztJQUM3QixpQ0FBaUM7SUFDakNBLFNBQVMsQ0FBQyxnQkFBZ0IsR0FBRztJQUM3Qix3REFBd0Q7SUFDeERBLFNBQVMsQ0FBQyxtQkFBbUIsR0FBRztBQUNwQyxHQUFHQSxhQUFjQSxDQUFBQSxZQUFZLENBQUM7QUFDOUIsSUFBSUMsY0FBYyxXQUFXLEdBQUksU0FBVUMsTUFBTTtJQUM3Q0gsZ0RBQVNBLENBQUNFLGFBQWFDO0lBQ3ZCLFNBQVNELFlBQVlFLEdBQUcsRUFBRUMsSUFBSSxFQUFFQyxlQUFlO1FBQzNDLElBQUlDLFFBQVFKLE9BQU9LLElBQUksQ0FBQyxJQUFJLEVBQUVKLFFBQVEsSUFBSTtRQUMxQ0csTUFBTUYsSUFBSSxHQUFHQTtRQUNiRSxNQUFNRCxlQUFlLEdBQUdBO1FBQ3hCLE9BQU9DO0lBQ1g7SUFDQUwsWUFBWU8sU0FBUyxDQUFDQyxRQUFRLEdBQUc7UUFDN0IsT0FBTyxvQkFBb0JDLE1BQU0sQ0FBQyxJQUFJLENBQUNOLElBQUksRUFBRSxNQUFNTSxNQUFNLENBQUMsSUFBSSxDQUFDQyxPQUFPO0lBQzFFO0lBQ0EsT0FBT1Y7QUFDWCxFQUFFVztBQUNxQjtBQUN2QixJQUFJQyxvQkFBb0IsV0FBVyxHQUFJLFNBQVVYLE1BQU07SUFDbkRILGdEQUFTQSxDQUFDYyxtQkFBbUJYO0lBQzdCLFNBQVNXLGtCQUFrQkMsVUFBVSxFQUFFQyxLQUFLLEVBQUVDLE9BQU8sRUFBRVgsZUFBZTtRQUNsRSxPQUFPSCxPQUFPSyxJQUFJLENBQUMsSUFBSSxFQUFFLHVCQUF3QkcsTUFBTSxDQUFDSSxZQUFZLFFBQVVKLE1BQU0sQ0FBQ0ssT0FBTyxvQkFBc0JMLE1BQU0sQ0FBQ08sT0FBT0MsSUFBSSxDQUFDRixTQUFTRyxJQUFJLENBQUMsU0FBUyxNQUFPbkIsVUFBVW9CLGFBQWEsRUFBRWYsb0JBQW9CLElBQUk7SUFDeE47SUFDQSxPQUFPUTtBQUNYLEVBQUVaO0FBQzJCO0FBQzdCLElBQUlvQix3QkFBd0IsV0FBVyxHQUFJLFNBQVVuQixNQUFNO0lBQ3ZESCxnREFBU0EsQ0FBQ3NCLHVCQUF1Qm5CO0lBQ2pDLFNBQVNtQixzQkFBc0JOLEtBQUssRUFBRU8sSUFBSSxFQUFFakIsZUFBZTtRQUN2RCxPQUFPSCxPQUFPSyxJQUFJLENBQUMsSUFBSSxFQUFFLGNBQWVHLE1BQU0sQ0FBQ0ssT0FBTyxzQkFBdUJMLE1BQU0sQ0FBQ1ksT0FBT3RCLFVBQVVvQixhQUFhLEVBQUVmLG9CQUFvQixJQUFJO0lBQ2hKO0lBQ0EsT0FBT2dCO0FBQ1gsRUFBRXBCO0FBQytCO0FBQ2pDLElBQUlzQixvQkFBb0IsV0FBVyxHQUFJLFNBQVVyQixNQUFNO0lBQ25ESCxnREFBU0EsQ0FBQ3dCLG1CQUFtQnJCO0lBQzdCLFNBQVNxQixrQkFBa0JULFVBQVUsRUFBRVQsZUFBZTtRQUNsRCxPQUFPSCxPQUFPSyxJQUFJLENBQUMsSUFBSSxFQUFFLHFDQUFzQ0csTUFBTSxDQUFDSSxZQUFZLHNDQUF3Q0osTUFBTSxDQUFDTCxpQkFBaUIsTUFBT0wsVUFBVXdCLGFBQWEsRUFBRW5CLG9CQUFvQixJQUFJO0lBQzlNO0lBQ0EsT0FBT2tCO0FBQ1gsRUFBRXRCO0FBQzJCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc21lLWFuYWx5dGljYS1kb2NzLy4vbm9kZV9tb2R1bGVzL2ludGwtbWVzc2FnZWZvcm1hdC9saWIvc3JjL2Vycm9yLmpzPzQyNzUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgX19leHRlbmRzIH0gZnJvbSBcInRzbGliXCI7XG5leHBvcnQgdmFyIEVycm9yQ29kZTtcbihmdW5jdGlvbiAoRXJyb3JDb2RlKSB7XG4gICAgLy8gV2hlbiB3ZSBoYXZlIGEgcGxhY2Vob2xkZXIgYnV0IG5vIHZhbHVlIHRvIGZvcm1hdFxuICAgIEVycm9yQ29kZVtcIk1JU1NJTkdfVkFMVUVcIl0gPSBcIk1JU1NJTkdfVkFMVUVcIjtcbiAgICAvLyBXaGVuIHZhbHVlIHN1cHBsaWVkIGlzIGludmFsaWRcbiAgICBFcnJvckNvZGVbXCJJTlZBTElEX1ZBTFVFXCJdID0gXCJJTlZBTElEX1ZBTFVFXCI7XG4gICAgLy8gV2hlbiB3ZSBuZWVkIHNwZWNpZmljIEludGwgQVBJIGJ1dCBpdCdzIG5vdCBhdmFpbGFibGVcbiAgICBFcnJvckNvZGVbXCJNSVNTSU5HX0lOVExfQVBJXCJdID0gXCJNSVNTSU5HX0lOVExfQVBJXCI7XG59KShFcnJvckNvZGUgfHwgKEVycm9yQ29kZSA9IHt9KSk7XG52YXIgRm9ybWF0RXJyb3IgPSAvKiogQGNsYXNzICovIChmdW5jdGlvbiAoX3N1cGVyKSB7XG4gICAgX19leHRlbmRzKEZvcm1hdEVycm9yLCBfc3VwZXIpO1xuICAgIGZ1bmN0aW9uIEZvcm1hdEVycm9yKG1zZywgY29kZSwgb3JpZ2luYWxNZXNzYWdlKSB7XG4gICAgICAgIHZhciBfdGhpcyA9IF9zdXBlci5jYWxsKHRoaXMsIG1zZykgfHwgdGhpcztcbiAgICAgICAgX3RoaXMuY29kZSA9IGNvZGU7XG4gICAgICAgIF90aGlzLm9yaWdpbmFsTWVzc2FnZSA9IG9yaWdpbmFsTWVzc2FnZTtcbiAgICAgICAgcmV0dXJuIF90aGlzO1xuICAgIH1cbiAgICBGb3JtYXRFcnJvci5wcm90b3R5cGUudG9TdHJpbmcgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiBcIltmb3JtYXRqcyBFcnJvcjogXCIuY29uY2F0KHRoaXMuY29kZSwgXCJdIFwiKS5jb25jYXQodGhpcy5tZXNzYWdlKTtcbiAgICB9O1xuICAgIHJldHVybiBGb3JtYXRFcnJvcjtcbn0oRXJyb3IpKTtcbmV4cG9ydCB7IEZvcm1hdEVycm9yIH07XG52YXIgSW52YWxpZFZhbHVlRXJyb3IgPSAvKiogQGNsYXNzICovIChmdW5jdGlvbiAoX3N1cGVyKSB7XG4gICAgX19leHRlbmRzKEludmFsaWRWYWx1ZUVycm9yLCBfc3VwZXIpO1xuICAgIGZ1bmN0aW9uIEludmFsaWRWYWx1ZUVycm9yKHZhcmlhYmxlSWQsIHZhbHVlLCBvcHRpb25zLCBvcmlnaW5hbE1lc3NhZ2UpIHtcbiAgICAgICAgcmV0dXJuIF9zdXBlci5jYWxsKHRoaXMsIFwiSW52YWxpZCB2YWx1ZXMgZm9yIFxcXCJcIi5jb25jYXQodmFyaWFibGVJZCwgXCJcXFwiOiBcXFwiXCIpLmNvbmNhdCh2YWx1ZSwgXCJcXFwiLiBPcHRpb25zIGFyZSBcXFwiXCIpLmNvbmNhdChPYmplY3Qua2V5cyhvcHRpb25zKS5qb2luKCdcIiwgXCInKSwgXCJcXFwiXCIpLCBFcnJvckNvZGUuSU5WQUxJRF9WQUxVRSwgb3JpZ2luYWxNZXNzYWdlKSB8fCB0aGlzO1xuICAgIH1cbiAgICByZXR1cm4gSW52YWxpZFZhbHVlRXJyb3I7XG59KEZvcm1hdEVycm9yKSk7XG5leHBvcnQgeyBJbnZhbGlkVmFsdWVFcnJvciB9O1xudmFyIEludmFsaWRWYWx1ZVR5cGVFcnJvciA9IC8qKiBAY2xhc3MgKi8gKGZ1bmN0aW9uIChfc3VwZXIpIHtcbiAgICBfX2V4dGVuZHMoSW52YWxpZFZhbHVlVHlwZUVycm9yLCBfc3VwZXIpO1xuICAgIGZ1bmN0aW9uIEludmFsaWRWYWx1ZVR5cGVFcnJvcih2YWx1ZSwgdHlwZSwgb3JpZ2luYWxNZXNzYWdlKSB7XG4gICAgICAgIHJldHVybiBfc3VwZXIuY2FsbCh0aGlzLCBcIlZhbHVlIGZvciBcXFwiXCIuY29uY2F0KHZhbHVlLCBcIlxcXCIgbXVzdCBiZSBvZiB0eXBlIFwiKS5jb25jYXQodHlwZSksIEVycm9yQ29kZS5JTlZBTElEX1ZBTFVFLCBvcmlnaW5hbE1lc3NhZ2UpIHx8IHRoaXM7XG4gICAgfVxuICAgIHJldHVybiBJbnZhbGlkVmFsdWVUeXBlRXJyb3I7XG59KEZvcm1hdEVycm9yKSk7XG5leHBvcnQgeyBJbnZhbGlkVmFsdWVUeXBlRXJyb3IgfTtcbnZhciBNaXNzaW5nVmFsdWVFcnJvciA9IC8qKiBAY2xhc3MgKi8gKGZ1bmN0aW9uIChfc3VwZXIpIHtcbiAgICBfX2V4dGVuZHMoTWlzc2luZ1ZhbHVlRXJyb3IsIF9zdXBlcik7XG4gICAgZnVuY3Rpb24gTWlzc2luZ1ZhbHVlRXJyb3IodmFyaWFibGVJZCwgb3JpZ2luYWxNZXNzYWdlKSB7XG4gICAgICAgIHJldHVybiBfc3VwZXIuY2FsbCh0aGlzLCBcIlRoZSBpbnRsIHN0cmluZyBjb250ZXh0IHZhcmlhYmxlIFxcXCJcIi5jb25jYXQodmFyaWFibGVJZCwgXCJcXFwiIHdhcyBub3QgcHJvdmlkZWQgdG8gdGhlIHN0cmluZyBcXFwiXCIpLmNvbmNhdChvcmlnaW5hbE1lc3NhZ2UsIFwiXFxcIlwiKSwgRXJyb3JDb2RlLk1JU1NJTkdfVkFMVUUsIG9yaWdpbmFsTWVzc2FnZSkgfHwgdGhpcztcbiAgICB9XG4gICAgcmV0dXJuIE1pc3NpbmdWYWx1ZUVycm9yO1xufShGb3JtYXRFcnJvcikpO1xuZXhwb3J0IHsgTWlzc2luZ1ZhbHVlRXJyb3IgfTtcbiJdLCJuYW1lcyI6WyJfX2V4dGVuZHMiLCJFcnJvckNvZGUiLCJGb3JtYXRFcnJvciIsIl9zdXBlciIsIm1zZyIsImNvZGUiLCJvcmlnaW5hbE1lc3NhZ2UiLCJfdGhpcyIsImNhbGwiLCJwcm90b3R5cGUiLCJ0b1N0cmluZyIsImNvbmNhdCIsIm1lc3NhZ2UiLCJFcnJvciIsIkludmFsaWRWYWx1ZUVycm9yIiwidmFyaWFibGVJZCIsInZhbHVlIiwib3B0aW9ucyIsIk9iamVjdCIsImtleXMiLCJqb2luIiwiSU5WQUxJRF9WQUxVRSIsIkludmFsaWRWYWx1ZVR5cGVFcnJvciIsInR5cGUiLCJNaXNzaW5nVmFsdWVFcnJvciIsIk1JU1NJTkdfVkFMVUUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/intl-messageformat/lib/src/error.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/intl-messageformat/lib/src/formatters.js":
/*!***************************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/src/formatters.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PART_TYPE: () => (/* binding */ PART_TYPE),\n/* harmony export */   formatToParts: () => (/* binding */ formatToParts),\n/* harmony export */   isFormatXMLElementFn: () => (/* binding */ isFormatXMLElementFn)\n/* harmony export */ });\n/* harmony import */ var _formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @formatjs/icu-messageformat-parser */ \"(rsc)/./node_modules/@formatjs/icu-messageformat-parser/lib/index.js\");\n/* harmony import */ var _error__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./error */ \"(rsc)/./node_modules/intl-messageformat/lib/src/error.js\");\n\n\nvar PART_TYPE;\n(function(PART_TYPE) {\n    PART_TYPE[PART_TYPE[\"literal\"] = 0] = \"literal\";\n    PART_TYPE[PART_TYPE[\"object\"] = 1] = \"object\";\n})(PART_TYPE || (PART_TYPE = {}));\nfunction mergeLiteral(parts) {\n    if (parts.length < 2) {\n        return parts;\n    }\n    return parts.reduce(function(all, part) {\n        var lastPart = all[all.length - 1];\n        if (!lastPart || lastPart.type !== PART_TYPE.literal || part.type !== PART_TYPE.literal) {\n            all.push(part);\n        } else {\n            lastPart.value += part.value;\n        }\n        return all;\n    }, []);\n}\nfunction isFormatXMLElementFn(el) {\n    return typeof el === \"function\";\n}\n// TODO(skeleton): add skeleton support\nfunction formatToParts(els, locales, formatters, formats, values, currentPluralValue, // For debugging\noriginalMessage) {\n    // Hot path for straight simple msg translations\n    if (els.length === 1 && (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isLiteralElement)(els[0])) {\n        return [\n            {\n                type: PART_TYPE.literal,\n                value: els[0].value\n            }\n        ];\n    }\n    var result = [];\n    for(var _i = 0, els_1 = els; _i < els_1.length; _i++){\n        var el = els_1[_i];\n        // Exit early for string parts.\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isLiteralElement)(el)) {\n            result.push({\n                type: PART_TYPE.literal,\n                value: el.value\n            });\n            continue;\n        }\n        // TODO: should this part be literal type?\n        // Replace `#` in plural rules with the actual numeric value.\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isPoundElement)(el)) {\n            if (typeof currentPluralValue === \"number\") {\n                result.push({\n                    type: PART_TYPE.literal,\n                    value: formatters.getNumberFormat(locales).format(currentPluralValue)\n                });\n            }\n            continue;\n        }\n        var varName = el.value;\n        // Enforce that all required values are provided by the caller.\n        if (!(values && varName in values)) {\n            throw new _error__WEBPACK_IMPORTED_MODULE_1__.MissingValueError(varName, originalMessage);\n        }\n        var value = values[varName];\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isArgumentElement)(el)) {\n            if (!value || typeof value === \"string\" || typeof value === \"number\") {\n                value = typeof value === \"string\" || typeof value === \"number\" ? String(value) : \"\";\n            }\n            result.push({\n                type: typeof value === \"string\" ? PART_TYPE.literal : PART_TYPE.object,\n                value: value\n            });\n            continue;\n        }\n        // Recursively format plural and select parts' option — which can be a\n        // nested pattern structure. The choosing of the option to use is\n        // abstracted-by and delegated-to the part helper object.\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isDateElement)(el)) {\n            var style = typeof el.style === \"string\" ? formats.date[el.style] : (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isDateTimeSkeleton)(el.style) ? el.style.parsedOptions : undefined;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters.getDateTimeFormat(locales, style).format(value)\n            });\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isTimeElement)(el)) {\n            var style = typeof el.style === \"string\" ? formats.time[el.style] : (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isDateTimeSkeleton)(el.style) ? el.style.parsedOptions : formats.time.medium;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters.getDateTimeFormat(locales, style).format(value)\n            });\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isNumberElement)(el)) {\n            var style = typeof el.style === \"string\" ? formats.number[el.style] : (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isNumberSkeleton)(el.style) ? el.style.parsedOptions : undefined;\n            if (style && style.scale) {\n                value = value * (style.scale || 1);\n            }\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters.getNumberFormat(locales, style).format(value)\n            });\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isTagElement)(el)) {\n            var children = el.children, value_1 = el.value;\n            var formatFn = values[value_1];\n            if (!isFormatXMLElementFn(formatFn)) {\n                throw new _error__WEBPACK_IMPORTED_MODULE_1__.InvalidValueTypeError(value_1, \"function\", originalMessage);\n            }\n            var parts = formatToParts(children, locales, formatters, formats, values, currentPluralValue);\n            var chunks = formatFn(parts.map(function(p) {\n                return p.value;\n            }));\n            if (!Array.isArray(chunks)) {\n                chunks = [\n                    chunks\n                ];\n            }\n            result.push.apply(result, chunks.map(function(c) {\n                return {\n                    type: typeof c === \"string\" ? PART_TYPE.literal : PART_TYPE.object,\n                    value: c\n                };\n            }));\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isSelectElement)(el)) {\n            var opt = el.options[value] || el.options.other;\n            if (!opt) {\n                throw new _error__WEBPACK_IMPORTED_MODULE_1__.InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values));\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isPluralElement)(el)) {\n            var opt = el.options[\"=\".concat(value)];\n            if (!opt) {\n                if (!Intl.PluralRules) {\n                    throw new _error__WEBPACK_IMPORTED_MODULE_1__.FormatError('Intl.PluralRules is not available in this environment.\\nTry polyfilling it using \"@formatjs/intl-pluralrules\"\\n', _error__WEBPACK_IMPORTED_MODULE_1__.ErrorCode.MISSING_INTL_API, originalMessage);\n                }\n                var rule = formatters.getPluralRules(locales, {\n                    type: el.pluralType\n                }).select(value - (el.offset || 0));\n                opt = el.options[rule] || el.options.other;\n            }\n            if (!opt) {\n                throw new _error__WEBPACK_IMPORTED_MODULE_1__.InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values, value - (el.offset || 0)));\n            continue;\n        }\n    }\n    return mergeLiteral(result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/intl-messageformat/lib/src/formatters.js\n");

/***/ })

};
;