import { useTranslations } from "next-intl";
import { Link } from "@/i18n/routing";
import { Github, Twitter, Linkedin, Mail } from "lucide-react";

export function Footer() {
  const t = useTranslations("footer");

  return (
    <footer className="border-t bg-background">
      <div className="container py-8 md:py-12">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-4">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="h-6 w-6 rounded bg-primary" />
              <span className="font-bold">SME Analytica</span>
            </div>
            <p className="text-sm text-muted-foreground">
              AI-powered business intelligence platform for small and medium enterprises.
            </p>
            <div className="flex space-x-4">
              <Link
                href="https://github.com/sme-analytica"
                className="text-muted-foreground hover:text-foreground"
              >
                <Github className="h-5 w-5" />
                <span className="sr-only">GitHub</span>
              </Link>
              <Link
                href="https://twitter.com/smeanalytica"
                className="text-muted-foreground hover:text-foreground"
              >
                <Twitter className="h-5 w-5" />
                <span className="sr-only">Twitter</span>
              </Link>
              <Link
                href="https://linkedin.com/company/sme-analytica"
                className="text-muted-foreground hover:text-foreground"
              >
                <Linkedin className="h-5 w-5" />
                <span className="sr-only">LinkedIn</span>
              </Link>
              <Link
                href="mailto:<EMAIL>"
                className="text-muted-foreground hover:text-foreground"
              >
                <Mail className="h-5 w-5" />
                <span className="sr-only">Email</span>
              </Link>
            </div>
          </div>

          {/* Platform */}
          <div className="space-y-4">
            <h3 className="text-sm font-semibold">Platform</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link
                  href="/platform"
                  className="text-muted-foreground hover:text-foreground"
                >
                  Overview
                </Link>
              </li>
              <li>
                <Link
                  href="/platform#features"
                  className="text-muted-foreground hover:text-foreground"
                >
                  Features
                </Link>
              </li>
              <li>
                <Link
                  href="/platform#tech-stack"
                  className="text-muted-foreground hover:text-foreground"
                >
                  Tech Stack
                </Link>
              </li>
              <li>
                <Link
                  href="/ai-automation"
                  className="text-muted-foreground hover:text-foreground"
                >
                  AI & Automation
                </Link>
              </li>
            </ul>
          </div>

          {/* Modules */}
          <div className="space-y-4">
            <h3 className="text-sm font-semibold">Modules</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link
                  href="/modules/menuflow"
                  className="text-muted-foreground hover:text-foreground"
                >
                  MenuFlow
                </Link>
              </li>
              <li>
                <Link
                  href="/modules/sme-app"
                  className="text-muted-foreground hover:text-foreground"
                >
                  SME App
                </Link>
              </li>
              <li>
                <Link
                  href="/modules/connecto"
                  className="text-muted-foreground hover:text-foreground"
                >
                  Connecto
                </Link>
              </li>
              <li>
                <Link
                  href="/modules/ticketing"
                  className="text-muted-foreground hover:text-foreground"
                >
                  Event Ticketing
                </Link>
              </li>
            </ul>
          </div>

          {/* Resources */}
          <div className="space-y-4">
            <h3 className="text-sm font-semibold">Resources</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link
                  href="/integrations"
                  className="text-muted-foreground hover:text-foreground"
                >
                  API Documentation
                </Link>
              </li>
              <li>
                <Link
                  href="/for-businesses"
                  className="text-muted-foreground hover:text-foreground"
                >
                  For Businesses
                </Link>
              </li>
              <li>
                <Link
                  href="/about"
                  className="text-muted-foreground hover:text-foreground"
                >
                  About Us
                </Link>
              </li>
              <li>
                <Link
                  href="https://api.smeanalytica.dev/docs"
                  target="_blank"
                  className="text-muted-foreground hover:text-foreground"
                >
                  Live API Docs
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-8 border-t pt-8">
          <div className="flex flex-col items-center justify-between space-y-4 md:flex-row md:space-y-0">
            <p className="text-sm text-muted-foreground">
              © 2024 SME Analytica. All rights reserved.
            </p>
            <div className="flex space-x-6 text-sm">
              <Link
                href="/privacy"
                className="text-muted-foreground hover:text-foreground"
              >
                Privacy Policy
              </Link>
              <Link
                href="/terms"
                className="text-muted-foreground hover:text-foreground"
              >
                Terms of Service
              </Link>
              <Link
                href="/cookies"
                className="text-muted-foreground hover:text-foreground"
              >
                Cookie Policy
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
