{"nav": {"home": "Home", "platform": "Platform Overview", "modules": "<PERSON><PERSON><PERSON>", "ai": "AI & Automation", "integration": "Integrations", "business": "For Businesses", "about": "About Us", "developer": "Developer Docs", "language": "Language"}, "home": {"title": "SME Analytica Documentation", "subtitle": "The ultimate guide to AI-powered business intelligence for small and medium enterprises", "description": "Comprehensive documentation for business owners, developers, VCs, and investors to understand what SME Analytica is about, what we build, and how to use our system.", "getStarted": "Get Started", "viewDemo": "View Demo", "mission": "Democratizing AI analytics for every small business", "vision": "To become the leading AI business intelligence platform that empowers SMEs to compete with enterprise-level insights", "whoItsFor": {"title": "Who SME Analytica is For", "sme": {"title": "Small & Medium Enterprises", "description": "Restaurants, retail stores, clinics, salons, and service businesses"}, "hospitality": {"title": "Hospitality Industry", "description": "Hotels, restaurants, cafes, and entertainment venues"}, "retail": {"title": "Retail & E-commerce", "description": "Physical stores, online shops, and hybrid retail businesses"}, "healthcare": {"title": "Healthcare & Wellness", "description": "Clinics, dental offices, spas, and fitness centers"}}}, "platform": {"title": "Platform Overview", "whatIs": {"title": "What is SME Analytica?", "description": "SME Analytica is a next-generation AI Business Intelligence platform that helps small and medium enterprises make data-driven decisions through advanced analytics, sentiment analysis, and dynamic pricing optimization."}, "benefits": {"title": "Core Benefits", "realtime": {"title": "Real-time Analytics", "description": "Get instant insights into your business performance with live data updates and monitoring"}, "ai": {"title": "AI-powered Insights", "description": "Advanced machine learning algorithms analyze your data to provide actionable recommendations"}, "pricing": {"title": "Dynamic Pricing", "description": "Optimize your pricing strategy with AI-driven demand forecasting and competitor analysis"}, "integration": {"title": "Seamless Integration", "description": "Connect with existing POS systems, payment processors, and business tools"}}, "techStack": {"title": "Technology Stack", "backend": "FastAPI Python backend with advanced AI integration", "frontend": "React Native mobile app and Next.js web applications", "database": "Supabase with PostgreSQL for robust data management", "ai": "OpenRouter integration with GPT, Claude, DeepSeek, and Perplexity models", "infrastructure": "AWS cloud infrastructure with Docker containerization"}}, "modules": {"title": "SME Analytica Modules", "menuflow": {"title": "<PERSON><PERSON><PERSON><PERSON> (Restaurant Management)", "description": "Complete restaurant management system with dynamic pricing, QR-based ordering, and POS integration", "features": {"pricing": "AI-powered dynamic pricing system", "qr": "QR code-based live menu ordering", "tracking": "Real-time traffic and order tracking", "pos": "Seamless POS system integration", "analytics": "Comprehensive restaurant analytics"}, "link": "restaurants.smeanalytica.dev"}, "connecto": {"title": "<PERSON><PERSON><PERSON> (AI Voice Receptionist)", "description": "Intelligent voice assistant that handles customer calls, bookings, and inquiries", "features": {"voice": "Natural language voice interaction", "booking": "Automated reservation and appointment booking", "multilingual": "Support for multiple languages", "integration": "Integration with existing phone systems", "analytics": "Call analytics and insights"}, "status": "Under Construction"}, "mobile": {"title": "SME Analytics App", "description": "Comprehensive mobile application for business analysis and insights", "features": {"sales": "Sales performance analysis", "sentiment": "Customer sentiment tracking", "competitor": "Competitor benchmarking", "kpi": "Real-time KPI visualizations", "insights": "AI-generated business insights"}}, "ticketing": {"title": "Event Ticketing Engine", "description": "Dynamic pricing ticketing system for events, clubs, and entertainment venues", "features": {"dynamic": "Real-time dynamic pricing", "events": "Event management and promotion", "analytics": "Ticket sales analytics", "integration": "Venue management integration"}, "status": "Future Development"}}, "ai": {"title": "AI & Automation", "description": "How SME Analytica leverages artificial intelligence to provide intelligent business insights", "models": {"title": "AI Models Used", "llm": "Large Language Models (GPT<PERSON><PERSON>, <PERSON>, DeepSeek)", "ml": "Machine Learning algorithms for predictive analytics", "nlp": "Natural Language Processing for sentiment analysis", "computer_vision": "Computer vision for image and video analysis"}, "dataSources": {"title": "Data Sources", "google": "Google Places API for business data", "tripadvisor": "TripAdvisor API for reviews and ratings", "yelp": "Yelp API for customer feedback", "internal": "Internal business data and transactions"}, "privacy": {"title": "Data Privacy & Security", "encryption": "End-to-end encryption for all data transmission", "compliance": "GDPR and CCPA compliant data handling", "storage": "Secure cloud storage with backup redundancy", "access": "Role-based access control and authentication"}}, "integration": {"title": "Integration Documentation", "pos": {"title": "POS System Integration", "description": "Connect SME Analytica with popular point-of-sale systems", "supported": "Supported systems: Square, Toast, Clover, Shopify POS", "features": "Real-time sales data sync, inventory management, customer data integration"}, "supabase": {"title": "Supabase Schema", "description": "Database architecture and table relationships", "tables": "Core tables: users, businesses, analyses, transactions, insights"}, "webhooks": {"title": "Webhooks & APIs", "description": "RESTful APIs and webhook integration for third-party services", "endpoints": "Authentication, analysis, reporting, and notification endpoints"}}, "business": {"title": "For Businesses", "useCases": {"title": "Use Cases by Industry", "restaurant": "Restaurant optimization, menu engineering, customer flow analysis", "retail": "Inventory optimization, customer behavior analysis, pricing strategies", "healthcare": "Patient flow optimization, appointment scheduling, service analysis", "hospitality": "Occupancy optimization, guest experience analysis, revenue management"}, "onboarding": {"title": "How to Get Started", "step1": "Sign up for SME Analytica account", "step2": "Complete business profile and verification", "step3": "Connect your existing business tools", "step4": "Start analyzing with AI-powered insights"}, "demo": {"title": "Request a Demo", "description": "See SME Analytica in action with a personalized demo for your business", "contact": "Contact our team for a free consultation and demo"}}, "about": {"title": "About SME Analytica", "company": {"title": "Company Overview", "description": "SME Analytica was founded with the mission to democratize AI-powered business intelligence for small and medium enterprises worldwide."}, "timeline": {"title": "Company Timeline", "2023": "Platform development and AI integration", "2024": "Mobile app launch and restaurant module deployment", "2025": "Multi-industry expansion and advanced features"}, "contact": {"title": "Contact Information", "email": "<EMAIL>", "twitter": "@smeanalytica", "website": "smeanalytica.dev"}}, "developer": {"title": "Developer Documentation", "api": {"title": "API Reference", "description": "Complete API documentation for integrating with SME Analytica", "authentication": "JWT-based authentication system", "endpoints": "RESTful endpoints for all platform features"}, "webhooks": {"title": "Webhook Events", "description": "Real-time event notifications for business data changes", "events": "analysis_complete, pricing_update, alert_triggered"}, "sdk": {"title": "Software Development Kits", "javascript": "JavaScript/TypeScript SDK for web integration", "python": "Python SDK for backend integration", "mobile": "React Native components for mobile apps"}}}