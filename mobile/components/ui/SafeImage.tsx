import React, { useState, useEffect } from 'react';
import {
  Image as RNImage,
  ImageProps,
  View,
  ActivityIndicator,
  StyleSheet,
  Text
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';

interface SafeImageProps extends Omit<ImageProps, 'source'> {
  uri: string | null;
  fallbackIcon?: keyof typeof Feather.glyphMap;
  fallbackIconSize?: number;
  showLoadingIndicator?: boolean;
  isProfileImage?: boolean; // Whether this is a profile image (for better error messages)
  useTestImage?: boolean; // For backward compatibility
  maxRetries?: number; // For backward compatibility
}

const MAX_RETRIES = 1; // Only try once before showing fallback

/**
 * A safe image component that handles loading errors gracefully
 * and provides fallback UI when images fail to load
 */
const SafeImage: React.FC<SafeImageProps> = ({
  uri,
  fallbackIcon = 'image',
  fallbackIconSize = 24,
  showLoadingIndicator = true,
  isProfileImage = false,
  style,
  ...props
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [currentUri, setCurrentUri] = useState<string | null>(null);

  const { colors } = useTheme();

  // Validate and set the initial URI
  useEffect(() => {
    // Reset state when URI changes
    setRetryCount(0);
    setError(false);

    if (!uri) {
      // No URI provided, show fallback immediately
      setLoading(false);
      setError(true);
      setCurrentUri(null);
      return;
    }

    // Check if this is a known deleted image
    const isKnownDeletedImage =
      uri.includes('1744469424865-1cmwq27v.png') ||
      uri.includes('1744440640732-asyys4ea.png');

    if (isKnownDeletedImage) {
      setLoading(false);
      setError(true);
      setCurrentUri(null);
      return;
    }

    // Process URI based on source (Supabase or Cloudinary)
    let processedUri = uri;

    // Handle Supabase URLs
    if (uri.includes('supabase.co/storage')) {
      // Add content-type parameter if needed
      if (!uri.includes('content-type=')) {
        const separator = uri.includes('?') ? '&' : '?';
        processedUri = `${uri}${separator}content-type=image/png`;
      }

      // Add cache busting
      const cacheBuster = `t=${Date.now()}`;
      processedUri = `${processedUri}${processedUri.includes('?') ? '&' : '?'}${cacheBuster}`;
    }
    // Handle Cloudinary URLs
    else if (uri.includes('res.cloudinary.com')) {
      // Cloudinary URLs don't need cache busting as they have versioning built-in
      // But we can optimize them for better performance
      if (!uri.includes('/upload/f_auto')) {
        // Add auto format and quality optimization if not already present
        processedUri = uri.replace('/upload/', '/upload/f_auto,q_auto/');
      }
    }
    // For all other URLs, check if it's a valid image URL
    else {
      // Check if URL looks like a webpage rather than an image
      const isWebpage = uri.includes('/photo/') || 
                       uri.includes('/image/') || 
                       (!uri.match(/\.(jpg|jpeg|png|gif|webp|bmp|svg)(\?|$)/i) && 
                        (uri.includes('freeimages.com') || uri.includes('unsplash.com') || uri.includes('pixabay.com')));
      
      if (isWebpage) {
        // This looks like a webpage URL, not a direct image URL
        setLoading(false);
        setError(true);
        setCurrentUri(null);
        return;
      }
      
      const cacheBuster = `t=${Date.now()}`;
      processedUri = `${uri}${uri.includes('?') ? '&' : '?'}${cacheBuster}`;
    }

    setCurrentUri(processedUri);
    setLoading(true);

    // Prefetch the image to check if it's valid
    RNImage.prefetch(processedUri)
      .then(() => {
        setLoading(false);
      })
      .catch(error => {
        // If prefetch fails, we'll still try to load it normally
        // The onError handler will catch any loading errors
        setLoading(false);
      });
  }, [uri]);

  // Handle image loading errors
  const handleError = () => {
    if (retryCount < MAX_RETRIES) {
      // Try with a local fallback image
      setRetryCount(retryCount + 1);

      // Use a local fallback image instead of placekitten.com
      // This is more reliable as it doesn't depend on external services
      setCurrentUri(null); // This will trigger the fallback icon
      setError(true);
      setLoading(false);
    } else {
      // Show fallback icon
      setError(true);
      setLoading(false);
    }
  };

  // If there's an error or no URI, show the fallback icon
  if (error || !currentUri) {
    return (
      <View style={[styles.container, style]}>
        <View style={styles.fallbackContainer}>
          <Feather name={fallbackIcon} size={fallbackIconSize} color={colors.textSecondary} />
          {isProfileImage && (
            <Text style={[styles.errorText, { color: colors.textSecondary }]}>
              Reset profile image
            </Text>
          )}
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      <RNImage
        {...props}
        source={{ uri: currentUri }}
        style={[styles.image, style]}
        onLoadStart={() => setLoading(true)}
        onLoad={() => setLoading(false)}
        onLoadEnd={() => setLoading(false)}
        onError={handleError}
      />

      {loading && showLoadingIndicator && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={colors.primary} />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  loadingContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
  fallbackContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    marginTop: 8,
    fontSize: 12,
    textAlign: 'center',
  }
});

export default SafeImage;
