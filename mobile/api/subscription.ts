import { logger } from '../utils/logger';
import { apiClient } from '../utils/api';
import { SubscriptionTier } from '../types/subscription';
import { AnalysisType } from '../types/analysis';
// Removed debug import
import { supabase } from '../utils/supabase';
// Removed process tracker import

interface SubscriptionResponse {
  data?: {
    tier?: SubscriptionTier;
    message: string;
    subscription_updated_at?: string;
  };
  error?: {
    message: string;
    status?: number;
  };
}

interface UsageResponse {
  current_usage: number;
  limits: {
    max_analyses_per_month: number;
    max_tokens_per_analysis: number;
    max_concurrent_analyses: number;
    max_competitors?: number;
    update_frequency?: string;
    historical_data_months?: number;
  };
}

interface SubscriptionInfo {
  tier: SubscriptionTier;
  subscription_date: string;
  updated_at?: string;
  status: 'active' | 'inactive' | 'pending';
  limits: {
    max_analyses_per_month: number;
    max_tokens_per_analysis: number;
    max_concurrent_analyses: number;
    frameworks: string[];
    max_competitors: number;
    update_frequency: string;
    historical_data_months: number;
  };
  usage: {
    analyses_this_month: number;
    remaining_analyses: number;
  };
  remaining_credits?: number;
  total_credits?: number;
  renewal_date?: string;
}

// Get the current subscription period range based on subscription date
const getSubscriptionPeriodRange = async (userId: string): Promise<{ start: Date; end: Date }> => {
  try {
    const { data: existingProfile, error: profileError } = await supabase
      .from('profiles_view')
      .select('subscription_updated_at, subscription_tier')
      .eq('id', userId)
      .single();

    if (profileError || !existingProfile) {
      const { data: response } = await apiClient.get<SubscriptionResponse>('/api/v1/subscription/status');

      if (!response || !response.data) {
        throw new Error('Invalid subscription response');
      }

      // Use the update function instead of direct table update
      const { data: updatedProfile, error: updateError } = await supabase.rpc('update_user_profile_with_business', {
        user_id_param: userId,
        profile_updates: {
          subscription_tier: response.data.tier,
          subscription_updated_at: new Date().toISOString()
        },
        business_updates: {}
      });

      if (updateError) {
        throw new Error('Failed to update subscription profile');
      }

      const subscriptionDate = new Date(updatedProfile.subscription_updated_at);
      return calculatePeriodRange(subscriptionDate);
    }

    return calculatePeriodRange(new Date(existingProfile.subscription_updated_at));
  } catch (error) {
    logger.error('Error in subscription period range:', error);
    // Fallback to current month
    const now = new Date();
    return calculatePeriodRange(now);
  }
};

const calculatePeriodRange = (subscriptionDate: Date): { start: Date; end: Date } => {
  const now = new Date();
  const currentDay = subscriptionDate.getDate();
  const periodStart = new Date(now.getFullYear(), now.getMonth(), currentDay);

  if (now.getDate() < currentDay) {
    periodStart.setMonth(periodStart.getMonth() - 1);
  }

  const periodEnd = new Date(periodStart);
  periodEnd.setMonth(periodEnd.getMonth() + 1);
  periodEnd.setDate(periodEnd.getDate() - 1);

  return { start: periodStart, end: periodEnd };
};

// Get the analysis count for the current subscription period
const getSubscriptionPeriodAnalysisCount = async (userId: string): Promise<number> => {
  try {
    const { start, end } = await getSubscriptionPeriodRange(userId);
    const { count, error } = await supabase
      .from('analyses')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .gte('created_at', start.toISOString())
      .lte('created_at', end.toISOString());

    if (error) {
      logger.error('Error getting analysis count:', error);
      return 0;
    }

    return count || 0;
  } catch (error) {
    logger.error('Error in getSubscriptionPeriodAnalysisCount:', error);
    return 0;
  }
};

export const subscriptionApi = {
  async getStatus(userId: string): Promise<SubscriptionInfo> {
    try {
      logger.info('Fetching subscription status', { userId });

      // First get the profile from Supabase directly
      const { data: profile, error: profileError } = await supabase
        .from('profiles_view')
        .select('subscription_tier, subscription_updated_at')
        .eq('id', userId)
        .single();

      if (profileError) {
        logger.error('[SubscriptionAPI] Profile fetch error:', profileError);
        throw new Error('Failed to fetch profile');
      }

      // Get subscription period
      const { start, end } = await getSubscriptionPeriodRange(userId);

      // Get analysis count for period
      const analysisCount = await getSubscriptionPeriodAnalysisCount(userId);

      // Get tier limits
      const { data: tierLimits, error: limitsError } = await supabase
        .from('subscription_tiers')
        .select('*')
        .eq('tier', profile.subscription_tier)
        .single();

      if (limitsError || !tierLimits) {
        logger.error('[SubscriptionAPI] Tier limits error:', limitsError);
        throw new Error('Failed to fetch tier limits');
      }

      // Calculate remaining analyses
      const remaining = tierLimits.max_analyses_per_month - analysisCount;

      logger.info('Subscription status fetched', {
        tier: tierLimits.tier,
        analyses: analysisCount,
        remaining
      });

      // Create the subscription info
      const subscriptionInfo: SubscriptionInfo = {
        tier: profile.subscription_tier,
        status: 'active',
        subscription_date: profile.subscription_updated_at,
        updated_at: new Date().toISOString(),
        limits: {
          max_analyses_per_month: tierLimits.max_analyses_per_month || 50,
          max_tokens_per_analysis: tierLimits.max_tokens_per_analysis || 1000,
          max_concurrent_analyses: tierLimits.max_concurrent_analyses || 1,
          frameworks: tierLimits.frameworks || ['basic'],
          max_competitors: tierLimits.max_competitors || 3,
          update_frequency: tierLimits.update_frequency || 'monthly',
          historical_data_months: tierLimits.historical_data_months || 12
        },
        usage: {
          analyses_this_month: analysisCount,
          remaining_analyses: Math.max(0, tierLimits.max_analyses_per_month - analysisCount)
        },
        renewal_date: end.toISOString(),
        remaining_credits: 0,
        total_credits: 0
      };

      return subscriptionInfo;
    } catch (error) {
      logger.error('[SubscriptionAPI] Error in getStatus:', error);
      throw error;
    }
  },

  async getUsage(userId: string): Promise<UsageResponse> {
    try {
      logger.info('[SubscriptionAPI] Fetching subscription usage for user:', userId);
      const response = await apiClient.get<UsageResponse>('/api/v1/subscription/usage');

      if (!response.data) {
        logger.error('[SubscriptionAPI] No data received from subscription usage endpoint');
        throw new Error('No data received from subscription usage endpoint');
      }

      // If we got a response but it doesn't match our expected format, try to adapt it
      if (response.data && typeof response.data === 'object') {
        const data = response.data as any;

        // Ensure we have a properly formatted response
        const formattedResponse: UsageResponse = {
          current_usage: data.current_usage || 0,
          limits: {
            max_analyses_per_month: data.limits?.max_analyses_per_month || 50,
            max_tokens_per_analysis: data.limits?.max_tokens_per_analysis || 1000,
            max_concurrent_analyses: data.limits?.max_concurrent_analyses || 1,
            max_competitors: data.limits?.max_competitors || 3,
            update_frequency: data.limits?.update_frequency || '7 days',
            historical_data_months: data.limits?.historical_data_months || 12
          }
        };

        logger.info('[SubscriptionAPI] Returning formatted subscription usage:', formattedResponse);
        return formattedResponse;
      }

      logger.info('[SubscriptionAPI] Returning subscription usage:', response.data);
      return response.data;
    } catch (error) {
      logger.error('[SubscriptionAPI] Error fetching subscription usage:', error);

      // Return default values on error
      const defaultResponse: UsageResponse = {
        current_usage: 0,
        limits: {
          max_analyses_per_month: 50,
          max_tokens_per_analysis: 1000,
          max_concurrent_analyses: 1,
          max_competitors: 3,
          update_frequency: '7 days',
          historical_data_months: 12
        }
      };

      return defaultResponse;
    }
  },

  async upgrade(userId: string): Promise<SubscriptionInfo> {
    try {
      logger.info('[SubscriptionAPI] Upgrading subscription for user:', userId);

      // Check if we have a valid session before making the API call
      const { data: session } = await supabase.auth.getSession();

      if (!session?.session?.access_token) {
        logger.error('[SubscriptionAPI] No valid session found for upgrade');
        throw new Error('Authentication required. Please log in again.');
      }

      // Get the token for authentication
      const token = session.session.access_token;
      logger.info(`[SubscriptionAPI] Using token for authentication`);

      logger.info('[SubscriptionAPI] Session found, proceeding with upgrade');

      // Call the backend endpoint to upgrade the subscription
      logger.info('[SubscriptionAPI] Calling backend endpoint: subscription/upgrade');

      // Prepare the URL for the API call
      const apiBaseUrl = apiClient.getBaseUrl();
      const fullEndpointUrl = `${apiBaseUrl}/api/v1/subscription/upgrade`;

      // Try a direct fetch call instead of using the apiClient
      // This bypasses any potential issues with the apiClient
      let response;
      try {
        // Make a direct fetch call to the API
        const directUrl = `${apiBaseUrl}/api/v1/subscription/upgrade`;

        const fetchResponse = await fetch(directUrl, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        // Parse the response
        const responseText = await fetchResponse.text();

        let responseData;
        try {
          responseData = JSON.parse(responseText);

        } catch (e) {

          responseData = { message: responseText };
        }

        if (!fetchResponse.ok) {
          throw new Error(`API call failed with status ${fetchResponse.status}: ${responseText}`);
        }

        // Create a response object that matches what the apiClient would return
        response = {
          data: responseData,
          error: null
        };
      } catch (apiError) {
        logger.error('[SubscriptionAPI] API call failed:', apiError);
        throw new Error(`API call failed: ${apiError instanceof Error ? apiError.message : 'Unknown error'}`);
      }

      if (!response) {
        const error = 'No response from upgrade endpoint';
        logger.error(`[SubscriptionAPI] ${error}`);
        throw new Error('No response from server. Please try again.');
      }

      if (response.error) {
        const error = `Error response from upgrade endpoint: ${response.error.message}`;
        logger.error(`[SubscriptionAPI] ${error}`);
        throw new Error(`Upgrade failed: ${response.error.message}`);
      }

      if (!response.data) {
        const error = 'Empty response data from upgrade endpoint';
        logger.error(`[SubscriptionAPI] ${error}`);
        throw new Error('Invalid response from server. Please try again.');
      }

      logger.info('[SubscriptionAPI] Upgrade response:', response.data);

      // Extract the tier from the response
      // The backend response format is { message: string }
      // We need to set the tier to 'premium' since that's what the backend does
      const tier = 'premium';

      // Fetch the updated profile to get the latest subscription info
      logger.info('[SubscriptionAPI] Fetching updated profile after upgrade');

      // Wait longer for the backend to complete the update and for database changes to propagate
      // This helps prevent race conditions with Supabase's eventual consistency
      logger.info('[SubscriptionAPI] Waiting for database updates to propagate...');
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Try multiple times to verify the profile was updated
      let profile;
      let profileError;
      let retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries) {
        try {
          const result = await supabase
            .from('profiles_view')
            .select('*')
            .eq('id', userId)
            .single();

          profile = result.data;
          profileError = result.error;

          if (profileError) {
            logger.error('[SubscriptionAPI] Profile fetch error:', profileError);
            retryCount++;
            if (retryCount < maxRetries) {
              // Wait before retrying
              await new Promise(resolve => setTimeout(resolve, 1000));
            }
          } else if (!profile) {
            logger.error('[SubscriptionAPI] Profile not found');
            retryCount++;
            if (retryCount < maxRetries) {
              // Wait before retrying
              await new Promise(resolve => setTimeout(resolve, 1000));
            }
          } else {
            // Profile found, break the loop
            break;
          }
        } catch (e) {
          logger.error('[SubscriptionAPI] Profile fetch exception:', e);
          retryCount++;
          if (retryCount < maxRetries) {
            // Wait before retrying
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }
      }

      if (profileError) {
        logger.error('[SubscriptionAPI] Error fetching profile:', profileError);
        throw new Error(`Failed to fetch profile: ${profileError.message}`);
      }

      if (!profile) {
        const error = 'Profile not found after multiple attempts';
        logger.error(`[SubscriptionAPI] ${error}`);
        throw new Error(error);
      }

      // Verify the subscription tier was updated
      // Note: The backend might return 'premium' in a different case or format
      const profileTier = profile.subscription_tier?.toLowerCase() || '';
      if (profileTier !== 'premium') {
        const error = `Profile not updated to premium: ${JSON.stringify(profile)}`;
        logger.error(`[SubscriptionAPI] ${error}`);

        // Try one more direct check with Supabase
        try {
          const { data: directProfile, error: directError } = await supabase
            .from('profiles_view')
            .select('subscription_tier, subscription_updated_at')
            .eq('id', userId)
            .single();

          if (directError) {
            logger.error('[SubscriptionAPI] Direct check error:', directError);
          } else if (directProfile && directProfile.subscription_tier?.toLowerCase() === 'premium') {
            // The profile is actually premium, so we can continue
            logger.info('[SubscriptionAPI] Direct check confirmed premium status');
            return {
              tier: 'premium',
              status: 'active',
              analyses_this_month: 0,
              remaining_analyses: 50,
              max_analyses: 50
            };
          }
        } catch (e) {
          logger.error('[SubscriptionAPI] Direct check exception:', e);
        }

        throw new Error('Subscription upgrade failed: Profile not updated to premium');
      }

      logger.info('[SubscriptionAPI] Profile verification successful');

      logger.info('[SubscriptionAPI] Updated profile:', profile);

      const analysesThisPeriod = await getSubscriptionPeriodAnalysisCount(userId);
      const maxAnalyses = tier === 'premium' ? 50 : 3; // Premium gets 50, standard gets 3
      const { end: renewalDate } = await getSubscriptionPeriodRange(userId);

      const subscriptionInfo: SubscriptionInfo = {
        tier: tier,
        status: 'active',
        subscription_date: profile.subscription_updated_at,
        updated_at: profile.updated_at,
        limits: {
          max_analyses_per_month: maxAnalyses,
          max_tokens_per_analysis: tier === 'premium' ? 2000 : 1000,
          max_concurrent_analyses: tier === 'premium' ? 3 : 1,
          frameworks: tier === 'premium' ? ['advanced', 'basic'] : ['basic'],
          max_competitors: tier === 'premium' ? 10 : 3,
          update_frequency: tier === 'premium' ? 'weekly' : 'monthly',
          historical_data_months: tier === 'premium' ? 24 : 12
        },
        usage: {
          analyses_this_month: analysesThisPeriod,
          remaining_analyses: Math.max(0, maxAnalyses - analysesThisPeriod)
        },
        renewal_date: renewalDate.toISOString(),
        remaining_credits: 0,
        total_credits: 0
      };

      logger.info('[SubscriptionAPI] Returning subscription info after upgrade:', {
        tier: subscriptionInfo.tier,
        analyses: subscriptionInfo.usage.analyses_this_month,
        remaining: subscriptionInfo.usage.remaining_analyses,
        frameworks: subscriptionInfo.limits.frameworks
      });

      return subscriptionInfo;
    } catch (error) {
      // Log the error
      logger.error('[SubscriptionAPI] Subscription upgrade failed:', error);
      throw error;
    }
  }
};