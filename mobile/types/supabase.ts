export interface Profile {
  id: string;
  email: string;
  full_name?: string | null;
  first_name?: string | null;
  last_name?: string | null;
  avatar_url?: string | null;
  phone?: string | null;
  timezone?: string | null;
  language?: string | null;
  avatar_load_error?: boolean;
  banner_load_error?: boolean;
  subscription_tier: 'free' | 'standard' | 'premium';
  subscription_status: 'active' | 'inactive' | 'cancelled';
  subscription_updated_at?: string | null;
  last_login_at?: string | null;
  last_sign_in_at?: string | null;
  email_verified?: boolean;
  phone_verified?: boolean;
  preferences?: any;
  metadata?: any;
  is_active?: boolean;
  created_at: string;
  updated_at: string;
  business_name?: string | null;
  business_type?: string | null;
  location?: string | null;
  banner_url?: string | null;
  business_description?: string | null;
  business_website?: string | null;
  business_phone?: string | null;
}

export interface Business {
  id: string;
  user_id: string;
  business_type_id: string;
  name: string;
  description?: string | null;
  email?: string | null;
  phone?: string | null;
  website?: string | null;
  address?: string | null;
  city?: string | null;
  state?: string | null;
  country?: string | null;
  postal_code?: string | null;
  timezone?: string | null;
  currency?: string | null;
  logo_url?: string | null;
  banner_url?: string | null;
  settings?: any;
  is_active?: boolean;
  created_at: string;
  updated_at: string;
}

export interface BusinessType {
  id: string;
  name: string;
  description?: string | null;
  icon?: string | null;
  is_active?: boolean;
  created_at: string;
  updated_at: string;
}

export interface Database {
  public: {
    Tables: {
      users: {
        Row: Profile;
        Insert: Partial<Profile>;
        Update: Partial<Profile>;
      };
      businesses: {
        Row: Business;
        Insert: Partial<Business>;
        Update: Partial<Business>;
      };
      business_types: {
        Row: BusinessType;
        Insert: Partial<BusinessType>;
        Update: Partial<BusinessType>;
      };
      // Keep profiles for backward compatibility during migration
      profiles: {
        Row: Profile;
        Insert: Partial<Profile>;
        Update: Partial<Profile>;
      };
      // Add the new backward compatibility view
      profiles_view: {
        Row: Profile;
        Insert: Partial<Profile>;
        Update: Partial<Profile>;
      };
      // Add other tables as needed
    };
  };
}
