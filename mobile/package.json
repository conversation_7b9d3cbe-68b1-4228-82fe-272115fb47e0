{"name": "mobile", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "setup-storage": "node scripts/setup-supabase-storage.js", "upload-translations": "node scripts/upload-translations.js"}, "dependencies": {"@expo-google-fonts/poppins": "^0.2.3", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/netinfo": "11.4.1", "@react-native-picker/picker": "2.11.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.2.0", "@react-navigation/stack": "^7.0.0", "@supabase/supabase-js": "^2.39.3", "axios": "^1.7.9", "base64-arraybuffer": "^1.0.2", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "constants-browserify": "^1.0.0", "crypto-browserify": "^3.12.1", "date-fns": "^4.1.0", "events": "^3.3.0", "expo": "53", "expo-application": "~6.1.4", "expo-build-properties": "~0.14.6", "expo-constants": "~17.1.6", "expo-dev-client": "~5.1.8", "expo-font": "~13.3.1", "expo-image": "~2.1.7", "expo-image-manipulator": "~13.1.7", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.5", "expo-localization": "~16.1.5", "expo-location": "~18.1.5", "expo-router": "~5.0.7", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.7", "https-browserify": "^1.0.0", "i18next": "^24.2.2", "i18next-http-backend": "^3.0.2", "lottie-react-native": "7.2.2", "moti": "^0.30.0", "node-libs-expo": "^0.0.3", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "process": "^0.11.10", "prop-types": "^15.8.1", "querystring-es3": "^0.2.1", "react": "19.0.0", "react-i18next": "^15.4.1", "react-native": "0.79.2", "react-native-chart-kit": "^6.12.0", "react-native-element-dropdown": "^2.12.4", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~2.24.0", "react-native-iap": "^12.16.2", "react-native-onboarding-swiper": "^1.3.0", "react-native-polyfill-globals": "^3.1.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-tcp": "^4.0.0", "react-native-url-polyfill": "^2.0.0", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "string_decoder": "^1.3.0", "tailwindcss": "^3.4.0", "tls-browserify": "^0.2.2", "url": "^0.11.4", "use-latest-callback": "^0.2.3", "util": "^0.12.5", "victory-native": "^41.16.1"}, "devDependencies": {"@babel/core": "^7.23.9", "@react-native-community/cli": "^18.0.0", "@testing-library/jest-native": "^5.4.2", "@testing-library/react-native": "^12.3.0", "@tsconfig/react-native": "^3.0.5", "@types/jest": "^29.5.0", "@types/react": "~18.3.12", "@types/react-native-onboarding-swiper": "^1.1.9", "babel-plugin-module-resolver": "^5.0.2", "babel-preset-expo": "^12.0.9", "dotenv": "^16.5.0", "metro": "^0.81.0", "metro-react-native-babel-preset": "^0.77.0", "module-resolver": "^1.0.0", "nativewind": "^4.1.23", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "react-native-dotenv": "^3.4.11", "react-test-renderer": "^18.2.0", "typescript": "^5.3.3"}, "private": true}