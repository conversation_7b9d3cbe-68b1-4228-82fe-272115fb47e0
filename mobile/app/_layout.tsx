// import 'dotenv/config';
import { Stack, useSegments, useRouter, SplashScreen } from 'expo-router';
import { AuthProvider, useAuth } from '../contexts/AuthContext';
import { ProfileProvider } from '../contexts/ProfileContext';
import { SubscriptionProvider } from '../contexts/SubscriptionContext';
import { ThemeProvider } from '../contexts/ThemeContext';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { useEffect, useState } from 'react';
import { supabase } from '../utils/supabase';
import { startHealthCheck } from '../utils/healthCheck';
import { LoadingScreen } from '../components/ui/LoadingScreen';
import AsyncStorage from '@react-native-async-storage/async-storage';
import '../utils/i18n'; // Import i18n configuration
import { I18nextProvider } from 'react-i18next';
import i18n from '../utils/i18n';
import { Platform } from 'react-native';
import Constants from 'expo-constants';

SplashScreen.preventAutoHideAsync();

export default function RootLayout(): JSX.Element {
  const [isSupabaseReady, setSupabaseReady] = useState(false);
  const [isThemeReady, setThemeReady] = useState(false);
  const [isI18nReady, setI18nReady] = useState(false);

  // Log environment variables for debugging
  useEffect(() => {
    // Access env vars directly from process.env in development
    // In production (TestFlight), these will be bundled correctly
    console.log('🔎 API URL:', process.env.EXPO_PUBLIC_API_URL || Constants.expoConfig?.extra?.apiUrl);
    console.log('🔎 Supabase URL:', process.env.EXPO_PUBLIC_SUPABASE_URL || Constants.expoConfig?.extra?.supabaseUrl);
    console.log('🔎 Environment:', __DEV__ ? 'Development' : 'Production');
    console.log('🔎 Platform:', Platform.OS);
    console.log('🔎 Bundle ID:', Constants.expoConfig?.ios?.bundleIdentifier);
    console.log('🔎 Is TestFlight:', __DEV__ ? 'No (Development)' : 'Yes (Production)');
  }, []);

  useEffect(() => {
    const initializeSupabase = async () => {
      try {
        console.log('🚀 Starting Supabase initialization...');
        const { data: { session }, error } = await supabase.auth.getSession();
        if (error) {
          console.error('❌ Error initializing Supabase:', error);
        } else {
          console.log('✅ Supabase initialized successfully');
        }
        setSupabaseReady(true);
      } catch (error) {
        console.error('❌ Error initializing Supabase:', error);
        setSupabaseReady(true);
      }
    };

    const initializeTheme = async () => {
      try {
        console.log('🎨 Starting theme initialization...');
        // Load saved theme from AsyncStorage
        const savedTheme = await AsyncStorage.getItem('@theme');
        if (__DEV__) console.log('✅ Loaded saved theme:', savedTheme);
        setThemeReady(true);
      } catch (error) {
        console.error('❌ Error loading theme:', error);
        setThemeReady(true);
      }
    };

    const initializeI18n = async () => {
      try {
        console.log('🌐 Starting i18n initialization...');
        // Load saved language from AsyncStorage
        const savedLanguage = await AsyncStorage.getItem('user-language');
        if (savedLanguage) {
          console.log(`[i18n] Loading saved language: ${savedLanguage}`);
          await i18n.changeLanguage(savedLanguage);

          // Force reload resources
          await i18n.reloadResources(savedLanguage);
          console.log(`[i18n] Resources reloaded for: ${savedLanguage}`);
          console.log(`[i18n] Current language is now: ${i18n.language}`);
        } else {
          // Default to English if no saved language
          console.log('[i18n] No saved language, using default: en');
          await i18n.changeLanguage('en');
          await AsyncStorage.setItem('user-language', 'en');
        }
        console.log('✅ i18n initialized successfully');
        setI18nReady(true);
      } catch (error) {
        console.error('❌ Error initializing i18n:', error);
        setI18nReady(true);
      }
    };

    initializeSupabase();
    initializeTheme();
    initializeI18n();
  }, []);

  useEffect(() => {
    // Start health check monitoring
    console.log('🏥 Starting health check monitoring...');
    const cleanup = startHealthCheck(30000); // Check every 30 seconds
    return () => cleanup();
  }, []);

  console.log('🔍 Initialization status:', {
    supabase: isSupabaseReady,
    theme: isThemeReady,
    i18n: isI18nReady
  });

  if (!isSupabaseReady || !isThemeReady || !isI18nReady) {
    console.log('⏳ Still loading, showing LoadingScreen...');
    return <LoadingScreen />;
  }

  console.log('🚀 All initialization complete, rendering app...');

  return (
    <I18nextProvider i18n={i18n}>
      <SafeAreaProvider>
        <ThemeProvider>
          <AuthProvider>
            <ProfileProvider>
              <SubscriptionProvider>
                <RootLayoutNav />
              </SubscriptionProvider>
            </ProfileProvider>
          </AuthProvider>
        </ThemeProvider>
      </SafeAreaProvider>
    </I18nextProvider>
  );
}

function RootLayoutNav(): JSX.Element {
  const segments = useSegments();
  const router = useRouter();
  const { user, loading } = useAuth();

  console.log('🧭 Navigation state:', {
    segments,
    hasUser: !!user,
    loading,
    userEmail: user?.email
  });

  useEffect(() => {
    if (loading) {
      console.log('⏳ Auth still loading, waiting...');
      return;
    }

    // Safely handle undefined segments
    if (!segments) {
      console.warn('⚠️ Router segments not yet available');
      return;
    }

    const inAuthGroup = segments[0] === '(auth)';
    console.log('🔍 Route analysis:', {
      inAuthGroup,
      hasUser: !!user,
      currentSegments: segments
    });

    if (!user && !inAuthGroup) {
      // Redirect to the sign-in page.
      console.log('🔄 Redirecting to login (no user, not in auth group)');
      router.replace('/(auth)/login');
    } else if (user && inAuthGroup) {
      // Redirect away from the sign-in page.
      console.log('🔄 Redirecting to dashboard (user exists, in auth group)');
      router.replace('/(app)/dashboard');
    } else {
      console.log('✅ Navigation state is correct, no redirect needed');
    }
  }, [user, segments, loading]);

  useEffect(() => {
    if (!loading) {
      console.log('🎬 Hiding splash screen...');
      SplashScreen.hideAsync();
    }
  }, [loading]);

  console.log('📱 Rendering Stack with auth loading:', loading);

  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen name="(auth)" options={{ headerShown: false }} />
      <Stack.Screen name="(app)" options={{ headerShown: false }} />
    </Stack>
  );
}