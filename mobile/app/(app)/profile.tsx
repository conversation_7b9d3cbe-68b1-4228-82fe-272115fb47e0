import React, { useState } from 'react';
import { View, StyleSheet, TextInput, TouchableOpacity, ActivityIndicator, ScrollView, Alert, Platform, StatusBar } from 'react-native';
import SafeImage from '../../components/ui/SafeImage';
import { useTheme } from '../../contexts/ThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import { Text } from '../../components/ui/text';
import { Feather } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';
import { useProfile } from '../../contexts/ProfileContext';
import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import { Profile } from '../../types/supabase';
import { validateImageUrl } from '../../utils/file-helper';
import { isValidImageUrl } from '../../utils/image-validator';
import { logger } from '../../utils/logger';
// import { fixSubscriptionTier } from '../../utils/subscription-fix'; // Removed as requested

export default function ProfileScreen() {
  const { colors, isDark } = useTheme();
  const { user } = useAuth();
  const { t } = useTranslation('translation', { useSuspense: false });
  const { profile, loading: profileLoading, updateProfile, uploadAvatar, uploadBanner, refetchProfile } = useProfile();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    full_name: '',
    business_name: '',
    location: '',
  });
  const [uploadingAvatar, setUploadingAvatar] = useState(false);
  const [uploadingBanner, setUploadingBanner] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [timestamp, setTimestamp] = useState(Date.now());

  // Local state for immediate UI updates
  const [localProfile, setLocalProfile] = useState<Profile | null>(null);

  // Use local profile or context profile
  const displayProfile = localProfile || profile;

  // Keep track of the last validated URLs to prevent infinite loops
  const lastValidatedAvatarUrl = React.useRef<string | null>(null);
  const lastValidatedBannerUrl = React.useRef<string | null>(null);

  // Debug logging for image URLs and validation
  React.useEffect(() => {
    if (displayProfile) {
      console.log('Profile image URLs:', {
        avatar: displayProfile.avatar_url,
        banner: displayProfile.banner_url,
        timestamp,
        avatar_load_error: displayProfile.avatar_load_error,
        banner_load_error: displayProfile.banner_load_error
      });

      // Use the refs to track validated URLs

      // Validate image URLs - use synchronous validation first, then async if needed
      const validateImages = async () => {
        // Only validate avatar URL if it exists, has changed, and doesn't already have an error
        if (displayProfile.avatar_url &&
            displayProfile.avatar_url !== lastValidatedAvatarUrl.current &&
            !displayProfile.avatar_load_error) {

          try {
            // Update the last validated URL
            lastValidatedAvatarUrl.current = displayProfile.avatar_url;

            // First do a quick synchronous check
            const isAvatarValidSync = isValidImageUrl(displayProfile.avatar_url);

            if (!isAvatarValidSync) {

              setLocalProfile(prev => prev && !prev.avatar_load_error ? {
                ...prev,
                avatar_load_error: true
              } : prev);
            } else {
              // If sync check passes, do a more thorough async check
              const isAvatarValid = await validateImageUrl(displayProfile.avatar_url);

              if (!isAvatarValid) {

                setLocalProfile(prev => prev && !prev.avatar_load_error ? {
                  ...prev,
                  avatar_load_error: true
                } : prev);
              }
            }
          } catch (error) {
            console.error('Error validating avatar URL:', error);
          }
        } else if (!displayProfile.avatar_url && displayProfile.avatar_load_error) {
          // If URL is empty but we have an error flag, clear it
          setLocalProfile(prev => prev ? {
            ...prev,
            avatar_load_error: false
          } : prev);
        }

        // Only validate banner URL if it exists, has changed, and doesn't already have an error
        if (displayProfile.banner_url &&
            displayProfile.banner_url !== lastValidatedBannerUrl.current &&
            !displayProfile.banner_load_error) {

          try {
            // Update the last validated URL
            lastValidatedBannerUrl.current = displayProfile.banner_url;

            // First do a quick synchronous check
            const isBannerValidSync = isValidImageUrl(displayProfile.banner_url);

            if (!isBannerValidSync) {

              setLocalProfile(prev => prev && !prev.banner_load_error ? {
                ...prev,
                banner_load_error: true
              } : prev);
            } else {
              // If sync check passes, do a more thorough async check
              const isBannerValid = await validateImageUrl(displayProfile.banner_url);

              if (!isBannerValid) {

                setLocalProfile(prev => prev && !prev.banner_load_error ? {
                  ...prev,
                  banner_load_error: true
                } : prev);
              }
            }
          } catch (error) {
            console.error('Error validating banner URL:', error);
          }
        } else if (!displayProfile.banner_url && displayProfile.banner_load_error) {
          // If URL is empty but we have an error flag, clear it
          setLocalProfile(prev => prev ? {
            ...prev,
            banner_load_error: false
          } : prev);
        }
      };

      validateImages();
    }
  }, [displayProfile, timestamp]);

  // Update form data when profile changes
  React.useEffect(() => {
    if (profile) {
      setFormData({
        full_name: profile.full_name || '',
        business_name: profile.business_name || '',
        location: profile.location || '',
      });

      // Also update local profile state when context profile changes
      setLocalProfile(profile);

      // Update timestamp to force image refresh when profile changes
      setTimestamp(Date.now());
    }
  }, [profile]);

  const requestPermission = async () => {
    if (Platform.OS !== 'web') {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission required', 'Sorry, we need camera roll permissions to upload images.');
        return false;
      }
    }
    return true;
  };

  const handlePickImage = async (type: 'avatar' | 'banner') => {
    try {
      setError(null);

      // Show loading state and clear previous image
      if (type === 'avatar') {
        setUploadingAvatar(true);
        // Clear old URL to show loading state
        setLocalProfile(prev => prev ? {
          ...prev,
          avatar_url: null // Clear old URL to force fallback during upload
        } : null);
      } else {
        setUploadingBanner(true);
        // Clear old URL to show loading state
        setLocalProfile(prev => prev ? {
          ...prev,
          banner_url: null // Clear old URL to force fallback during upload
        } : null);
      }

      // Force UI update
      setTimestamp(Date.now());

      const hasPermission = await requestPermission();
      if (!hasPermission) return;

      const options = {
        allowsEditing: true,
        aspect: type === 'avatar' ? [1, 1] as [number, number] : [16, 9] as [number, number],
        quality: 0.8,
      };

      try {
        const result = await ImagePicker.launchImageLibraryAsync(options);

        if (!result.canceled && result.assets && result.assets.length > 0) {
          const selectedAsset = result.assets[0];

          try {
            // Determine if this is an avatar or banner upload
            const isAvatar = type === 'avatar';

            // Set the appropriate loading state
            if (isAvatar) {
              setUploadingAvatar(true);
            } else {
              setUploadingBanner(true);
            }

            // Log the selected asset
            logger.debug(`Selected ${type} image`, {
              uri: selectedAsset.uri,
              width: selectedAsset.width,
              height: selectedAsset.height
            });

            // Verify the file exists and is not empty
            const fileInfo = await FileSystem.getInfoAsync(selectedAsset.uri, { size: true });
            if (!fileInfo.exists) {
              throw new Error('Selected file does not exist');
            }
            if ('size' in fileInfo && fileInfo.size === 0) {
              throw new Error('Selected file is empty');
            }

            logger.debug('File info verified', {
              size: 'size' in fileInfo ? `${(fileInfo.size / 1024).toFixed(2)}KB` : 'unknown'
            });

            // Upload the image using the appropriate function
            const uploadFunction = isAvatar ? uploadAvatar : uploadBanner;
            const publicUrl = await uploadFunction(selectedAsset.uri);

            logger.debug(`${type} uploaded successfully`, { publicUrl });

            // Update local state with the new URL
            if (isAvatar) {
              setLocalProfile((prev: Profile | null) => prev ? ({
                ...prev,
                avatar_url: publicUrl,
                avatar_load_error: false // Keep in local state only
              }) : null);
            } else {
              setLocalProfile((prev: Profile | null) => prev ? ({
                ...prev,
                banner_url: publicUrl,
                banner_load_error: false // Keep in local state only
              }) : null);
            }

            // Update timestamp to force image refresh
            setTimestamp(Date.now());

            // Refresh the profile
            if (refetchProfile) {
              await refetchProfile();
            }

            // Show success message
            Alert.alert(
              'Upload Successful',
              `Your ${isAvatar ? 'profile picture' : 'banner'} has been updated.`
            );
          } catch (error: any) {
            logger.error(`Error uploading ${type}:`, error);
            setError(`${type === 'avatar' ? 'Avatar' : 'Banner'} upload failed: ${error.message}`);

            // Show error message
            Alert.alert(
              'Upload Failed',
              `Could not upload ${type === 'avatar' ? 'profile picture' : 'banner'}. Please try again.`
            );
          } finally {
            // Reset loading state
            if (type === 'avatar') {
              setUploadingAvatar(false);
            } else {
              setUploadingBanner(false);
            }
          }
        }
      } catch (pickError: any) {
        console.error('Image picker error:', pickError);
        setError('Could not open image picker');
      }
    } catch (error: any) {
      console.error(`Error in handlePickImage for ${type}:`, error);
      setError(`Image picker error: ${error.message}`);
    }
  };

  // handleFixSubscription function removed as requested

  const handleSave = async () => {
    try {
      setError(null);
      await updateProfile(formData);
      setIsEditing(false);
      // Force refresh images after profile update
      setTimestamp(Date.now());
    } catch (error: any) {
      console.error('Error updating profile:', error);
      setError(`Profile update failed: ${error.message}`);
    }
  };


  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase();
  };

  // Check if subscription is expired (more than 30 days since last update)
  const isSubscriptionExpired = (updatedAt: string, tier: string): boolean => {
    const updateDate = new Date(updatedAt);
    const now = new Date();

    // If the date is in the future and it's a premium subscription, it's not expired
    // This is likely a future expiration date for premium subscriptions
    if (updateDate > now && tier === 'premium') {

      return false;
    }

    // If the date is in the past, check if it's more than 30 days old
    const diffTime = Math.abs(now.getTime() - updateDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    // Log the expiration check

    return diffDays > 30;
  };

  // Get the effective subscription tier based on profile data
  const getEffectiveSubscriptionTier = (profile: Profile | null): 'standard' | 'premium' => {
    if (!profile || !profile.subscription_tier) {

      return 'standard';
    }

    // If the subscription tier is premium, check if it's expired
    if (profile.subscription_tier === 'premium') {
      // If we have a subscription_updated_at date, check if it's expired
      if (profile.subscription_updated_at) {
        const isExpired = isSubscriptionExpired(profile.subscription_updated_at, 'premium');

        // If not expired, return premium
        if (!isExpired) {
          return 'premium';
        } else {

          return 'standard';
        }
      }

      // If no updated_at date, assume it's valid

      return 'premium';
    }

    console.log('Current subscription tier:', profile.subscription_tier,
                'Updated at:', profile.subscription_updated_at);
    return profile.subscription_tier;
  };

  // Force refresh profile when navigating to this screen
  React.useEffect(() => {
    if (refetchProfile) {

      refetchProfile().catch(err => {
        console.error('Error refreshing profile:', err);
      });
    }

    return () => {

    };
  }, []);

  if (!user) return null;

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar
        backgroundColor={colors.surfaceVariant}
        barStyle={isDark ? "light-content" : "dark-content"}
      />
      <SafeAreaView style={{ flex: 1 }} edges={['left', 'right', 'bottom']}>
        <ScrollView
          style={styles.container}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {error && (
            <View style={styles.errorContainer}>
              <Text style={[styles.errorText, { color: colors.error }]}>{error}</Text>
            </View>
          )}

          <View style={[styles.header, { backgroundColor: colors.surfaceVariant }]}>
            <SafeImage
              uri={displayProfile?.banner_url || null}
              style={styles.bannerImage}
              resizeMode="cover"
              fallbackIcon="image"
              fallbackIconSize={32}
              isProfileImage={true}
            />
            <TouchableOpacity
              style={[styles.editBannerButton, {
                backgroundColor: 'rgba(0,0,0,0.8)',
                borderColor: colors.surface,
              }]}
              onPress={() => handlePickImage('banner')}
              disabled={uploadingBanner}
            >
              {uploadingBanner ? (
                <ActivityIndicator size="small" color={colors.surface} />
              ) : (
                <Feather name="image" size={20} color={colors.surface} />
              )}
            </TouchableOpacity>
          </View>

          <View style={[styles.avatarContainer, { borderColor: colors.surface }]}>
            {displayProfile?.avatar_url ? (
              <SafeImage
                uri={displayProfile.avatar_url}
                style={[styles.avatarImage, { borderRadius: 60 }]}
                resizeMode="cover"
                fallbackIcon="user"
                fallbackIconSize={40}
                isProfileImage={true}
              />
            ) : (
              <View style={styles.initialsContainer}>
                <Text variant="h1" style={[styles.initials, { color: colors.surface }]}>
                  {getInitials(displayProfile?.full_name || user.email || 'U')}
                </Text>
              </View>
            )}
            <TouchableOpacity
              style={[styles.editAvatarButton, {
                backgroundColor: colors.primary,
                borderColor: colors.surface,
              }]}
              onPress={() => handlePickImage('avatar')}
              disabled={uploadingAvatar}
            >
              {uploadingAvatar ? (
                <ActivityIndicator size="small" color={colors.surface} />
              ) : (
                <Feather name="edit-2" size={18} color={colors.surface} />
              )}
            </TouchableOpacity>
          </View>

          <View style={styles.content}>
            <View style={styles.headerActions}>
              <Text style={[styles.email, { color: colors.textSecondary }]}>{user.email}</Text>
            </View>

            {displayProfile?.business_name && (
              <View style={[styles.businessBadge, { backgroundColor: colors.surfaceVariant }]}>
                <Feather name="briefcase" size={16} color={colors.primary} />
                <Text style={[styles.businessText, { color: colors.primary }]}>{displayProfile.business_name}</Text>
              </View>
            )}

            {displayProfile?.location && (
              <View style={styles.locationContainer}>
                <Feather name="map-pin" size={16} color={colors.textSecondary} />
                <Text style={[styles.locationText, { color: colors.textSecondary }]}>{displayProfile.location}</Text>
              </View>
            )}

            <View style={[styles.accountInfoContainer, { backgroundColor: colors.surfaceVariant }]}>
              <Text style={[styles.accountInfoTitle, { color: colors.text }]}>{t('profile.account_info')}</Text>

              <View style={styles.accountInfoRow}>
                <Text style={[styles.accountInfoLabel, { color: colors.textSecondary }]}>{t('profile.member_since')}</Text>
                <Text style={[styles.accountInfoValue, { color: colors.text }]}>
                  {displayProfile?.created_at
                    ? new Date(displayProfile.created_at).toLocaleDateString(undefined, { year: 'numeric', month: 'long' })
                    : 'Unknown'}
                </Text>
              </View>

              <View style={styles.accountInfoRow}>
                <Text style={[styles.accountInfoLabel, { color: colors.textSecondary }]}>{t('profile.subscription')}</Text>
                <View style={styles.subscriptionContainer}>
                  <Text
                    style={[styles.accountInfoValue, {
                      color: getEffectiveSubscriptionTier(displayProfile) === 'premium'
                        ? colors.primary
                        : colors.textSecondary
                    }]}
                  >
                    {getEffectiveSubscriptionTier(displayProfile) === 'premium'
                      ? t('profile.premium_tier')
                      : t('profile.standard_tier')}
                  </Text>
                  {displayProfile?.subscription_status && (
                    <View
                      style={[styles.statusIndicator, {
                        backgroundColor: displayProfile.subscription_status === 'active' &&
                                         getEffectiveSubscriptionTier(displayProfile) === 'premium'
                          ? colors.success
                          : colors.error
                      }]}
                    />
                  )}
                </View>
              </View>

              {/* Fix Subscription Button removed as requested */}

              {displayProfile?.subscription_updated_at && (
                <View style={styles.accountInfoRow}>
                  <Text style={[styles.accountInfoLabel, { color: colors.textSecondary }]}>
                    {new Date(displayProfile.subscription_updated_at) > new Date() && displayProfile.subscription_tier !== 'premium'
                      ? t('profile.subscription_invalid')
                      : isSubscriptionExpired(displayProfile.subscription_updated_at, displayProfile.subscription_tier || 'standard')
                        ? t('profile.subscription_expired')
                        : displayProfile.subscription_tier === 'premium' && new Date(displayProfile.subscription_updated_at) > new Date()
                          ? t('profile.subscription_renewal')
                          : t('profile.subscription_updated')}
                  </Text>
                  <Text
                    style={[styles.accountInfoValue, {
                      color: new Date(displayProfile.subscription_updated_at) > new Date() && displayProfile.subscription_tier !== 'premium'
                        ? colors.error
                        : isSubscriptionExpired(displayProfile.subscription_updated_at, displayProfile.subscription_tier || 'standard')
                          ? colors.error
                          : displayProfile.subscription_tier === 'premium' && new Date(displayProfile.subscription_updated_at) > new Date()
                            ? colors.primary
                            : colors.text,
                      fontSize: 14
                    }]}
                  >
                    {new Date(displayProfile.subscription_updated_at) > new Date()
                      ? displayProfile.subscription_tier === 'premium'
                        ? `${t('profile.expires')}: ${new Date(displayProfile.subscription_updated_at).toLocaleDateString()}`
                        : `${new Date(displayProfile.subscription_updated_at).toLocaleDateString()} (${t('profile.future_date')})`
                      : new Date(displayProfile.subscription_updated_at).toLocaleDateString()}
                  </Text>
                </View>
              )}

              <View style={styles.accountInfoRow}>
                <Text style={[styles.accountInfoLabel, { color: colors.textSecondary }]}>{t('profile.account_id')}</Text>
                <Text style={[styles.accountInfoValue, { color: colors.text }]}>
                  {user?.id?.substring(0, 8) || "286b46f9"}
                </Text>
              </View>
            </View>

            <View style={styles.section}>
              <Text style={[styles.label, { color: colors.text }]}>Full Name</Text>
              <TextInput
                style={[styles.input, {
                  backgroundColor: colors.surfaceVariant,
                  color: colors.text
                }]}
                value={formData.full_name}
                onChangeText={text => setFormData(prev => ({ ...prev, full_name: text }))}
                placeholder="Enter your full name"
                placeholderTextColor={colors.textSecondary}
                editable={isEditing}
              />

              <Text style={[styles.label, { color: colors.text }]}>Business Name</Text>
              <TextInput
                style={[styles.input, {
                  backgroundColor: colors.surfaceVariant,
                  color: colors.text
                }]}
                value={formData.business_name}
                onChangeText={text => setFormData(prev => ({ ...prev, business_name: text }))}
                placeholder="Enter your business name"
                placeholderTextColor={colors.textSecondary}
                editable={isEditing}
              />

              <Text style={[styles.label, { color: colors.text }]}>Location</Text>
              <TextInput
                style={[styles.input, {
                  backgroundColor: colors.surfaceVariant,
                  color: colors.text
                }]}
                value={formData.location}
                onChangeText={text => setFormData(prev => ({ ...prev, location: text }))}
                placeholder="Enter your location"
                placeholderTextColor={colors.textSecondary}
                editable={isEditing}
              />

              <TouchableOpacity
                style={[
                  styles.saveButton,
                  { backgroundColor: colors.primary },
                  profileLoading && styles.saveButtonDisabled
                ]}
                onPress={isEditing ? handleSave : () => setIsEditing(true)}
                disabled={isEditing && profileLoading}
              >
                {isEditing && profileLoading ? (
                  <ActivityIndicator color={colors.surface} />
                ) : (
                  <Text style={[styles.saveButtonText, { color: colors.surface }]}>
                    {isEditing ? 'Save' : 'Edit'}
                  </Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 120,
  },
  header: {
    height: 200,
    backgroundColor: '#2B2B2B',
    position: 'relative',
    width: '100%',
  },
  bannerImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
    backgroundColor: '#F3F4F6',
    borderRadius: 0,
  },
  bannerPlaceholder: {
    width: '100%',
    height: '100%',
    backgroundColor: '#2B2B2B',
    justifyContent: 'center',
    alignItems: 'center',
  },
  editBannerButton: {
    position: 'absolute',
    right: 16,
    bottom: 16,
    backgroundColor: 'rgba(0,0,0,0.7)',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#fff',
    zIndex: 10,
  },
  avatarContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#855E42',
    position: 'absolute',
    left: '50%',
    transform: [{ translateX: -60 }],
    top: 140,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 4,
    borderColor: '#fff',
    overflow: 'visible',
    zIndex: 5,
  },
  avatarImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
    backgroundColor: '#F3F4F6',
    borderRadius: 60,
    overflow: 'hidden',
  },
  initialsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  initials: {
    color: '#fff',
    fontSize: 48,
  },
  editAvatarButton: {
    position: 'absolute',
    right: -4,
    bottom: -4,
    backgroundColor: '#007AFF',
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#fff',
    zIndex: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  content: {
    flex: 1,
    paddingTop: 80,
    paddingHorizontal: 16,
  },
  email: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 24,
    width: '100%',
  },
  businessBadge: {
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
    alignSelf: 'center',
    marginBottom: 24,
    flexDirection: 'row',
    alignItems: 'center',
  },
  businessText: {
    color: '#007AFF',
    marginLeft: 4,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  locationText: {
    color: '#6B7280',
    marginLeft: 4,
  },
  accountInfoContainer: {
    marginTop: 24,
    borderRadius: 12,
    marginHorizontal: 8,
    padding: 16,
  },
  accountInfoTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 16,
  },
  accountInfoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
  },
  accountInfoLabel: {
    fontSize: 16,
  },
  accountInfoValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  subscriptionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginLeft: 8,
  },
  section: {
    marginTop: 32,
    marginBottom: 32,
  },
  label: {
    fontSize: 16,
    color: '#111827',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    color: '#111827',
    marginBottom: 16,
  },
  saveButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 16,
  },
  saveButtonDisabled: {
    opacity: 0.7,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  errorContainer: {
    padding: 16,
    backgroundColor: '#FFE5E5',
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    color: '#D32F2F',
    fontSize: 14,
  },
  headerActions: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    marginBottom: 8,
  },
  headerButtonsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  refreshButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
    marginRight: 8,
  },
  fixSubscriptionButton: {
    marginTop: 16,
    marginBottom: 8,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  fixSubscriptionButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});