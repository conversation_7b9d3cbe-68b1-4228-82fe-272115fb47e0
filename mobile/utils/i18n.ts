import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import AsyncStorage from '@react-native-async-storage/async-storage';
// import Backend from 'i18next-http-backend'; // Uncomment when using GeneralTranslation

// Removed dependency on expo-localization to avoid native module errors

// Embedded translations (React Native handles these better than JSON imports)
const enTranslation = {
  "common": {
    "user": "User",
    "loading": "Loading...",
    "save": "Save",
    "cancel": "Cancel",
    "error": "Error",
    "success": "Success",
    "back": "Back",
    "next": "Next",
    "done": "Done"
  },
  "analysis": {
    "createNew": "Create New Analysis",
    "title": "Analysis",
    "sentiment": "Sentiment Analysis",
    "pricing": "Pricing Analysis",
    "market": "Market Analysis",
    "growth": "Growth Analysis",
    "noAnalysis": "No analysis available",
    "loading": "Analyzing...",
    "complete": "Analysis Complete",
    "error": "Analysis Failed"
  },
  "dashboard": {
    "title": "Dashboard",
    "welcome": "Welcome",
    "recentAnalysis": "Recent Analysis",
    "quickStats": "Quick Stats"
  },
  "profile": {
    "title": "Profile",
    "settings": "Settings",
    "logout": "Logout",
    "editProfile": "Edit Profile"
  },
  "auth": {
    "login": "Login",
    "signup": "Sign Up",
    "email": "Email",
    "password": "Password",
    "forgotPassword": "Forgot Password?"
  }
};

const esTranslation = {
  "common": {
    "user": "Usuario",
    "loading": "Cargando...",
    "save": "Guardar",
    "cancel": "Cancelar",
    "error": "Error",
    "success": "Éxito",
    "back": "Atrás",
    "next": "Siguiente",
    "done": "Hecho"
  },
  "analysis": {
    "createNew": "Crear Nuevo Análisis",
    "title": "Análisis",
    "sentiment": "Análisis de Sentimientos",
    "pricing": "Análisis de Precios",
    "market": "Análisis de Mercado",
    "growth": "Análisis de Crecimiento",
    "noAnalysis": "No hay análisis disponible",
    "loading": "Analizando...",
    "complete": "Análisis Completo",
    "error": "Análisis Fallido"
  },
  "dashboard": {
    "title": "Panel",
    "welcome": "Bienvenido",
    "recentAnalysis": "Análisis Recientes",
    "quickStats": "Estadísticas Rápidas"
  },
  "profile": {
    "title": "Perfil",
    "settings": "Configuraciones",
    "logout": "Cerrar Sesión",
    "editProfile": "Editar Perfil"
  },
  "auth": {
    "login": "Iniciar Sesión",
    "signup": "Registrarse",
    "email": "Correo Electrónico",
    "password": "Contraseña",
    "forgotPassword": "¿Olvidaste tu contraseña?"
  }
};

// Available languages
export const LANGUAGES = {
  en: { name: 'English', nativeName: 'English' },
  es: { name: 'Spanish', nativeName: 'Español' },
};

// Initialize i18n with local translations for now
i18n
  // .use(Backend) // Commented out until we have a valid GeneralTranslation
  .use(initReactI18next)
  .init({
    compatibilityJSON: 'v3' as any, // Type assertion to fix TypeScript error
    // Backend configuration for GeneralTranslation
    // Commented out until we have a valid GeneralTranslation account
    // backend: {
    //   loadPath: 'https://cdn.generaltranslation.com/sme-analytica/locales/{{lng}}/{{ns}}.json',
    // },
    // Fallback resources in case network is unavailable
    resources: {
      en: { translation: enTranslation },
      es: { translation: esTranslation }
    },
    lng: 'en', // Default language
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false
    },
    react: {
      useSuspense: false, // We want to handle loading states ourselves
      bindI18n: 'languageChanged loaded', // React to language changes
      bindI18nStore: 'added removed', // React to resource changes
    },
    returnNull: false, // Return key instead of null when translation is missing
    returnEmptyString: false, // Return key instead of empty string when translation is missing
    parseMissingKeyHandler: (key: string) => {

      // Extract the last part of the key for better readability
      const parts = key.split('.');
      const lastPart = parts[parts.length - 1];
      // Format the key for better readability with proper capitalization
      return lastPart
        .replace(/([A-Z])/g, ' $1') // Add space before capital letters
        .replace(/_/g, ' ') // Replace underscores with spaces
        .replace(/^\w/, c => c.toUpperCase()) // Capitalize first letter
        .trim();
    },
    debug: false // Disable debug mode to reduce noise
  });

// Helper function to change language
export const changeLanguage = async (language: string) => {
  try {
    // Store the language preference
    await AsyncStorage.setItem('user-language', language);
    // Change the language in i18n
    await i18n.changeLanguage(language);
  } catch (error) {
    console.error('[i18n] Error changing language:', error);
    throw error;
  }
};

// Helper function to get current language
export const getCurrentLanguage = async () => {
  try {
    const savedLanguage = await AsyncStorage.getItem('user-language');
    return savedLanguage || 'en';
  } catch (error) {
    console.error('[i18n] Error getting current language:', error);
    return 'en';
  }
};

// Initialize with saved language
export const initLanguage = async () => {
  try {
    const savedLanguage = await AsyncStorage.getItem('user-language');
    if (savedLanguage && LANGUAGES[savedLanguage as keyof typeof LANGUAGES]) {
      await i18n.changeLanguage(savedLanguage);
    } else {
      // Default to English
      const defaultLanguage = 'en';
      await i18n.changeLanguage(defaultLanguage);
      await AsyncStorage.setItem('user-language', defaultLanguage);
    }
  } catch (error) {
    console.error('[i18n] Error initializing language:', error);
  }
};

// Export for type safety
export type TranslationKey = keyof typeof enTranslation;

export default i18n;
