import { Platform } from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import { supabase } from './supabase';
import { logger } from './logger';
import { debugLogger } from './debug-logger';
import { prepareFileForUpload, generateUniqueFilename } from './file-helper';
import { shouldUseMockUploads, mockImageUpload } from './mock-upload';

// Import base64 polyfill to ensure atob and btoa are available
import './base64-polyfill';

/**
 * Handles image picker and uploads for various image types (avatar, banner).
 * Includes robust error handling and fallbacks.
 */
export const pickAndUploadImage = async (options: {
  type: 'avatar' | 'banner';
  aspect?: [number, number];
  quality?: number;
  onSuccess?: (url: string) => void;
  onError?: (error: Error) => void;
  onStart?: () => void;
  onComplete?: () => void;
}) => {
  const trace = debugLogger.startTrace(`pickAndUploadImage-${options.type}`);
  
  const {
    type,
    aspect = type === 'avatar' ? [1, 1] : [16, 9],
    quality = 0.7,
    onSuccess,
    onError,
    onStart,
    onComplete,
  } = options;

  try {
    onStart?.();
    trace.step('Started image picker process');

    // Check permissions
    trace.step('Requesting permissions');
    const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (!permissionResult.granted) {
      trace.error('Permission denied');
      throw new Error('Permission to access media library was denied');
    }

    // Pick the image - using the non-deprecated MediaType
    trace.step('Launching image picker', { aspect, quality });
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: 'images', // Use string instead of enum to avoid deprecation warning
      allowsEditing: true,
      aspect,
      quality,
      exif: false,
    });

    if (result.canceled || !result.assets || result.assets.length === 0) {
      trace.error('Image selection canceled');
      throw new Error('Image selection was canceled');
    }

    const uri = result.assets[0].uri;
    trace.step('Image selected', { uri });

    // Check if we should use mock uploads
    const useMock = shouldUseMockUploads();
    trace.step('Upload method determination', { useMock });

    // Upload to Supabase or use mock upload
    let publicUrl;
    if (useMock) {
      trace.step('Using mock upload (development mode)');
      publicUrl = await mockImageUpload(uri, type);
    } else {
      trace.step('Using real Supabase upload');
      publicUrl = await uploadImageToSupabase({
        uri,
        type,
      });
    }

    trace.step('Upload completed successfully');
    logger.debug(`${type} uploaded successfully:`, { publicUrl });
    onSuccess?.(publicUrl);
    trace.end({ success: true, publicUrl });
    return publicUrl;
  } catch (error) {
    trace.error(error);
    logger.error(`Error uploading ${type}:`, error);
    
    if (error instanceof Error) {
      onError?.(error);
    } else {
      onError?.(new Error('An unknown error occurred'));
    }
    throw error;
  } finally {
    onComplete?.();
  }
};

/**
 * Uploads an image to Supabase storage and updates the user profile.
 */
export const uploadImageToSupabase = async (options: {
  uri: string;
  type: 'avatar' | 'banner';
}): Promise<string> => {
  const trace = debugLogger.startTrace(`uploadImageToSupabase-${options.type}`);
  const { uri, type } = options;
  const bucketName = type === 'avatar' ? 'avatars' : 'banners';
  const fieldName = type === 'avatar' ? 'avatar_url' : 'banner_url';

  trace.step('Starting upload process', { bucketName, fieldName, uri });

  try {
    // Get current user
    trace.step('Getting authenticated user');
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      trace.error('No authenticated user found');
      throw new Error('No authenticated user found');
    }
    trace.step('User authenticated', { userId: user.id });

    // Prepare the file for upload
    trace.step('Preparing file');
    const { fileData, contentType } = await prepareFileForUpload(uri);
    trace.step('File prepared', { contentType, isBase64: typeof fileData === 'string' });
    
    // Generate a unique filename
    const filename = generateUniqueFilename(user.id, uri);
    trace.step('Filename generated', { filename });

    // Upload to Supabase
    trace.step('Uploading to Supabase', { 
      bucket: bucketName,
      filename,
      platform: Platform.OS,
      dataType: typeof fileData
    });
    
    // Different upload handling for web vs native
    let uploadResult;
    
    try {
      if (Platform.OS === 'web') {
        // For web: upload as blob
        uploadResult = await supabase.storage
          .from(bucketName)
          .upload(filename, fileData as Blob, {
            contentType,
            upsert: true
          });
      } else {
        // For native: use the base64 upload method
        uploadResult = await supabase.storage
          .from(bucketName)
          .upload(filename, fileData as string, {
            contentType,
            upsert: true
          });
      }
      
      trace.step('Upload API call completed');
    } catch (uploadError) {
      trace.error(uploadError);
      throw uploadError;
    }
    
    const { data, error } = uploadResult;

    if (error) {
      trace.error({ storageError: error });
      throw new Error(`Failed to upload image: ${error.message}`);
    }

    trace.step('File uploaded successfully', { data });
    
    // Get public URL
    trace.step('Getting public URL');
    const { data: publicUrlData } = supabase.storage
      .from(bucketName)
      .getPublicUrl(filename);

    if (!publicUrlData || !publicUrlData.publicUrl) {
      const error = new Error('Failed to get public URL');
      trace.error(error);
      throw error;
    }

    const publicUrl = publicUrlData.publicUrl;
    trace.step('Got public URL', { publicUrl });

    // Update user profile
    trace.step('Updating user profile');
    const { error: updateError } = await supabase
      .from('users')
      .update({ [fieldName]: publicUrl })
      .eq('id', user.id);

    if (updateError) {
      trace.error({ profileUpdateError: updateError });
      throw new Error(`Failed to update profile: ${updateError.message}`);
    }

    trace.step('Profile updated successfully');
    trace.end({ success: true, publicUrl });
    return publicUrl;
  } catch (error) {
    trace.error(error);
    throw error;
  }
};
