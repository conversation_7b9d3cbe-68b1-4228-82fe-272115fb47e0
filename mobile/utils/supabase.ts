import 'react-native-url-polyfill/auto';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { createClient } from '@supabase/supabase-js';
import { Database } from '../types/supabase';
import Constants from 'expo-constants';
import { logger } from './logger';
import { Alert, Platform } from 'react-native';

// Debug function to log environment state
const logEnvironmentState = () => {
  const envState = {
    platform: Platform.OS,
    version: Platform.Version,
    isDev: __DEV__,
    buildProfile: process.env.EAS_BUILD_PROFILE || 'development',
    nodeEnv: process.env.NODE_ENV,
    constants: {
      appOwnership: Constants.appOwnership,
      installationId: Constants.installationId,
      sessionId: Constants.sessionId,
      expoVersion: Constants.expoVersion,
      buildProfile: process.env.EAS_BUILD_PROFILE || 'development',
      releaseChannel: Constants.expoConfig?.extra?.releaseChannel || 'development'
    },
    expoConfig: {
      extra: Constants.expoConfig?.extra ? {
        supabaseUrlSet: !!Constants.expoConfig?.extra?.supabaseUrl,
        supabaseAnonKeySet: !!Constants.expoConfig?.extra?.supabaseAnonKey,
        apiUrlSet: !!Constants.expoConfig?.extra?.apiUrl,
        revenueCatKeySet: !!Constants.expoConfig?.extra?.revenueCatKey,
        cloudinaryPresetSet: !!Constants.expoConfig?.extra?.cloudinaryPreset,
        cloudinaryCloudNameSet: !!Constants.expoConfig?.extra?.cloudinaryCloudName
      } : 'not set'
    }
  };

  console.log('🔍 Environment State:', JSON.stringify(envState, null, 2));
  return envState;
};

class SupabaseClientSingleton {
  private static instance: ReturnType<typeof createClient<Database>> | null = null;
  private static isInitializing = false;
  private static initializationError: Error | null = null;

  static getInstance(): ReturnType<typeof createClient<Database>> {
    if (this.isInitializing) {
      throw new Error('Supabase client is still initializing');
    }

    if (this.instance) {
      return this.instance;
    }

    this.isInitializing = true;

    try {
      // Log environment state before initialization
      const envState = logEnvironmentState();
      
      // Get configuration from expo config extra
      const supabaseUrl = Constants.expoConfig?.extra?.supabaseUrl;
      const supabaseAnonKey = Constants.expoConfig?.extra?.supabaseAnonKey;

      // Enhanced configuration validation
      const configValidation = {
        supabaseUrl: {
          exists: !!supabaseUrl,
          length: supabaseUrl?.length || 0,
          preview: supabaseUrl ? `${supabaseUrl.substring(0, 15)}...` : 'undefined'
        },
        supabaseAnonKey: {
          exists: !!supabaseAnonKey,
          length: supabaseAnonKey?.length || 0,
          isValid: supabaseAnonKey?.startsWith('eyJ') || false
        },
        buildInfo: {
          profile: process.env.EAS_BUILD_PROFILE || 'development',
          nodeEnv: process.env.NODE_ENV,
          isDev: __DEV__,
          platform: Platform.OS,
          expoConstants: {
            appOwnership: Constants.appOwnership,
            expoVersion: Constants.expoVersion
          }
        }
      };

      console.log('🔍 Supabase Configuration Validation:', JSON.stringify(configValidation, null, 2));

      if (!supabaseUrl || !supabaseAnonKey) {
        const configError = new Error('Invalid Supabase configuration');
        configError.name = 'SupabaseConfigError';
        Object.assign(configError, {
          validation: configValidation,
          expoConfig: {
            hasExtra: !!Constants.expoConfig?.extra,
            extraKeys: Constants.expoConfig?.extra ? Object.keys(Constants.expoConfig.extra) : []
          }
        });
        
        // Enhanced error logging
        console.error('❌ Supabase Configuration Error:', {
          error: configError,
          validation: configValidation,
          buildProfile: process.env.EAS_BUILD_PROFILE,
          isDev: __DEV__
        });
        
        // Show detailed alert in development
        if (__DEV__) {
          Alert.alert(
            'Supabase Configuration Error',
            `Missing configuration:\nURL: ${configValidation.supabaseUrl.exists ? 'Present' : 'Missing'}\nKey: ${configValidation.supabaseAnonKey.exists ? 'Present' : 'Missing'}\n\nBuild Profile: ${configValidation.buildInfo.profile}`
          );
        }
        
        throw configError;
      }

      // Validate URL format
      try {
        new URL(supabaseUrl);
      } catch (urlError) {
        console.error('❌ Invalid Supabase URL format:', {
          urlPreview: configValidation.supabaseUrl.preview,
          error: urlError
        });
        throw new Error('Invalid Supabase URL format');
      }

      // Validate API key format
      if (!supabaseAnonKey.startsWith('eyJ')) {
        console.error('❌ Invalid Supabase API key format:', {
          keyLength: configValidation.supabaseAnonKey.length,
          keyStart: supabaseAnonKey.substring(0, 3)
        });
        throw new Error('Invalid Supabase API key format');
      }

      const options = {
        auth: {
          storage: AsyncStorage,
          autoRefreshToken: true,
          persistSession: true,
          detectSessionInUrl: false,
        },
        realtime: {
          params: {
            eventsPerSecond: 10
          }
        },
        logger: {
          debug: (msg: string) => console.log(`[Supabase Debug] ${msg}`),
          info: (msg: string) => console.log(`[Supabase Info] ${msg}`),
          warn: (msg: string) => console.warn(`[Supabase Warning] ${msg}`),
          error: (msg: string) => console.error(`[Supabase Error] ${msg}`),
        },
      };

      console.log('🚀 Initializing Supabase client...');
      this.instance = createClient<Database>(supabaseUrl, supabaseAnonKey, options);

      // Test the instance immediately
      const auth = this.instance.auth;
      if (!auth) {
        throw new Error('Supabase client created but auth is not available');
      }

      console.log('✅ Supabase client initialized successfully');
      return this.instance;
    } catch (error) {
      const err = error as Error;
      this.initializationError = err;
      
      console.error('❌ Supabase Initialization Error:', {
        name: err.name,
        message: err.message,
        stack: err.stack,
        buildProfile: process.env.EAS_BUILD_PROFILE || 'development',
        platform: Platform.OS,
        isDev: __DEV__
      });
      
      // In development, show an alert with the error
      if (__DEV__) {
        Alert.alert(
          'Supabase Initialization Error',
          `${err.message}\n\nCheck console for details.`
        );
      }
      
      throw err;
    } finally {
      this.isInitializing = false;
    }
  }

  // Helper methods for profile management
  static async updateProfile(userId: string, updates: {
    full_name?: string;
    avatar_url?: string;
    banner_url?: string;
    business_name?: string;
  }) {
    const client = this.getInstance();
    logger.debug('Updating profile:', { userId, updates });

    try {
      const { data, error } = await client
        .from('users')
        .update(updates)
        .eq('id', userId)
        .select()
        .single();

      if (error) {
        logger.error('Error updating profile:', error);
        throw error;
      }

      logger.debug('Profile updated successfully:', data);
      return data;
    } catch (error) {
      logger.error('Error in updateProfile:', error);
      throw error;
    }
  }

  static async uploadAvatar(userId: string, uri: string): Promise<string> {
    const client = this.getInstance();
    logger.debug('Uploading avatar:', { userId, uri: uri.substring(0, 50) + '...' });

    try {
      // Log the URI we're uploading
      logger.debug('Fetching image for upload', { uri });

      // Determine content type based on file extension
      let contentType = 'image/png'; // Default
      if (uri.toLowerCase().endsWith('.jpg') || uri.toLowerCase().endsWith('.jpeg')) {
        contentType = 'image/jpeg';
      } else if (uri.toLowerCase().endsWith('.gif')) {
        contentType = 'image/gif';
      } else if (uri.toLowerCase().endsWith('.webp')) {
        contentType = 'image/webp';
      }

      // Log the URI we're fetching
      logger.debug('Fetching image for upload using fetch API', { uri });

      // Use fetch to get the blob directly - this works better in React Native
      let blob;
      try {
        // Fetch the image and get the blob
        const response = await fetch(uri);
        blob = await response.blob();

        logger.debug('Successfully fetched blob', {
          blobSize: blob.size,
          blobType: blob.type || contentType
        });

        // If the blob doesn't have a type, set it explicitly
        if (!blob.type) {
          // Create a new blob with the correct content type
          blob = blob.slice(0, blob.size, contentType);
          logger.debug('Created new blob with explicit content type', { contentType });
        }
      } catch (error) {
        const fetchError = error instanceof Error ? error : new Error(String(error));
        logger.error('Error fetching blob:', fetchError);
        console.error('Error fetching blob:', fetchError);
        throw new Error(`Failed to fetch image: ${fetchError.message}`);
      }

      // Validate blob is not empty
      if (!blob || blob.size === 0) {
        logger.error('Created blob is empty');
        throw new Error('Created blob is empty. Cannot upload an empty image.');
      }

      // Log blob details
      logger.debug('Blob created for upload', {
        size: blob.size,
        type: blob.type || 'image/jpeg' // Default to image/jpeg if type is empty
      });

      // Dynamically determine the file extension from the content type
      // Handle both 'jpeg' and 'jpg' cases
      let ext = contentType.split('/')[1] || 'png';
      if (ext === 'jpeg') ext = 'jpg'; // Normalize jpeg to jpg for file extension
      const timestamp = Date.now();
      const randomId = Math.random().toString(36).substring(2, 10);
      const filename = `${userId}/avatar/${timestamp}-${randomId}.${ext}`;

      // Log detailed upload information
      logger.debug('Uploading to path:', {
        bucket: 'avatars',
        filename,
        fullPath: `${userId}/avatar/${timestamp}-${randomId}.${ext}`,
        contentType
      });

      // Attempt to upload the file
      const { error } = await client
        .storage
        .from('avatars')
        .upload(filename, blob, {
          contentType, // Use the detected content type
          upsert: true,
        });

      if (error) {
        logger.error('Error uploading avatar:', error);
        console.error('Upload error:', error);
        throw error;
      }

      // Log success
      logger.info('Upload SUCCESS!');

      // Get the public URL - make sure we use the correct relative path
      // The path should be relative to the bucket root (no 'avatars/' prefix)
      const relativePath = filename; // filename is already relative to bucket

      const { data: publicUrlData } = client
        .storage
        .from('avatars')
        .getPublicUrl(relativePath);

      logger.debug('Public URL result:', {
        relativePath,
        publicUrl: publicUrlData?.publicUrl
      });

      // Also create a signed URL for verification
      try {
        // Use the same relative path for signed URL
        const { data: signed, error: signedError } = await client
          .storage
          .from('avatars')
          .createSignedUrl(relativePath, 3600); // 1 hour expiry

        if (signedError) {
          logger.error('Error creating signed URL:', signedError);
        } else {
          logger.debug('Signed URL created:', signed?.signedUrl);

        }
      } catch (signedUrlError) {
        logger.error('Exception creating signed URL:', signedUrlError);
      }

      // We already got the public URL above, just return it with cache busting
      const publicUrl = publicUrlData?.publicUrl;

      // Add cache busting parameter to prevent stale images
      const cacheBuster = `ts=${Date.now()}`;
      const publicUrlWithCacheBuster = `${publicUrl}${publicUrl?.includes('?') ? '&' : '?'}${cacheBuster}`;

      // Log the final URL we're returning
      logger.debug('Returning public URL with cache buster:', publicUrlWithCacheBuster);

      return publicUrlWithCacheBuster;
    } catch (error) {
      logger.error('Error in uploadAvatar:', error);
      throw error;
    }
  }

  static async uploadBanner(userId: string, uri: string): Promise<string> {
    const client = this.getInstance();
    logger.debug('Uploading banner:', { userId, uri: uri.substring(0, 50) + '...' });

    try {
      // Log the URI we're uploading
      logger.debug('Fetching image for upload', { uri });

      // Determine content type based on file extension
      let contentType = 'image/png'; // Default
      if (uri.toLowerCase().endsWith('.jpg') || uri.toLowerCase().endsWith('.jpeg')) {
        contentType = 'image/jpeg';
      } else if (uri.toLowerCase().endsWith('.gif')) {
        contentType = 'image/gif';
      } else if (uri.toLowerCase().endsWith('.webp')) {
        contentType = 'image/webp';
      }

      // Log the URI we're fetching
      logger.debug('Fetching image for upload using fetch API', { uri });

      // Use fetch to get the blob directly - this works better in React Native
      let blob;
      try {
        // Fetch the image and get the blob
        const response = await fetch(uri);
        blob = await response.blob();

        logger.debug('Successfully fetched blob', {
          blobSize: blob.size,
          blobType: blob.type || contentType
        });

        // If the blob doesn't have a type, set it explicitly
        if (!blob.type) {
          // Create a new blob with the correct content type
          blob = blob.slice(0, blob.size, contentType);
          logger.debug('Created new blob with explicit content type', { contentType });
        }
      } catch (error) {
        const fetchError = error instanceof Error ? error : new Error(String(error));
        logger.error('Error fetching blob:', fetchError);
        console.error('Error fetching blob:', fetchError);
        throw new Error(`Failed to fetch image: ${fetchError.message}`);
      }

      // Validate blob is not empty
      if (!blob || blob.size === 0) {
        logger.error('Created blob is empty');
        throw new Error('Created blob is empty. Cannot upload an empty image.');
      }

      // Log blob details
      logger.debug('Blob created for upload', {
        size: blob.size,
        type: blob.type || 'image/jpeg' // Default to image/jpeg if type is empty
      });

      // Dynamically determine the file extension from the content type
      // Handle both 'jpeg' and 'jpg' cases
      let ext = contentType.split('/')[1] || 'png';
      if (ext === 'jpeg') ext = 'jpg'; // Normalize jpeg to jpg for file extension
      const timestamp = Date.now();
      const randomId = Math.random().toString(36).substring(2, 10);
      const filename = `${userId}/banner/${timestamp}-${randomId}.${ext}`;

      // Log detailed upload information
      logger.debug('Uploading to path:', {
        bucket: 'banners',
        filename,
        fullPath: `${userId}/banner/${timestamp}-${randomId}.${ext}`,
        contentType
      });

      // Attempt to upload the file
      const { error } = await client
        .storage
        .from('banners')
        .upload(filename, blob, {
          contentType, // Use the detected content type
          upsert: true,
        });

      if (error) {
        logger.error('Error uploading banner:', error);
        console.error('Upload error:', error);
        throw error;
      }

      // Log success
      logger.info('Upload SUCCESS!');

      // Get the public URL - make sure we use the correct relative path
      // The path should be relative to the bucket root (no 'banners/' prefix)
      const relativePath = filename; // filename is already relative to bucket

      const { data: publicUrlData } = client
        .storage
        .from('banners')
        .getPublicUrl(relativePath);

      logger.debug('Public URL result:', {
        relativePath,
        publicUrl: publicUrlData?.publicUrl
      });

      // Also create a signed URL for verification
      try {
        // Use the same relative path for signed URL
        const { data: signed, error: signedError } = await client
          .storage
          .from('banners')
          .createSignedUrl(relativePath, 3600); // 1 hour expiry

        if (signedError) {
          logger.error('Error creating signed URL:', signedError);
        } else {
          logger.debug('Signed URL created:', signed?.signedUrl);

        }
      } catch (signedUrlError) {
        logger.error('Exception creating signed URL:', signedUrlError);
      }

      // We already got the public URL above, just return it with cache busting
      const publicUrl = publicUrlData?.publicUrl;

      // Add cache busting parameter to prevent stale images
      const cacheBuster = `ts=${Date.now()}`;
      const publicUrlWithCacheBuster = `${publicUrl}${publicUrl?.includes('?') ? '&' : '?'}${cacheBuster}`;

      // Log the final URL we're returning
      logger.debug('Returning public URL with cache buster:', publicUrlWithCacheBuster);

      return publicUrlWithCacheBuster;
    } catch (error) {
      logger.error('Error in uploadBanner:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const supabase = SupabaseClientSingleton.getInstance();

// Add helper methods to the exported object
export const updateProfile = SupabaseClientSingleton.updateProfile.bind(SupabaseClientSingleton);
export const uploadAvatar = SupabaseClientSingleton.uploadAvatar.bind(SupabaseClientSingleton);
export const uploadBanner = SupabaseClientSingleton.uploadBanner.bind(SupabaseClientSingleton);
