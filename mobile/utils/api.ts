import { Platform } from 'react-native';
import { supabase } from './supabase';
import Constants from 'expo-constants';
import { logger } from './logger';
import {
  AnalysisType as FrontendAnalysisType,
  AnalysisRequest,
  AnalysisResponse,
  BusinessData,
  AnalysisResult,
  PricingConstraints
} from '../types/analysis';

// Define the missing types
interface ApiError {
  message: string;
  status: number;
}

interface HealthCheckResponse {
  status: string;
  timestamp: string;
  version: string;
  environment?: string;  // Optional since it might not always be present
}

interface ApiResponse<T> {
  data: T | null;
  error: ApiError | null;
}

// Map frontend analysis types to API endpoints
const ANALYSIS_ENDPOINTS: Record<FrontendAnalysisType, string> = {
  pricing: 'pricing',
  dynamic_pricing: 'dynamic-pricing',
  market: 'market_analysis',
  sentiment: 'sentiment_analysis',
  growth: 'growth_strategy',
  competitor: 'competitor_analysis',
  sales_forecasting: 'forecast'
};

const getApiUrl = () => {
  // First try process.env
  const envUrl = process.env.EXPO_PUBLIC_API_URL;
  if (envUrl) {
    return envUrl;
  }

  // Then try Constants
  const constantsUrl = Constants.expoConfig?.extra?.apiUrl;
  if (constantsUrl) {
    return constantsUrl;
  }

  // Finally, use fallbacks
  return __DEV__ 
    ? 'http://localhost:8000'
    : 'https://api.smeanalytica.dev';
};

const API_CONFIG = {
  baseUrl: getApiUrl()
};

class ApiClient {
  private baseUrl: string;
  private lastConnectionTest: number = 0;
  private readonly CONNECTION_TEST_INTERVAL = 5 * 60 * 1000; // 5 minutes

  constructor() {
    const apiUrl = getApiUrl();
    if (!apiUrl) {
      throw new Error('API URL not configured');
    }
    this.baseUrl = apiUrl.replace(/\/$/, '');

    // Log initial configuration at INFO level
    logger.info('API initialized', {
      baseUrl: this.baseUrl,
      dev: __DEV__,
      platform: Platform.OS,
      envApiUrl: process.env.EXPO_PUBLIC_API_URL,
      constantsApiUrl: Constants.expoConfig?.extra?.apiUrl
    });
  }

  private async checkConnection() {
    const now = Date.now();
    if (now - this.lastConnectionTest < this.CONNECTION_TEST_INTERVAL) {
      return;
    }

    try {
      // Log the full configuration state
      logger.debug('API Configuration State:', {
        baseUrl: this.baseUrl,
        isDev: __DEV__,
        platform: Platform.OS,
        expoPublicApiUrl: process.env.EXPO_PUBLIC_API_URL,
        constants: {
          manifest: Constants.manifest,
          expoConfig: Constants.expoConfig
        }
      });

      logger.debug('Attempting health check', {
        url: `${this.baseUrl}/public/health`,
        timestamp: new Date().toISOString()
      });

      // Add a longer timeout for development environments
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), __DEV__ ? 10000 : 5000);

      const response = await fetch(`${this.baseUrl}/public/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'User-Agent': `Analytica-Mobile/${Constants.expoConfig?.version || 'unknown'}`
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        logger.error('Health check response not OK', {
          status: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries(response.headers.entries()),
          responseText: errorText
        });

        // In DEV mode, don't throw so the app can still function
        if (__DEV__) {
          logger.warn('Continuing despite health check failure (DEV mode)');
          this.lastConnectionTest = now;
          return { status: 'degraded', timestamp: new Date().toISOString(), version: 'unknown' };
        }

        throw new Error(`Health check failed with status: ${response.status}, response: ${errorText}`);
      }

      const healthCheck: HealthCheckResponse = await response.json();
      this.lastConnectionTest = now;

      // Log success at INFO level for better visibility
      logger.info('[HealthCheck] Success', {
        status: healthCheck.status,
        version: healthCheck.version,
        environment: healthCheck.environment,
        timestamp: healthCheck.timestamp || new Date().toISOString(),
        responseTime: Date.now() - now
      });

      return healthCheck;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error('API connection test failed', {
        error: errorMessage,
        timestamp: new Date().toISOString(),
        baseUrl: this.baseUrl,
        stack: error instanceof Error ? error.stack : undefined
      });

      // Enhanced network error debugging
      if (error instanceof TypeError && error.message === 'Network request failed') {
        logger.error('Network error details', {
          message: 'Could not connect to API server. Please check:',
          checks: [
            'Server is running and accessible',
            'API URL is correctly configured',
            'Network connection is stable',
            'No firewall/security blocking access'
          ],
          url: `${this.baseUrl}/public/health`,
          platform: Platform.OS,
          dev: __DEV__,
          networkState: {
            baseUrl: this.baseUrl,
            apiUrl: process.env.EXPO_PUBLIC_API_URL,
            isDev: __DEV__
          }
        });
      }

      // In DEV mode, allow the app to continue functioning with a degraded status
      if (__DEV__) {
        logger.warn('Continuing despite health check failure (DEV mode)', { error: errorMessage });
        this.lastConnectionTest = now;
        return { status: 'degraded', timestamp: new Date().toISOString(), version: 'unknown' };
      }

      throw error;
    }
  }

  private async getAuthHeader() {
    try {
      logger.debug('[API] Getting auth header');
      const { data: { session } } = await supabase.auth.getSession();

      if (!session?.access_token) {
        logger.debug('[API] No active session, attempting to refresh');
        const { data: { session: refreshedSession }, error: refreshError } = await supabase.auth.refreshSession();
        if (refreshError || !refreshedSession) {
          logger.error('[API] Authentication failed', {
            error: refreshError?.message || 'Session refresh failed'
          });
          throw new Error('Authentication required. Please log in again.');
        }

        const decodedToken = this.parseJwt(refreshedSession.access_token);
        logger.debug('[API] Using refreshed auth token', {
          tokenLength: refreshedSession.access_token.length,
          tokenPrefix: refreshedSession.access_token.substring(0, 10) + '...',
          decodedClaims: {
            aud: decodedToken?.aud,
            iss: decodedToken?.iss,
            exp: decodedToken?.exp,
          }
        });

        return {
          'Authorization': `Bearer ${refreshedSession.access_token}`,
          'Content-Type': 'application/json',
        };
      }

      if (this.isTokenExpired(session.access_token)) {
        logger.debug('[API] Token expired, refreshing session');
        const { data: { session: refreshedSession }, error: refreshError } = await supabase.auth.refreshSession();
        if (refreshError || !refreshedSession) {
          logger.error('[API] Token refresh failed', {
            error: refreshError?.message || 'Session refresh failed'
          });
          throw new Error('Session expired. Please log in again.');
        }

        const decodedToken = this.parseJwt(refreshedSession.access_token);
        logger.debug('[API] Using refreshed auth token after expiration', {
          tokenLength: refreshedSession.access_token.length,
          tokenPrefix: refreshedSession.access_token.substring(0, 10) + '...',
          decodedClaims: {
            aud: decodedToken?.aud,
            iss: decodedToken?.iss,
            exp: decodedToken?.exp,
          }
        });

        return {
          'Authorization': `Bearer ${refreshedSession.access_token}`,
          'Content-Type': 'application/json',
        };
      }

      const decodedToken = this.parseJwt(session.access_token);
      logger.debug('[API] Using existing auth token', {
        tokenLength: session.access_token.length,
        tokenPrefix: session.access_token.substring(0, 10) + '...',
        decodedClaims: {
          aud: decodedToken?.aud,
          iss: decodedToken?.iss,
          exp: decodedToken?.exp,
        }
      });
      return {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json',
      };
    } catch (error) {
      logger.error('[API] Auth header error', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  private isTokenExpired(token: string): boolean {
    try {
      const payload = this.parseJwt(token);
      if (!payload || !payload.exp) return true;

      // Check if token expires in less than 5 minutes
      const expiresIn = payload.exp * 1000 - Date.now();
      return expiresIn < 5 * 60 * 1000;
    } catch (error) {
      logger.error('Error checking token expiration:', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return true;
    }
  }

  /**
   * Get the base URL of the API
   * @returns The base URL of the API
   */
  getBaseUrl(): string {
    return this.baseUrl;
  }

  private getUrl(endpoint: string): string {
    // Remove any leading slashes from the endpoint
    const cleanEndpoint = endpoint.replace(/^\/+/, '');

    // Handle public endpoints differently
    if (cleanEndpoint.startsWith('public/')) {
      return `${this.baseUrl}/${cleanEndpoint}`;
    }

    // For all other endpoints, add the api/v1 prefix
    return `${this.baseUrl}/api/v1/${cleanEndpoint}`;
  }

  private parseJwt(token: string) {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );
      return JSON.parse(jsonPayload);
    } catch (error) {
      logger.error('Error parsing JWT:', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return null;
    }
  }

  private async makeRequest<T>(
    endpoint: string,
    method: string,
    data?: any,
    retryCount = 0,
    maxRetries = 3,
    backoffMs = 1000,
    skipAuth: boolean = false
  ): Promise<ApiResponse<T>> {
    const url = this.getUrl(endpoint);

    try {
      // Log request details at debug level for troubleshooting
      logger.debug(`[API] Request: ${method} ${endpoint}`, {
        skipAuth,
        isPublicEndpoint: endpoint.startsWith('/public/'),
        retryCount,
        url
      });

      // Get auth headers with detailed logging
      let headers;
      try {
        headers = skipAuth ? { 'Content-Type': 'application/json' } : await this.getAuthHeader();
        logger.debug(`[API] Headers obtained for ${method} ${endpoint}`);
      } catch (headerError) {
        logger.error(`[API] Failed to get headers for ${method} ${endpoint}`, {
          error: headerError instanceof Error ? headerError.message : 'Unknown error'
        });
        throw headerError;
      }

      const requestData = data || {};

      // Log the request details
      logger.debug(`[API] Sending ${method} request to ${url}`, {
        headers: Object.keys(headers),
        hasBody: method !== 'GET' && !!requestData,
        bodySize: method !== 'GET' ? JSON.stringify(requestData).length : 0
      });

      const response = await fetch(url, {
        method,
        headers,
        body: method !== 'GET' ? JSON.stringify(requestData) : undefined,
      });

      logger.debug(`[API] Received response from ${url}`, {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries())
      });

      if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.detail || errorData.message || errorMessage;
          // Log extended error information
          logger.error('API Error Response:', {
            status: response.status,
            message: errorMessage,
            endpoint,
            responseData: errorData
          });
        } catch (e) {
          // If can't parse error JSON, use the status text
          errorMessage = response.statusText;
          logger.error('API Error (unparseable):', {
            status: response.status,
            statusText: response.statusText,
            endpoint
          });
        }

        // Handle profile not found with retry
        if (endpoint.includes('profiles') && response.status === 404 && retryCount < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, backoffMs));
          return this.makeRequest(endpoint, method, data, retryCount + 1, maxRetries, backoffMs * 2, skipAuth);
        }

        // If unauthorized and haven't retried yet, try refreshing token and retry
        if (response.status === 401 && retryCount < 1) {
          await supabase.auth.refreshSession();
          return this.makeRequest(endpoint, method, data, retryCount + 1, maxRetries, backoffMs, skipAuth);
        }

        throw new Error(errorMessage);
      }

      const responseData = await response.json();
      return {
        data: responseData,
        error: null
      };
    } catch (error) {
      const err = error as { message?: string; status?: number };
      logger.error(`API Error (${method} ${url}):`, {
        error: err.message || 'Unknown error occurred',
        status: err.status || 500
      });

      // For profile fetching, retry on any error
      if (endpoint.includes('profiles') && retryCount < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, backoffMs));
        return this.makeRequest(endpoint, method, data, retryCount + 1, maxRetries, backoffMs * 2, skipAuth);
      }

      return {
        data: null,
        error: {
          message: err.message || 'Unknown error occurred',
          status: err.status || 500
        }
      };
    }
  }

  /**
   * Make a GET request to the API
   * @param endpoint - The endpoint to request
   * @param skipAuth - Skip authentication for public endpoints
   * @returns The response data
   */
  async get<T>(endpoint: string, skipAuth: boolean = false): Promise<ApiResponse<T>> {
    logger.debug(`[API] GET request to ${endpoint}`, { skipAuth });
    return this.makeRequest<T>(endpoint, 'GET', undefined, 0, 3, 1000, skipAuth);
  }

  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    logger.debug(`[API] POST request to ${endpoint}`, {
      hasData: !!data,
      dataKeys: data ? Object.keys(data) : []
    });
    return this.makeRequest<T>(endpoint, 'POST', data);
  }

  async getAnalysisResults(analysisId: string): Promise<ApiResponse<AnalysisResponse>> {
    try {
      logger.debug('Fetching analysis results for ID', { analysisId });

      const { data, error } = await supabase
        .from('analyses')
        .select(`
          id,
          user_id,
          type,
          business_name,
          location,
          status,
          result_data,
          created_at,
          updated_at
        `)
        .eq('id', analysisId)
        .single();

      if (error) {
        logger.error('Error fetching analysis result', { error: error.message, id: analysisId });
        return {
          data: null,
          error: { message: error.message, status: 400 }
        };
      }

      if (!data) {
        logger.error('No analysis found with ID', { id: analysisId });
        return {
          data: null,
          error: { message: 'Analysis not found', status: 404 }
        };
      }

      // Enhanced debugging - log the complete raw data
      logger.debug(' Complete raw analysis result from database', {
        id: data.id,
        type: data.type,
        business: data.business_name,
        location: data.location,
        resultDataType: typeof data.result_data,
        resultDataRaw: data.result_data
      });

      // Ensure result_data is properly parsed if it's a string
      let resultData = data.result_data;
      if (typeof resultData === 'string') {
        try {
          resultData = JSON.parse(resultData);
          logger.debug(' Successfully parsed result_data from string to object');
        } catch (e) {
          logger.error(' Error parsing result_data JSON', { error: e, id: analysisId });
          resultData = {}; // Fallback to empty object
        }
      } else if (!resultData) {
        logger.warn(' result_data is null or undefined', { id: analysisId });
        resultData = {};
      } else if (typeof resultData !== 'object') {
        logger.warn(` result_data is unexpected type: ${typeof resultData}`, { id: analysisId });
        resultData = {};
      }

      // Log the parsed result data structure
      logger.debug(' Parsed result_data structure', {
        resultDataKeys: Object.keys(resultData || {}),
        hasTypeSpecificData: resultData ? !!resultData[data.type] : false,
        nestedStructure: resultData && resultData[data.type] ?
          `Found nested ${data.type} data with keys: ${Object.keys(resultData[data.type])}` :
          'No nested type-specific data'
      });

      // Process the data and format the response
      const response: AnalysisResponse = {
        id: data.id,
        user_id: data.user_id,
        type: data.type, // Use type directly from database
        business_name: data.business_name || 'Unknown Business',
        location: data.location || 'Unknown Location',
        status: data.status || 'completed',
        tier_used: 'premium', // Hardcoded for now, could be stored in database later
        created_at: data.created_at,
        updated_at: data.updated_at,
        // Map result_data to result for the UI components
        result: resultData || {}
      };

      // Enhanced debugging of the final transformed result
      logger.debug(' Transformed analysis response', {
        id: response.id,
        type: response.type,
        business: response.business_name,
        location: response.location,
        resultStructure: {
          topLevelKeys: Object.keys(response.result || {}),
          hasTypeSpecificData: response.result ? !!response.result[response.type] : false,
          hasMarketShare: response.result?.market_share !== undefined,
          hasPotentialMarketShare: response.result?.potential_market_share !== undefined,
          hasGrowthPotential: response.result?.growth_potential !== undefined,
          hasMetrics: response.result?.metrics !== undefined,
          metricsKeys: response.result?.metrics ? Object.keys(response.result.metrics) : []
        }
      });

      return { data: response, error: null };
    } catch (error: any) {
      logger.error('Unexpected error in getAnalysisResults', { error: error.message, analysisId });
      return {
        data: null,
        error: { message: error.message, status: 500 }
      };
    }
  }

  async fetchUserProfile(userId: string): Promise<any> {
    try {
      // Use the RPC function instead of the view to avoid any RLS issues
      const { data, error } = await supabase
        .rpc('get_user_profile', { 
          user_id_param: userId 
        });

      if (error) throw error;
      if (!data || data.length === 0) throw new Error('Profile not found');

      // Return the first (and only) result
      return data[0];
    } catch (error) {
      const err = error as { code?: string; message: string; details?: string };
      logger.error('Error in profile fetch sequence', {
        userId,
        errorCode: err.code || 'unknown',
        errorMessage: err.message,
        errorDetails: err.details || 'no details'
      });
      throw error;
    }
  }

  // Test the connection to the API server
  async testConnection(): Promise<void> {
    await this.checkConnection();
  }

  async submitAnalysis(type: FrontendAnalysisType, data: any): Promise<ApiResponse<AnalysisResponse>> {
    logger.debug('Submitting analysis request', {
      type,
      endpoint: `analysis/${ANALYSIS_ENDPOINTS[type]}`,
      data: { ...data, sensitive: '[REDACTED]' }
    });
    return this.post<AnalysisResponse>(`analysis/${ANALYSIS_ENDPOINTS[type]}`, data);
  }

  /**
   * Fetch user's analysis history
   * @param userId - The user ID to fetch analyses for
   * @param limit - Maximum number of analyses to fetch
   * @returns Array of user analyses or error
   */
  async fetchUserAnalyses(userId: string, limit: number = 10): Promise<ApiResponse<AnalysisResponse[]>> {
    try {
      logger.debug('Fetching user analyses', { userId, limit });

      // Correctly select from database schema columns
      const { data, error } = await supabase
        .from('analyses')
        .select(`
          id,
          user_id,
          type,
          business_name,
          location,
          status,
          result_data,
          created_at,
          updated_at
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        logger.error('Error fetching user analyses:', {
          error: error.message,
          code: error.code
        });

        return {
          data: [],
          error: null
        };
      }

      // Log the raw data for debugging
      logger.debug('Raw analyses data:', {
        count: data?.length || 0,
        firstItem: data && data.length > 0 ? {
          id: data[0].id,
          type: data[0].type,
          hasResult: !!data[0].result_data
        } : null
      });

      // Use our helper to properly convert and add missing fields with defaults
      const typedData = this.convertToAnalysisResponse(data || []);

      return {
        data: typedData,
        error: null
      };
    } catch (error) {
      const err = error as { message?: string; status?: number };
      logger.error('Error in fetchUserAnalyses:', {
        error: err.message || 'Unknown error',
        userId
      });

      // Return empty array to prevent UI breakage
      return {
        data: [],
        error: null
      };
    }
  }

  /**
   * Fetch user's dashboard metrics
   * @param userId - The user ID to fetch metrics for
   * @returns Dashboard metrics or error
   */
  async fetchDashboardMetrics(userId: string): Promise<ApiResponse<any>> {
    try {
      logger.debug('Fetching dashboard metrics', { userId });

      // Get user analyses with minimal fields
      const { data: analysesData, error: analysesError } = await supabase
        .from('analyses')
        .select('*')
        .eq('user_id', userId);

      if (analysesError) {
        logger.error('Error fetching analyses for metrics:', {
          error: analysesError.message,
          code: analysesError.code
        });

        return {
          data: {
            totalAnalyses: 0,
            recentAnalyses: 0,
            metrics: {
              revenue: 0,
              customers: 0,
              growthRate: 0
            }
          },
          error: null
        };
      }

      // Calculate metrics from analyses data
      const oneMonthAgo = new Date();
      oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

      const recentAnalyses = analysesData && analysesData.length > 0
        ? analysesData.filter(a => new Date(a.created_at) > oneMonthAgo)
        : [];

      // Since we don't have access to the result column with metrics data,
      // we'll use the analysis count to generate some simulated metrics
      const analysisCount = analysesData?.length || 0;

      // Generate some simulated metrics based on the user's analysis count
      const revenue = Math.max(10, analysisCount * 2); // At least 10, 2 per analysis
      const customers = Math.max(50, analysisCount * 15); // At least 50, 15 per analysis
      const growthRate = Math.min(0.5, analysisCount * 0.01); // Cap at 50%, 1% per analysis

      return {
        data: {
          totalAnalyses: analysisCount,
          recentAnalyses: recentAnalyses.length,
          metrics: {
            revenue: revenue,
            customers: customers,
            growthRate: growthRate
          }
        },
        error: null
      };
    } catch (error) {
      const err = error as { message?: string; status?: number };
      logger.error('Error in fetchDashboardMetrics:', {
        error: err.message || 'Unknown error',
        userId
      });

      // Return default metrics with no error to prevent UI breakage
      return {
        data: {
          totalAnalyses: 0,
          recentAnalyses: 0,
          metrics: {
            revenue: 0,
            customers: 0,
            growthRate: 0
          }
        },
        error: null
      };
    }
  }

  private async getHeaders(additionalHeaders: HeadersInit = {}): Promise<HeadersInit> {
    try {
      // Start with default headers
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': `Analytica-Mobile/${Constants.expoConfig?.version || 'unknown'}`
      };

      // Add authorization if available
      try {
        const authHeaders = await this.getAuthHeader();
        Object.assign(headers, authHeaders);
      } catch (error) {
        // If auth fails, continue without auth headers in dev mode
        if (__DEV__) {
          logger.warn('Proceeding without auth headers', {
            error: error instanceof Error ? error.message : String(error)
          });
        } else {
          throw error;
        }
      }

      // Add any additional headers
      if (additionalHeaders) {
        Object.assign(headers, additionalHeaders);
      }

      return headers;
    } catch (error) {
      logger.error('Error building headers', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  async executeRequest<T>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    try {
      // When in development mode, only log health check warnings but proceed with requests
      try {
        await this.checkConnection();
      } catch (error) {
        if (__DEV__) {
          logger.warn('Proceeding with request despite health check failure', {
            endpoint,
            error: error instanceof Error ? error.message : String(error)
          });
        } else {
          throw error;
        }
      }

      const url = `${this.baseUrl}${endpoint}`;
      const headers = await this.getHeaders(options.headers);

      const requestInit: RequestInit = {
        ...options,
        headers
      };

      logger.debug(`API Request: ${options.method || 'GET'} ${endpoint}`, {
        url,
        method: options.method || 'GET',
        headers: Object.fromEntries(Object.entries(headers).filter(([key]) => key !== 'Authorization'))
      });

      // Add timeout to prevent hanging requests
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), __DEV__ ? 15000 : 10000);

      const response = await fetch(url, {
        ...requestInit,
        signal: controller.signal
      });
      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        let errorObj: ApiError;

        try {
          const errorData = JSON.parse(errorText);
          errorObj = {
            message: errorData.message || errorData.error || `Error: ${response.status}`,
            status: response.status
          };
        } catch (e) {
          errorObj = {
            message: errorText || `Error: ${response.status}`,
            status: response.status
          };
        }

        logger.error(`API Error: ${options.method || 'GET'} ${endpoint}`, {
          status: response.status,
          error: errorObj.message,
          url
        });

        return { data: null, error: errorObj };
      }

      const data = await response.json();
      logger.debug(`API Success: ${options.method || 'GET'} ${endpoint}`, {
        status: response.status,
        // Use a safe stringify to avoid massive objects in logs
        data: JSON.stringify(data).substring(0, 200) + (JSON.stringify(data).length > 200 ? '...' : '')
      });

      return { data: data as T, error: null };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      // Handle aborted requests differently (timeout or manual abort)
      if (error instanceof DOMException && error.name === 'AbortError') {
        logger.warn(`API Request Timeout: ${options.method || 'GET'} ${endpoint}`, {
          error: 'Request timed out',
          url: `${this.baseUrl}${endpoint}`
        });

        return {
          data: null,
          error: {
            message: 'Request timed out. Please try again.',
            status: 408
          }
        };
      }

      logger.error(`API Exception: ${options.method || 'GET'} ${endpoint}`, {
        error: errorMessage,
        stack: error instanceof Error ? error.stack : undefined,
        url: `${this.baseUrl}${endpoint}`
      });

      return {
        data: null,
        error: {
          message: errorMessage,
          status: 0
        }
      };
    }
  }

  // Helper function to fix type conversion issue
  private convertToAnalysisResponse(data: any[]): AnalysisResponse[] {
    if (!Array.isArray(data)) {
      logger.warn('Received non-array data for analyses', { dataType: typeof data });
      return [];
    }

    logger.debug('Converting raw analyses data to response format', { count: data.length });

    return data.map(item => {
      // Process result_data - ensure it's parsed if it's a string
      let resultData = item.result_data;
      if (typeof resultData === 'string') {
        try {
          resultData = JSON.parse(resultData);
          logger.debug('Parsed result_data from string', { id: item.id });
        } catch (e) {
          logger.error('Error parsing result_data JSON', { error: e, id: item.id });
          resultData = {}; // Fallback to empty object
        }
      }

      // Use the type directly from the database
      const analysisType = item.type;

      // For debugging
      logger.debug('Analysis item conversion', {
        id: item.id,
        type: analysisType,
        resultDataKeys: resultData ? Object.keys(resultData) : []
      });

      // Create response object with proper defaults
      return {
        id: item.id,
        created_at: item.created_at,
        updated_at: item.updated_at || item.created_at,
        user_id: item.user_id,
        type: analysisType,
        business_name: item.business_name || 'Unknown Business',
        location: item.location || 'Unknown Location',
        status: item.status || 'completed',
        // Map result_data to result for UI components
        result: resultData || {},
        tier_used: 'premium' // Hardcoded for now
      };
    });
  }

  // Update the fetchAnalyses method to use our conversion helper
  async fetchRecentAnalyses(limit: number = 5): Promise<ApiResponse<AnalysisResponse[]>> {
    try {
      const { data: userSession } = await supabase.auth.getSession();
      const userId = userSession?.session?.user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .from('analyses')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        logger.error('Error fetching recent analyses', { error: error.message, details: error });
        return {
          data: null,
          error: { message: error.message, status: 400 }
        };
      }

      if (!data) {
        return { data: [], error: null };
      }

      // Use the conversion helper to ensure correct typing
      const typedData = this.convertToAnalysisResponse(data);

      return { data: typedData, error: null };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error('Exception fetching recent analyses', { error: errorMessage });

      return {
        data: null,
        error: { message: errorMessage, status: 0 }
      };
    }
  }

  async fetchDashboardData(): Promise<ApiResponse<any>> {
    try {
      const { data: userSession } = await supabase.auth.getSession();
      const userId = userSession?.session?.user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      logger.debug('Fetching dashboard data', { userId });

      // Get user profile
      const { data: userData, error: userError } = await supabase
        .from('profiles_view')
        .select('*')
        .eq('id', userId)
        .single();

      if (userError) {
        logger.error('Error fetching user profile', { error: userError.message });
        return {
          data: null,
          error: { message: userError.message, status: 400 }
        };
      }

      // Get user analyses - Make sure we use 'type' instead of 'analysis_type'
      logger.debug('Fetching analyses data for dashboard with correct schema');
      const { data: analysesData, error: analysesError } = await supabase
        .from('analyses')
        .select(`
          id,
          user_id,
          type,
          business_name,
          location,
          status,
          result_data,
          created_at,
          updated_at
        `)
        .eq('user_id', userId);

      if (analysesError) {
        logger.error('Error fetching analyses for dashboard', { error: analysesError });
        return {
          data: null,
          error: { message: analysesError.message, status: 400 }
        };
      }

      // Log raw data for debugging
      logger.debug('Raw analyses data for dashboard', {
        count: analysesData?.length || 0,
        sample: analysesData?.[0] ? {
          id: analysesData[0].id,
          type: analysesData[0].type,
          resultDataKeys: analysesData[0].result_data ? Object.keys(analysesData[0].result_data) : []
        } : null
      });

      // Calculate dashboard metrics
      const totalAnalyses = analysesData?.length || 0;
      const completedAnalyses = analysesData?.filter(a => a.status === 'completed')?.length || 0;
      const pendingAnalyses = analysesData?.filter(a => a.status === 'processing')?.length || 0;

      const userActiveSince = userData?.created_at
        ? new Date(userData.created_at)
        : new Date();

      const activeDays = Math.ceil(
        (new Date().getTime() - userActiveSince.getTime()) / (1000 * 60 * 60 * 24)
      );

      // Format recent analyses for the UI
      const recentAnalyses = analysesData
        ?.slice(0, 5)
        ?.map(analysis => {
          return {
            id: analysis.id,
            created_at: analysis.created_at,
            business_name: analysis.business_name || 'Unknown Business',
            location: analysis.location || 'Unknown Location',
            type: analysis.type, // Use type directly from the database
            status: analysis.status || 'completed',
            // Map result_data to result for consistency with API response
            result: analysis.result_data
          };
        }) || [];

      return {
        data: {
          user: userData,
          stats: {
            totalAnalyses,
            completedAnalyses,
            pendingAnalyses,
            activeDays,
          },
          recentAnalyses: recentAnalyses
        },
        error: null
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error('Error in fetchDashboardData', { error: errorMessage });

      return {
        data: null,
        error: { message: errorMessage, status: 0 }
      };
    }
  }

  // Helper to determine analysis type from data
  private determineAnalysisType(analysis: any): string {
    // Check ID prefix first
    if (analysis.id) {
      if (analysis.id.startsWith('dp_')) return 'dynamic_pricing';
      if (analysis.id.startsWith('mkt_')) return 'market';
      if (analysis.id.startsWith('gth_')) return 'growth';
      if (analysis.id.startsWith('snt_')) return 'sentiment';
      if (analysis.id.startsWith('cf_')) return 'competitor';
      if (analysis.id.startsWith('sf_')) return 'sales_forecasting';
    }

    // Check result data structure
    if (analysis.result_data) {
      const result = typeof analysis.result_data === 'string'
        ? JSON.parse(analysis.result_data)
        : analysis.result_data;

      if (result.dynamic_pricing) return 'dynamic_pricing';
      if (result.market) return 'market';
      if (result.growth) return 'growth';
      if (result.sentiment) return 'sentiment';
      if (result.competitor) return 'competitor';
      if (result.forecast) return 'sales_forecasting';
      if (result.pricing) return 'pricing';
    }

    // Default to unknown
    return 'unknown';
  }

  /**
   * Creates a new analysis
   * @param params Analysis creation parameters
   * @returns API response with data or error
   */
  async createAnalysis(params: {
    userId: string;
    analysisType: FrontendAnalysisType;
    businessName: string;
    industry: string;
    businessType?: string;
    additionalInfo?: string;
  }): Promise<ApiResponse<{ id: string }>> {
    try {
      await this.checkConnection();

      const endpoint = ANALYSIS_ENDPOINTS[params.analysisType];
      const url = this.getUrl(`analysis/${endpoint}`);

      logger.debug('Creating analysis', {
        type: params.analysisType,
        endpoint,
        url,
        businessName: params.businessName
      });

      const headers = await this.getAuthHeader();

      // Match exactly what the backend expects
      const requestBody = {
        business_name: params.businessName,
        business_type: params.businessType || 'unknown',
        location: params.industry,
        timeframe: null,  // Optional parameter
        additional_notes: params.additionalInfo,
        user_id: params.userId,  // Additional field for tracking
        type: params.analysisType  // Additional field for tracking
      };

      logger.debug('Analysis request details', {
        url,
        headers: { ...headers, Authorization: '[REDACTED]' },
        body: requestBody
      });

      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody)
      });

      // Log the raw response for debugging
      logger.debug('Raw API response', {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries())
      });

      const data = await response.json();

      if (!response.ok) {
        logger.error('Analysis creation failed', {
          status: response.status,
          statusText: response.statusText,
          endpoint,
          url,
          responseData: data,
          requestBody
        });

        return {
          data: null,
          error: {
            message: data.message || data.error || 'Failed to create analysis',
            status: response.status
          }
        };
      }

      logger.debug('Analysis created successfully', {
        id: data.id,
        type: params.analysisType,
        endpoint
      });

      return {
        data: { id: data.id },
        error: null
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      logger.error('Analysis creation error', {
        error: errorMessage,
        type: params.analysisType,
        endpoint: ANALYSIS_ENDPOINTS[params.analysisType],
        url: this.getUrl(`analysis/${ANALYSIS_ENDPOINTS[params.analysisType]}`),
        params
      });

      return {
        data: null,
        error: {
          message: errorMessage,
          status: 500
        }
      };
    }
  }

  async updateSubscription(params: {
    userId: string;
    receipt?: string;
    platform: string;
  }): Promise<ApiResponse<any>> {
    try {
      logger.debug('Updating subscription', { userId: params.userId, platform: params.platform });

      // Use the update function to handle both user and business data
      const { data, error } = await supabase.rpc('update_user_profile_with_business', {
        user_id_param: params.userId,
        profile_updates: {
          subscription_tier: params.receipt ? 'premium' : 'standard',
          subscription_status: 'active',
          subscription_updated_at: new Date().toISOString()
        },
        business_updates: {}
      });

      if (error) {
        logger.error('Error updating subscription', { error: error.message });
        return {
          data: null,
          error: { message: error.message, status: 400 }
        };
      }

      // Format the subscription data
      const subscription = {
        tier: data.subscription_tier || 'standard',
        status: data.subscription_status || 'active',
        usage: {
          analyses_this_month: 0
        },
        limits: {
          max_analyses_per_month: data.subscription_tier === 'premium' ? 50 : 3,
          max_competitors: data.subscription_tier === 'premium' ? 3 : 1,
          update_frequency: data.subscription_tier === 'premium' ? '7 days' : 'monthly',
          historical_data_months: data.subscription_tier === 'premium' ? 12 : 3
        }
      };

      return { data: subscription, error: null };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error('Exception updating subscription', { error: errorMessage });
      return {
        data: null,
        error: { message: errorMessage, status: 500 }
      };
    }
  }

  async getSubscriptionUsage(userId: string): Promise<ApiResponse<any>> {
    try {
      logger.debug('Fetching subscription usage', { userId });

      // Get the current month's analyses count
      const startOfMonth = new Date();
      startOfMonth.setDate(1);
      startOfMonth.setHours(0, 0, 0, 0);

      const { count, error } = await supabase
        .from('analyses')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .gte('created_at', startOfMonth.toISOString());

      if (error) {
        logger.error('Error fetching subscription usage', { error: error.message });
        return {
          data: null,
          error: { message: error.message, status: 400 }
        };
      }

      return {
        data: {
          usage: {
            analyses_this_month: count || 0
          }
        },
        error: null
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error('Exception fetching subscription usage', { error: errorMessage });
      return {
        data: null,
        error: { message: errorMessage, status: 500 }
      };
    }
  }
}

export const apiClient = new ApiClient();
