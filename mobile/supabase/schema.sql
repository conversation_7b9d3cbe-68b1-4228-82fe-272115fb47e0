-- Drop existing objects if they exist
DO $$ BEGIN
    -- Drop triggers first
    DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
    DROP TRIGGER IF EXISTS on_auth_user_signed_in ON auth.users;
    DROP TRIGGER IF EXISTS on_auth_user_updated ON auth.users;
    DROP TRIGGER IF EXISTS subscription_date_trigger ON public.profiles;
    DROP TRIGGER IF EXISTS sync_subscription_tier_trigger ON public.profiles;
    
    -- Drop functions with CASCADE to handle dependencies
    DROP FUNCTION IF EXISTS public.handle_new_user() CASCADE;
    DROP FUNCTION IF EXISTS public.handle_user_sign_in() CASCADE;
    DROP FUNCTION IF EXISTS public.sync_user_profile() CASCADE;
    DROP FUNCTION IF EXISTS public.update_subscription_date() CASCADE;
    DROP FUNCTION IF EXISTS public.sync_subscription_tier() CASCADE;
    DROP FUNCTION IF EXISTS public.manage_subscription(uuid, text, boolean) CASCADE;
    DROP FUNCTION IF EXISTS public.create_missing_profiles() CASCADE;
    
    -- Drop dependent tables first
    DROP TABLE IF EXISTS public.growth_metrics;
    DROP TABLE IF EXISTS public.market_trends;
    DROP TABLE IF EXISTS public.strategy_recommendations;
    DROP TABLE IF EXISTS public.analysis_stats;
    DROP TABLE IF EXISTS public.business_monitoring;
    DROP TABLE IF EXISTS public.analyses;
    DROP TABLE IF EXISTS public.profiles;
    DROP TABLE IF EXISTS public.subscription_tiers;
END $$;

-- Grant schema usage
GRANT USAGE ON SCHEMA public TO postgres, anon, authenticated, service_role;

-- Create table for subscription tiers and their limits
CREATE TABLE IF NOT EXISTS public.subscription_tiers (
    tier text PRIMARY KEY,
    max_analyses_per_month integer NOT NULL DEFAULT 10,
    max_tokens_per_analysis integer NOT NULL DEFAULT 1000,
    max_concurrent_analyses integer NOT NULL DEFAULT 1,
    created_at timestamptz NOT NULL DEFAULT now(),
    updated_at timestamptz NOT NULL DEFAULT now(),
    CONSTRAINT valid_tier CHECK (tier IN ('standard', 'premium'))
);

-- Insert default subscription tiers
INSERT INTO public.subscription_tiers (tier, max_analyses_per_month, max_tokens_per_analysis, max_concurrent_analyses)
VALUES 
    ('standard', 10, 1000, 1),
    ('premium', 50, 5000, 3)
ON CONFLICT (tier) DO UPDATE SET
    max_analyses_per_month = EXCLUDED.max_analyses_per_month,
    max_tokens_per_analysis = EXCLUDED.max_tokens_per_analysis,
    max_concurrent_analyses = EXCLUDED.max_concurrent_analyses,
    updated_at = now();

-- Create profiles table for user data
CREATE TABLE IF NOT EXISTS public.profiles (
    id uuid PRIMARY KEY,
    email text NOT NULL,
    full_name text,
    avatar_url text,
    banner_url text,
    business_name text,
    business_type text,
    location text,
    subscription_tier text NOT NULL DEFAULT 'standard',
    subscription_status text NOT NULL DEFAULT 'active',
    subscription_updated_at timestamptz NOT NULL DEFAULT now(),
    last_sign_in_at timestamptz,
    created_at timestamptz NOT NULL DEFAULT now(),
    updated_at timestamptz NOT NULL DEFAULT now(),
    CONSTRAINT fk_user_id FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Create analyses tracking table
CREATE TABLE IF NOT EXISTS public.analyses (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id uuid NOT NULL,
    type text NOT NULL CHECK (
        type IN (
            'dynamic_pricing',
            'pricing',
            'sentiment',
            'market',
            'competitor',
            'sales_forecasting',
            'growth'
        )
    ),
    business_name text NOT NULL,
    business_type text,
    location text,
    input_data jsonb NOT NULL DEFAULT '{}'::jsonb,
    result_data jsonb,
    status text NOT NULL DEFAULT 'pending' CHECK (
        status IN (
            'pending',
            'processing',
            'completed',
            'failed'
        )
    ),
    created_at timestamptz NOT NULL DEFAULT now(),
    updated_at timestamptz NOT NULL DEFAULT now(),
    completed_at timestamptz,
    tokens_used integer DEFAULT 0,
    error_message text,
    CONSTRAINT fk_user_profile
        FOREIGN KEY (user_id)
        REFERENCES public.profiles(id)
        ON DELETE CASCADE
);

-- Create analysis stats table
CREATE TABLE IF NOT EXISTS public.analysis_stats (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    analysis_id uuid NOT NULL,
    user_id uuid NOT NULL,
    max_analyses_per_month integer NOT NULL DEFAULT 10,
    max_competitors integer NOT NULL DEFAULT 5,
    update_frequency interval NOT NULL DEFAULT '24 hours'::interval,
    historical_data_months integer NOT NULL DEFAULT 12,
    created_at timestamptz NOT NULL DEFAULT now(),
    updated_at timestamptz NOT NULL DEFAULT now(),
    CONSTRAINT fk_user_profile
        FOREIGN KEY (user_id)
        REFERENCES public.profiles(id)
        ON DELETE CASCADE,
    CONSTRAINT fk_analysis
        FOREIGN KEY (analysis_id)
        REFERENCES public.analyses(id)
        ON DELETE CASCADE
);

-- Create remaining tables
CREATE TABLE IF NOT EXISTS public.market_trends (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    analysis_id uuid NOT NULL REFERENCES public.analyses(id) ON DELETE CASCADE,
    keyword text NOT NULL,
    trend_value float NOT NULL,
    growth_rate float,
    source text NOT NULL,
    timestamp timestamptz NOT NULL DEFAULT now(),
    created_at timestamptz NOT NULL DEFAULT now(),
    updated_at timestamptz NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS public.growth_metrics (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    analysis_id uuid NOT NULL REFERENCES public.analyses(id) ON DELETE CASCADE,
    metrics jsonb NOT NULL DEFAULT '{}'::jsonb,
    confidence_score numeric CHECK (confidence_score >= 0 AND confidence_score <= 1),
    insights text[],
    recommendations text[],
    created_at timestamptz NOT NULL DEFAULT now(),
    updated_at timestamptz NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS public.strategy_recommendations (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    analysis_id uuid NOT NULL REFERENCES public.analyses(id) ON DELETE CASCADE,
    recommendations jsonb NOT NULL DEFAULT '{}'::jsonb,
    priority text CHECK (priority IN ('high', 'medium', 'low')),
    impact_areas text[],
    implementation_timeline text,
    estimated_roi numeric,
    confidence_score numeric CHECK (confidence_score >= 0 AND confidence_score <= 1),
    created_at timestamptz NOT NULL DEFAULT now(),
    updated_at timestamptz NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS public.business_monitoring (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id uuid NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    business_id text NOT NULL,
    last_check timestamptz NOT NULL DEFAULT now(),
    next_check timestamptz NOT NULL DEFAULT now() + interval '24 hours',
    alert_config jsonb NOT NULL DEFAULT '{}'::jsonb,
    created_at timestamptz NOT NULL DEFAULT now(),
    updated_at timestamptz NOT NULL DEFAULT now()
);

-- Grant table permissions
GRANT ALL ON ALL TABLES IN SCHEMA public TO postgres, service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO postgres, service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO postgres, service_role;

-- Grant specific permissions to authenticated users
GRANT SELECT, INSERT, UPDATE ON public.profiles TO authenticated;
GRANT SELECT ON public.subscription_tiers TO authenticated;
GRANT SELECT, INSERT ON public.analyses TO authenticated;
GRANT SELECT ON public.analysis_stats TO authenticated;
GRANT SELECT ON public.market_trends TO authenticated;
GRANT SELECT ON public.growth_metrics TO authenticated;
GRANT SELECT ON public.strategy_recommendations TO authenticated;
GRANT SELECT ON public.business_monitoring TO authenticated;

-- Grant specific permissions to anonymous users
GRANT SELECT ON public.subscription_tiers TO anon;

-- Enable RLS on all tables
ALTER TABLE public.subscription_tiers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.analyses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.analysis_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.market_trends ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.growth_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.strategy_recommendations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.business_monitoring ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Allow public read access to subscription_tiers"
    ON public.subscription_tiers FOR SELECT
    TO authenticated, anon
    USING (true);

CREATE POLICY "Public profiles are viewable by everyone."
    ON public.profiles FOR SELECT
    USING (true);

CREATE POLICY "Users can insert their own profile."
    ON public.profiles FOR INSERT
    WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update own profile."
    ON public.profiles FOR UPDATE
    USING (auth.uid() = id)
    WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can view their own analyses"
    ON public.analyses FOR SELECT
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own analyses"
    ON public.analyses FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view their own analysis stats"
    ON public.analysis_stats FOR SELECT
    TO authenticated
    USING (auth.uid() = user_id);

-- Create storage bucket for avatars if it doesn't exist
INSERT INTO storage.buckets (id, name, public)
VALUES ('avatars', 'avatars', true)
ON CONFLICT (id) DO NOTHING;

-- Allow public access to avatars
CREATE POLICY "Avatars are publicly accessible"
ON storage.objects FOR SELECT
USING (bucket_id = 'avatars');

-- Allow authenticated users to upload avatars
CREATE POLICY "Users can upload avatars"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
    bucket_id = 'avatars' AND
    (storage.foldername(name))[1] = auth.uid()::text
);

-- Allow users to update their own avatars
CREATE POLICY "Users can update their own avatars"
ON storage.objects FOR UPDATE
TO authenticated
USING (
    bucket_id = 'avatars' AND
    (storage.foldername(name))[1] = auth.uid()::text
)
WITH CHECK (
    bucket_id = 'avatars' AND
    (storage.foldername(name))[1] = auth.uid()::text
);

-- Allow users to delete their own avatars
CREATE POLICY "Users can delete their own avatars"
ON storage.objects FOR DELETE
TO authenticated
USING (
    bucket_id = 'avatars' AND
    (storage.foldername(name))[1] = auth.uid()::text
);

-- Create storage bucket for banners if it doesn't exist
INSERT INTO storage.buckets (id, name, public)
VALUES ('banners', 'banners', true)
ON CONFLICT (id) DO NOTHING;

-- Allow public access to banners
CREATE POLICY "Banners are publicly accessible"
ON storage.objects FOR SELECT
USING (bucket_id = 'banners');

-- Allow authenticated users to upload banners
CREATE POLICY "Users can upload banners"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
    bucket_id = 'banners' AND
    (storage.foldername(name))[1] = auth.uid()::text
);

-- Allow users to update their own banners
CREATE POLICY "Users can update their own banners"
ON storage.objects FOR UPDATE
TO authenticated
USING (
    bucket_id = 'banners' AND
    (storage.foldername(name))[1] = auth.uid()::text
)
WITH CHECK (
    bucket_id = 'banners' AND
    (storage.foldername(name))[1] = auth.uid()::text
);

-- Allow users to delete their own banners
CREATE POLICY "Users can delete their own banners"
ON storage.objects FOR DELETE
TO authenticated
USING (
    bucket_id = 'banners' AND
    (storage.foldername(name))[1] = auth.uid()::text
);

-- Create trigger to create profiles for new users
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    INSERT INTO public.profiles (
        id,
        email,
        full_name,
        subscription_tier,
        subscription_status,
        subscription_updated_at,
        created_at,
        updated_at
    )
    VALUES (
        NEW.id,
        COALESCE(NEW.email, ''),
        COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
        COALESCE(NEW.raw_user_meta_data->>'subscription_tier', 'standard'),
        'active',
        now(),
        now(),
        now()
    );
    RETURN NEW;
END;
$$;

-- Create trigger for new users
CREATE OR REPLACE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_new_user();

-- Function to create missing profiles
CREATE OR REPLACE FUNCTION public.create_missing_profiles()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    user_record RECORD;
BEGIN
    FOR user_record IN 
        SELECT 
            au.id,
            au.email,
            COALESCE(au.raw_user_meta_data->>'full_name', '') as full_name,
            COALESCE(au.raw_user_meta_data->>'subscription_tier', 'standard') as subscription_tier
        FROM auth.users au
        LEFT JOIN public.profiles p ON p.id = au.id
        WHERE p.id IS NULL
    LOOP
        INSERT INTO public.profiles (
            id,
            email,
            full_name,
            subscription_tier,
            subscription_status,
            subscription_updated_at,
            created_at,
            updated_at
        ) VALUES (
            user_record.id,
            COALESCE(user_record.email, ''),
            COALESCE(user_record.full_name, ''),
            user_record.subscription_tier,
            'active',
            now(),
            now(),
            now()
        )
        ON CONFLICT (id) DO NOTHING;
    END LOOP;
END;
$$;

-- Create function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
DECLARE
    default_tier text;
    new_profile_id uuid;
BEGIN
    -- Log incoming data
    RAISE LOG 'Creating profile for user %. Email: %. Raw meta: %', 
        new.id, 
        new.email,
        new.raw_user_meta_data;

    -- Get default tier with fallback
    SELECT tier INTO default_tier 
    FROM public.subscription_tiers 
    WHERE tier = COALESCE(new.raw_user_meta_data->>'subscription_tier', 'standard')
    LIMIT 1;

    IF default_tier IS NULL THEN
        RAISE LOG 'Default tier not found, creating standard tier';
        default_tier := 'standard';
        
        -- Ensure standard tier exists
        INSERT INTO public.subscription_tiers (tier, max_analyses_per_month, max_tokens_per_analysis, max_concurrent_analyses)
        VALUES ('standard', 10, 1000, 1)
        ON CONFLICT (tier) DO NOTHING;
    END IF;

    -- Create profile with proper error handling
    BEGIN
        INSERT INTO public.profiles (
            id,
            email,
            full_name,
            subscription_tier,
            subscription_date,
            created_at,
            updated_at
        ) VALUES (
            new.id,
            COALESCE(new.email, ''),
            COALESCE(new.raw_user_meta_data->>'full_name', ''),
            default_tier,
            COALESCE((new.raw_user_meta_data->>'subscription_date')::timestamptz, now()),
            now(),
            now()
        )
        RETURNING id INTO new_profile_id;

        IF new_profile_id IS NULL THEN
            RAISE LOG 'Failed to create profile for user %. Error: Profile ID is NULL', new.id;
            RETURN new;
        END IF;

        RAISE LOG 'Successfully created profile % for user %', new_profile_id, new.id;
        RETURN new;
    EXCEPTION 
        WHEN unique_violation THEN
            RAISE LOG 'Profile already exists for user %, updating instead', new.id;
            -- If profile exists, update it
            UPDATE public.profiles
            SET 
                email = COALESCE(new.email, email),
                full_name = COALESCE(new.raw_user_meta_data->>'full_name', full_name),
                subscription_tier = COALESCE(new.raw_user_meta_data->>'subscription_tier', subscription_tier),
                subscription_date = COALESCE((new.raw_user_meta_data->>'subscription_date')::timestamptz, subscription_date),
                updated_at = now()
            WHERE id = new.id
            RETURNING id INTO new_profile_id;

            IF new_profile_id IS NULL THEN
                RAISE LOG 'Failed to update existing profile for user %. Error: Profile ID is NULL', new.id;
                RETURN new;
            END IF;

            RAISE LOG 'Successfully updated profile % for user %', new_profile_id, new.id;
            RETURN new;
        WHEN OTHERS THEN
            RAISE LOG 'Error in handle_new_user for user %: % (SQLSTATE: %)', new.id, SQLERRM, SQLSTATE;
            RETURN new;
    END;
END;
$$;

-- Drop and recreate the trigger with proper permissions
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT OR UPDATE ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_new_user();

-- Create missing profiles for existing users
SELECT public.create_missing_profiles();

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.handle_new_user() TO authenticated;
GRANT EXECUTE ON FUNCTION public.create_missing_profiles() TO authenticated;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA public TO postgres, service_role;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO anon;
GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO postgres, anon, authenticated, service_role;
