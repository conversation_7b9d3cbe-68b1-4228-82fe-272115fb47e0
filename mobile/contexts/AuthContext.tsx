import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '../utils/supabase';
import { authService } from '../utils/authService';
import { Session, User } from '@supabase/supabase-js';
import { useRouter, useSegments } from 'expo-router';
import * as SecureStore from 'expo-secure-store';
import { logger } from '../utils/logger';

// For development only
const DEV_MODE = true; // Set this to false before deploying to production

// Verify Supabase client is available
if (!supabase?.auth) {
  console.error('Supabase client is not properly initialized');
  throw new Error('Supabase client is not properly initialized');
}

const AUTH_STATE_KEY = 'auth_state';

interface AuthState {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, profile: { full_name: string; email: string }) => Promise<void>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthState | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const segments = useSegments();
  const router = useRouter();

  const [state, setState] = useState<Omit<AuthState, 'signIn' | 'signUp' | 'signOut'>>({
    user: null,
    session: null,
    loading: true,
  });

  // Handle navigation based on auth state
  useEffect(() => {
    if (!state.loading) {
      const inAuthGroup = segments[0] === '(auth)';

      if (!state.session && !inAuthGroup) {
        router.replace('/(auth)/login');
      } else if (state.session && inAuthGroup) {
        router.replace('/(app)/dashboard');
      }
    }
  }, [state.session, state.loading, segments]);

  // Initialize auth state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        console.log('🔐 Starting auth initialization...');
        // Get initial session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        if (sessionError) {
          console.error('❌ Error getting session:', sessionError);
          setState(prev => ({ ...prev, loading: false }));
          return;
        }

        console.log('✅ Session retrieved:', {
          hasSession: !!session,
          hasUser: !!session?.user,
          userEmail: session?.user?.email
        });

        // Set initial state
        setState({
          session,
          user: session?.user ?? null,
          loading: false,
        });

        console.log('🔐 Auth state initialized, setting up listener...');

        // Listen for auth changes
        const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
          try {
            console.log('🔄 Auth state change:', {
              event,
              hasSession: !!session,
              hasUser: !!session?.user,
              userEmail: session?.user?.email
            });

            if (event === 'SIGNED_OUT') {
              console.log('👋 User signed out, clearing state...');
              // Clear any stored auth state
              await SecureStore.deleteItemAsync(AUTH_STATE_KEY);

              setState({
                session: null,
                user: null,
                loading: false,
              });
            } else if (session) {
              console.log('👤 User signed in, updating state...');
              // Store minimal session data to stay under 2048 bytes
              const minimalSessionData = {
                access_token: session.access_token,
                refresh_token: session.refresh_token,
                expires_at: session.expires_at,
              };

              await SecureStore.setItemAsync(AUTH_STATE_KEY, JSON.stringify(minimalSessionData));

              // Create a new state object instead of modifying the session
              setState({
                session: { ...session },
                user: session?.user ?? null,
                loading: false,
              });
            }
          } catch (error) {
            console.error('❌ Error handling auth state change:', error);
            setState(prev => ({ ...prev, loading: false }));
          }
        });

        console.log('✅ Auth listener set up successfully');

        return () => {
          console.log('🧹 Cleaning up auth subscription');
          subscription.unsubscribe();
        };
      } catch (error) {
        console.error('❌ Error initializing auth:', error);
        setState(prev => ({ ...prev, loading: false }));
      }
    };

    initializeAuth();
  }, []);

  const signIn = async (email: string, password: string) => {
    setState(prev => ({ ...prev, loading: true }));
    try {
      logger.info('Attempting to sign in', { email });

      // Try the API-based authentication first
      try {
        const authResponse = await authService.signIn(email, password);
        logger.info('API-based authentication successful');

        // Use the returned token to sign in with Supabase
        const { data, error } = await supabase.auth.signInWithPassword({
          email,
          password,
        });

        if (error) {
          logger.error('Supabase sign in error after API auth:', error);
          throw error;
        }

        if (!data.session || !data.user) {
          throw new Error('No session or user data returned after sign in');
        }

        // Store minimal session data
        const minimalSessionData = {
          access_token: data.session.access_token,
          refresh_token: data.session.refresh_token,
          expires_at: data.session.expires_at,
        };
      } catch (apiError) {
        logger.warn('API-based authentication failed, falling back to direct Supabase auth', { error: apiError });

        // Fall back to direct Supabase authentication
        const { data, error } = await supabase.auth.signInWithPassword({
          email,
          password,
        });

        if (error) {
          logger.error('Sign in error:', error);
          throw error;
        }

        if (!data.session || !data.user) {
          throw new Error('No session or user data returned after sign in');
        }

        // Store minimal session data
        const minimalSessionData = {
          access_token: data.session.access_token,
          refresh_token: data.session.refresh_token,
          expires_at: data.session.expires_at,
        };

        await SecureStore.setItemAsync(AUTH_STATE_KEY, JSON.stringify(minimalSessionData));

        // Update state with session data
        setState(prev => ({
          ...prev,
          session: data.session,
          user: data.user,
          loading: false
        }));
      }
    } catch (error: any) {
      console.error('Sign in error:', error);
      setState(prev => ({ ...prev, loading: false }));
      throw error;
    }
  };

  const signUp = async (email: string, password: string, profile: { full_name: string; email: string }) => {
    setState(prev => ({ ...prev, loading: true }));
    try {
      logger.info('Attempting to register', { email });

      // Try the API-based registration first
      try {
        const authResponse = await authService.signUp(email, password, profile);
        logger.info('API-based registration successful');
      } catch (apiError) {
        logger.warn('API-based registration failed, falling back to direct Supabase auth', { error: apiError });
      }

      // Always perform the Supabase registration to ensure proper client-side state
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: profile.full_name,
            email: profile.email,
          },
        },
      });

      if (error) {
        logger.error('Sign up error:', error);
        throw error;
      }

      if (!data.user) {
        throw new Error('No user data returned after sign up');
      }

      // Wait for profile to be created (with retry)
      let retries = 0;
      let profileData = null;
      while (retries < 3) {
        const { data: profile, error: profileError } = await supabase
          .from('profiles_view')
          .select('*')
          .eq('id', data.user.id)
          .single();

        if (profile) {
          profileData = profile;
          break;
        }

        // Wait 1 second before retrying
        await new Promise(resolve => setTimeout(resolve, 1000));
        retries++;
      }

      if (!profileData) {
        // If profile doesn't exist after retries, create it manually using the update function
        const { data: createdProfile, error: insertError } = await supabase.rpc('update_user_profile_with_business', {
          user_id_param: data.user.id,
          profile_updates: {
            id: data.user.id,
            email: profile.email,
            full_name: profile.full_name,
            subscription_tier: 'standard'
          },
          business_updates: {}
        });

        if (insertError) {
          console.error('Error creating profile:', insertError);
          // Clean up the created user since profile creation failed
          await supabase.auth.signOut();
          throw new Error('Failed to create user profile');
        }
      }

      // Now we can safely update the state
      setState(prev => ({
        ...prev,
        session: data.session,
        user: data.user,
        loading: false
      }));
    } catch (error: any) {
      console.error('Sign up error:', error);
      setState(prev => ({ ...prev, loading: false }));
      throw error;
    }
  };

  const signOut = async () => {
    setState(prev => ({ ...prev, loading: true }));
    try {
      // First update the state to clear the session
      setState(prev => ({
        ...prev,
        user: null,
        session: null,
        loading: true
      }));

      // Then clear stored auth state
      await SecureStore.deleteItemAsync(AUTH_STATE_KEY);

      // Finally sign out from Supabase
      const { error } = await supabase.auth.signOut({
        scope: 'global'  // Sign out from all tabs/windows
      });

      if (error) {
        console.error('Sign out error:', error);
        throw error;
      }

      setState(prev => ({ ...prev, loading: false }));
    } catch (error: any) {
      console.error('Sign out error:', error);
      setState(prev => ({ ...prev, loading: false }));
      throw error;
    }
  };

  return (
    <AuthContext.Provider
      value={{
        ...state,
        signIn,
        signUp,
        signOut,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}