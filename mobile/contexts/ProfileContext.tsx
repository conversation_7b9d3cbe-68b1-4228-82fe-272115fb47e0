import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '../utils/supabase';
import { Profile } from '../types/supabase';
import { useAuth } from './AuthContext';
import logger from '../utils/logger';
import { Platform, Image as RNImage } from 'react-native';
import { uploadToCloudinary, isSupabaseStorageUrl, migrateImageToCloudinary } from '../utils/cloudinary';

interface ProfileContextType {
  profile: Profile | null;
  loading: boolean;
  error: string | null;
  updateProfile: (updates: Partial<Profile>) => Promise<void>;
  uploadAvatar: (uri: string) => Promise<string>;
  uploadBanner: (uri: string) => Promise<string>;
  refetchProfile: () => Promise<void>; // Add refetchProfile here
  clearInvalidImageUrls: () => Promise<void>; // Add function to clear invalid image URLs
  migrateToCloudinary: () => Promise<void>; // Migrate Supabase Storage URLs to Cloudinary
}

const ProfileContext = createContext<ProfileContextType | undefined>(undefined);

export function ProfileProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth();
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      fetchProfile();
    } else {
      setProfile(null);
      setLoading(false);
    }
  }, [user]);

  // Set up real-time profile updates
  useEffect(() => {
    if (!user) return;

    logger.debug('Setting up real-time profile updates', { userId: user.id });

    const channel = supabase.channel('profile-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'users',
          filter: `id=eq.${user.id}`
        },
        (payload) => {
          logger.debug('User data change detected', payload);
          // Refetch the complete profile when user data changes
          refetchProfile().catch(err => logger.error('Error refetching after user change:', err));
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'businesses',
          filter: `user_id=eq.${user.id}`
        },
        (payload) => {
          logger.debug('Business data change detected', payload);
          // Refetch the complete profile when business data changes
          refetchProfile().catch(err => logger.error('Error refetching after business change:', err));
        }
      )
      .subscribe();

    return () => {
      logger.debug('Cleaning up profile subscription');
      supabase.removeChannel(channel);
    };
  }, [user]);

  const fetchProfile = async () => {
    try {
      logger.debug('Fetching user profile', { userId: user?.id });
      const { data, error } = await supabase
        .rpc('get_user_profile', { 
          user_id_param: user?.id 
        });

      if (error) throw error;
      if (!data || data.length === 0) throw new Error('Profile not found');
      
      setProfile(data[0]);
      logger.debug('Profile fetched successfully');
    } catch (error: any) {
      logger.error('Error fetching profile:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const refetchProfile = async () => {
    if (!user?.id) throw new Error('No user logged in');

    try {
      setLoading(true);
      logger.debug('Refetching user profile', { userId: user.id });

      // Use the RPC function for backward compatibility and to avoid RLS issues
      const { data, error } = await supabase
        .rpc('get_user_profile', { 
          user_id_param: user.id 
        });

      if (error) throw error;
      if (!data || data.length === 0) throw new Error('Profile not found');

      const profileData = data[0];

      // Log the profile data for debugging
      logger.debug('Profile data received:', {
        avatar_url: profileData.avatar_url,
        subscription_tier: profileData.subscription_tier,
        subscription_status: profileData.subscription_status,
        business_name: profileData.business_name,
        business_type: profileData.business_type
      });

      // Update the profile state
      setProfile(profileData);
      logger.debug('Profile refetched successfully');
      return profileData;
    } catch (error: any) {
      logger.error('Error refetching profile:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (updates: Partial<Profile>) => {
    if (!user?.id) throw new Error('No user logged in');

    try {
      setLoading(true);
      logger.debug('Updating user profile', { userId: user.id, updates });

      // Separate user updates from business updates
      const userFields = [
        'full_name', 'first_name', 'last_name', 'avatar_url', 'phone', 
        'timezone', 'language', 'subscription_tier', 'subscription_status',
        'subscription_updated_at', 'email_verified', 'phone_verified',
        'preferences', 'metadata'
      ];
      
      const businessFields = [
        'business_name', 'business_type', 'location', 'banner_url',
        'business_description', 'business_website', 'business_phone'
      ];

      const userUpdates: any = {};
      const businessUpdates: any = {};

      Object.entries(updates).forEach(([key, value]) => {
        if (userFields.includes(key)) {
          userUpdates[key] = value;
        } else if (businessFields.includes(key)) {
          businessUpdates[key] = value;
        }
      });

      // Add updated_at to user updates
      if (Object.keys(userUpdates).length > 0) {
        userUpdates.updated_at = new Date().toISOString();
      }

      // Use the update function that handles both user and business data
      const { data, error } = await supabase.rpc('update_user_profile_with_business', {
        user_id_param: user.id,
        profile_updates: Object.keys(userUpdates).length > 0 ? userUpdates : {},
        business_updates: Object.keys(businessUpdates).length > 0 ? businessUpdates : {}
      });

      if (error) {
        logger.error('Error updating profile:', error);
        throw error;
      }

      setProfile(prev => ({ ...prev, ...data }));
      setError(null);
      logger.debug('Profile updated successfully', data);
    } catch (error: any) {
      logger.error('Error updating profile:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const uploadAvatar = async (uri: string) => {
    if (!user?.id) throw new Error('No user logged in');

    try {
      setLoading(true);
      logger.debug('Starting avatar upload to Cloudinary', { userId: user.id, uri: uri.substring(0, 50) + '...' });

      // Upload the image to Cloudinary with avatar-specific settings
      const folder = `avatars/${user.id}`;
      const publicUrl = await uploadToCloudinary(uri, folder, true); // true = isAvatar
      logger.debug('Image uploaded to Cloudinary, updating profile with URL', { publicUrl });

      // Update profile using the new update function
      await updateProfile({ avatar_url: publicUrl });

      logger.debug('Avatar upload and profile update completed successfully');
      return publicUrl;
    } catch (error: any) {
      logger.error('Error in avatar upload process:', error);
      setError(error.message || 'Failed to upload avatar');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const uploadBanner = async (uri: string) => {
    if (!user?.id) throw new Error('No user logged in');

    try {
      setLoading(true);
      logger.debug('Starting banner upload to Cloudinary', { userId: user.id, uri: uri.substring(0, 50) + '...' });

      // Upload the image to Cloudinary with banner-specific settings
      const folder = `banners/${user.id}`;
      const publicUrl = await uploadToCloudinary(uri, folder, false); // false = not an avatar
      logger.debug('Image uploaded to Cloudinary, updating profile with URL', { publicUrl });

      // Update profile using the new update function (banner_url goes to business)
      await updateProfile({ banner_url: publicUrl });

      logger.debug('Banner upload and profile update completed successfully');
      return publicUrl;
    } catch (error: any) {
      logger.error('Error in banner upload process:', error);
      setError(error.message || 'Failed to upload banner');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Function to migrate Supabase Storage URLs to Cloudinary
  const migrateToCloudinary = async () => {
    if (!user?.id) throw new Error('No user logged in');
    if (!profile) throw new Error('No profile loaded');

    try {
      setLoading(true);
      logger.debug('Starting migration of images to Cloudinary', { userId: user.id });

      let updates: Partial<Profile> = {};
      let needsUpdate = false;

      // Check if avatar URL is from Supabase Storage
      if (profile.avatar_url && isSupabaseStorageUrl(profile.avatar_url)) {
        logger.debug('Migrating avatar from Supabase to Cloudinary', { url: profile.avatar_url });
        try {
          // Migrate avatar to Cloudinary
          const folder = `avatars/${user.id}`;
          const cloudinaryUrl = await migrateImageToCloudinary(profile.avatar_url, folder, true); // true = isAvatar

          if (cloudinaryUrl && cloudinaryUrl !== profile.avatar_url) {
            updates.avatar_url = cloudinaryUrl;
            // Keep load_error in local state only, not in database
            needsUpdate = true;
            logger.debug('Avatar migrated successfully', { newUrl: cloudinaryUrl });
          }
        } catch (error) {
          logger.error('Error migrating avatar to Cloudinary:', error);
        }
      }

      // Check if banner URL is from Supabase Storage
      if (profile.banner_url && isSupabaseStorageUrl(profile.banner_url)) {
        logger.debug('Migrating banner from Supabase to Cloudinary', { url: profile.banner_url });
        try {
          // Migrate banner to Cloudinary
          const folder = `banners/${user.id}`;
          const cloudinaryUrl = await migrateImageToCloudinary(profile.banner_url, folder, false); // false = not an avatar

          if (cloudinaryUrl && cloudinaryUrl !== profile.banner_url) {
            updates.banner_url = cloudinaryUrl;
            // Keep load_error in local state only, not in database
            needsUpdate = true;
            logger.debug('Banner migrated successfully', { newUrl: cloudinaryUrl });
          }
        } catch (error) {
          logger.error('Error migrating banner to Cloudinary:', error);
        }
      }

      // Update profile if needed
      if (needsUpdate) {
        logger.debug('Updating profile with Cloudinary URLs', updates);

        // Use the new update function to handle both avatar (user) and banner (business) updates
        await updateProfile(updates);

        logger.debug('Profile updated with Cloudinary URLs');
      } else {
        logger.debug('No Supabase Storage URLs found in profile');
      }
    } catch (error: any) {
      logger.error('Error migrating images to Cloudinary:', error);
      setError(error.message || 'Failed to migrate images to Cloudinary');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Function to clear invalid image URLs from the profile
  const clearInvalidImageUrls = async () => {
    if (!user?.id) throw new Error('No user logged in');
    if (!profile) throw new Error('No profile loaded');

    const isImageUrlValid = async (url: string): Promise<boolean> => {
      if (Platform.OS !== 'web' && typeof RNImage.getSize === 'function') {
        return new Promise((resolve) => {
          RNImage.getSize(
            url,
            () => resolve(true),
            () => resolve(false)
          );
        });
      } else {
        // Fallback: assume valid (or return true) on web or if Image.getSize isn't available
        return true;
      }
    };

    try {
      setLoading(true);
      logger.debug('Checking for invalid image URLs in profile', { userId: user.id });

      // Check if avatar URL is valid
      let updates: Partial<Profile> = {};
      let needsUpdate = false;

      if (profile.avatar_url) {
        const isValid = await isImageUrlValid(profile.avatar_url);
        if (!isValid) {
          logger.warn('Avatar URL is invalid, will clear it', { url: profile.avatar_url });
          updates.avatar_url = null;
          needsUpdate = true;
        }
      }

      // Check if banner URL is valid
      if (profile.banner_url) {
        const isValid = await isImageUrlValid(profile.banner_url);
        if (!isValid) {
          logger.warn('Banner URL is invalid, will clear it', { url: profile.banner_url });
          updates.banner_url = null;
          needsUpdate = true;
        }
      }

      // Update profile if needed
      if (needsUpdate) {
        logger.debug('Updating profile to clear invalid image URLs', updates);

        // Use the new update function
        await updateProfile(updates);

        logger.debug('Profile updated to clear invalid image URLs');
      } else {
        logger.debug('No invalid image URLs found in profile');
      }
    } catch (error: any) {
      logger.error('Error clearing invalid image URLs:', error);
      setError(error.message || 'Failed to clear invalid image URLs');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return (
    <ProfileContext.Provider value={{
      profile,
      loading,
      error,
      updateProfile,
      uploadAvatar,
      uploadBanner,
      refetchProfile,
      clearInvalidImageUrls,
      migrateToCloudinary
    }}>
      {children}
    </ProfileContext.Provider>
  );
}

export function useProfile() {
  const context = useContext(ProfileContext);
  if (context === undefined) {
    throw new Error('useProfile must be used within a ProfileProvider');
  }
  return context;
}
