import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'SME Analytica Documentation',
  description: 'Comprehensive documentation for SME Analytica - AI Business Intelligence Platform',
  keywords: ['SME Analytica', 'AI', 'Business Intelligence', 'Small Business', 'Analytics'],
  authors: [{ name: 'SME Analytica Team' }],
  openGraph: {
    title: 'SME Analytica Documentation',
    description: 'The ultimate guide to AI-powered business intelligence for small and medium enterprises',
    url: 'https://docs.smeanalytica.dev',
    siteName: 'SME Analytica Docs',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'SME Analytica Documentation',
    description: 'The ultimate guide to AI-powered business intelligence for small and medium enterprises',
    creator: '@smeanalytica',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>{children}</body>
    </html>
  );
} 