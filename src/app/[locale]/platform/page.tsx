import { useTranslations } from 'next-intl';
import { Metadata } from 'next';
import { 
  Zap, 
  Brain, 
  TrendingUp, 
  Link as LinkIcon,
  Server,
  Smartphone,
  Database,
  Cloud
} from 'lucide-react';

export const metadata: Metadata = {
  title: 'Platform Overview - SME Analytica Documentation',
  description: 'Learn about SME Analytica\'s AI-powered platform architecture, core benefits, and technology stack.',
};

export default function PlatformPage() {
  const t = useTranslations('platform');

  const benefits = [
    {
      key: 'realtime',
      icon: Zap,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100 dark:bg-yellow-900/30',
    },
    {
      key: 'ai',
      icon: Brain,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100 dark:bg-purple-900/30',
    },
    {
      key: 'pricing',
      icon: TrendingUp,
      color: 'text-green-600',
      bgColor: 'bg-green-100 dark:bg-green-900/30',
    },
    {
      key: 'integration',
      icon: LinkIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100 dark:bg-blue-900/30',
    },
  ];

  const techStack = [
    {
      key: 'backend',
      icon: Server,
      color: 'text-red-600',
      bgColor: 'bg-red-100 dark:bg-red-900/30',
    },
    {
      key: 'frontend',
      icon: Smartphone,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100 dark:bg-blue-900/30',
    },
    {
      key: 'database',
      icon: Database,
      color: 'text-green-600',
      bgColor: 'bg-green-100 dark:bg-green-900/30',
    },
    {
      key: 'ai',
      icon: Brain,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100 dark:bg-purple-900/30',
    },
    {
      key: 'infrastructure',
      icon: Cloud,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100 dark:bg-orange-900/30',
    },
  ];

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
              {t('title')}
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Discover how SME Analytica revolutionizes business intelligence for small and medium enterprises
            </p>
          </div>
        </div>
      </section>

      {/* What is SME Analytica */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-8 text-center">
              {t('whatIs.title')}
            </h2>
            <div className="bg-gradient-to-r from-primary-50 to-blue-50 dark:from-primary-900/20 dark:to-blue-900/20 rounded-2xl p-8 border border-primary-200 dark:border-primary-800">
              <p className="text-lg text-gray-700 dark:text-gray-300 leading-relaxed">
                {t('whatIs.description')}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Core Benefits */}
      <section className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              {t('benefits.title')}
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Discover the key advantages that make SME Analytica the perfect choice for your business
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit) => {
              const Icon = benefit.icon;
              return (
                <div
                  key={benefit.key}
                  className="bg-white dark:bg-gray-900 rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow border border-gray-200 dark:border-gray-700"
                >
                  <div className={`w-12 h-12 ${benefit.bgColor} rounded-lg flex items-center justify-center mb-4`}>
                    <Icon className={`w-6 h-6 ${benefit.color}`} />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                    {t(`benefits.${benefit.key}.title` as any)}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    {t(`benefits.${benefit.key}.description` as any)}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Technology Stack */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              {t('techStack.title')}
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Built with cutting-edge technologies to ensure scalability, reliability, and performance
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {techStack.map((tech) => {
              const Icon = tech.icon;
              return (
                <div
                  key={tech.key}
                  className="bg-gray-50 dark:bg-gray-800 rounded-xl p-6 hover:bg-white dark:hover:bg-gray-700 transition-all duration-300 border border-gray-200 dark:border-gray-700 hover:shadow-lg"
                >
                  <div className={`w-12 h-12 ${tech.bgColor} rounded-lg flex items-center justify-center mb-4`}>
                    <Icon className={`w-6 h-6 ${tech.color}`} />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    {tech.key.charAt(0).toUpperCase() + tech.key.slice(1)}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">
                    {t(`techStack.${tech.key}` as any)}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Architecture Diagram */}
      <section className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Platform Architecture
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              A high-level overview of how SME Analytica components work together
            </p>
          </div>

          <div className="bg-white dark:bg-gray-900 rounded-2xl p-8 shadow-lg border border-gray-200 dark:border-gray-700">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 items-center">
              {/* Data Sources */}
              <div className="text-center">
                <div className="bg-blue-100 dark:bg-blue-900/30 rounded-lg p-6 mb-4">
                  <Database className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-gray-900 dark:text-white">Data Sources</h3>
                </div>
                <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                  <li>Google Places API</li>
                  <li>TripAdvisor API</li>
                  <li>Yelp API</li>
                  <li>POS Systems</li>
                  <li>Internal Data</li>
                </ul>
              </div>

              {/* AI Processing */}
              <div className="text-center">
                <div className="bg-purple-100 dark:bg-purple-900/30 rounded-lg p-6 mb-4">
                  <Brain className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-gray-900 dark:text-white">AI Processing</h3>
                </div>
                <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                  <li>GPT-4 & Claude</li>
                  <li>DeepSeek & Perplexity</li>
                  <li>ML Algorithms</li>
                  <li>NLP Processing</li>
                  <li>Sentiment Analysis</li>
                </ul>
              </div>

              {/* Applications */}
              <div className="text-center">
                <div className="bg-green-100 dark:bg-green-900/30 rounded-lg p-6 mb-4">
                  <Smartphone className="w-8 h-8 text-green-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-gray-900 dark:text-white">Applications</h3>
                </div>
                <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                  <li>Mobile App</li>
                  <li>MenuFlow</li>
                  <li>Connecto</li>
                  <li>Web Dashboard</li>
                  <li>API Access</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
} 