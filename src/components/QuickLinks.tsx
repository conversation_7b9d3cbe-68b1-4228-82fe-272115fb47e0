'use client';

import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  Layers, 
  Puzzle, 
  Brain, 
  Link as LinkIcon, 
  Building, 
  Users, 
  Code, 
  ArrowRight 
} from 'lucide-react';

const QuickLinks = () => {
  const t = useTranslations('nav');
  const pathname = usePathname();
  const currentLocale = pathname.split('/')[1] || 'en';

  const quickLinks = [
    {
      key: 'platform',
      href: '/platform',
      icon: Layers,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100 dark:bg-blue-900/30',
      description: 'Learn about our AI-powered platform architecture and core benefits'
    },
    {
      key: 'modules',
      href: '/modules',
      icon: Puzzle,
      color: 'text-green-600',
      bgColor: 'bg-green-100 dark:bg-green-900/30',
      description: 'Explore MenuFlow, Connecto, Mobile App, and Event Ticketing modules'
    },
    {
      key: 'ai',
      href: '/ai',
      icon: Brain,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100 dark:bg-purple-900/30',
      description: 'Discover how we leverage AI models and automation for business insights'
    },
    {
      key: 'integration',
      href: '/integration',
      icon: LinkIcon,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100 dark:bg-orange-900/30',
      description: 'Connect with POS systems, APIs, and third-party business tools'
    },
    {
      key: 'business',
      href: '/business',
      icon: Building,
      color: 'text-red-600',
      bgColor: 'bg-red-100 dark:bg-red-900/30',
      description: 'Use cases, onboarding guide, and demo requests for businesses'
    },
    {
      key: 'about',
      href: '/about',
      icon: Users,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100 dark:bg-indigo-900/30',
      description: 'Company overview, timeline, and contact information'
    },
    {
      key: 'developer',
      href: '/developer',
      icon: Code,
      color: 'text-pink-600',
      bgColor: 'bg-pink-100 dark:bg-pink-900/30',
      description: 'API reference, webhooks, SDKs, and technical documentation'
    },
  ];

  return (
    <section className="py-20 bg-white dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Explore the Documentation
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Navigate through our comprehensive documentation to understand every aspect of SME Analytica
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {quickLinks.map((link) => {
            const Icon = link.icon;
            return (
              <Link
                key={link.key}
                href={`/${currentLocale}${link.href}`}
                className="group bg-gray-50 dark:bg-gray-800 rounded-xl p-6 hover:bg-white dark:hover:bg-gray-700 transition-all duration-300 border border-gray-200 dark:border-gray-700 hover:shadow-lg hover:border-primary-300 dark:hover:border-primary-600"
              >
                <div className="flex items-start space-x-4">
                  <div className={`w-12 h-12 ${link.bgColor} rounded-lg flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform`}>
                    <Icon className={`w-6 h-6 ${link.color}`} />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-xl font-semibold text-gray-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                        {t(link.key as any)}
                      </h3>
                      <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-primary-600 dark:group-hover:text-primary-400 group-hover:translate-x-1 transition-all" />
                    </div>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      {link.description}
                    </p>
                  </div>
                </div>
              </Link>
            );
          })}
        </div>

        {/* Call to Action */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-primary-50 to-blue-50 dark:from-primary-900/20 dark:to-blue-900/20 rounded-2xl p-8 border border-primary-200 dark:border-primary-800">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Ready to Get Started?
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
              Join thousands of small and medium enterprises already using SME Analytica to make data-driven decisions
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="https://smeanalytica.dev"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-semibold rounded-lg transition-colors"
              >
                Try SME Analytica
                <ArrowRight className="w-4 h-4 ml-2" />
              </Link>
              <Link
                href={`/${currentLocale}/business`}
                className="inline-flex items-center px-6 py-3 bg-white dark:bg-gray-800 text-gray-900 dark:text-white font-semibold rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                Request Demo
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default QuickLinks; 