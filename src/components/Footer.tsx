'use client';

import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Twitter, Mail, Globe } from 'lucide-react';

const Footer = () => {
  const t = useTranslations();
  const pathname = usePathname();
  const currentLocale = pathname.split('/')[1] || 'en';

  const footerLinks = {
    platform: [
      { key: 'platform', href: '/platform' },
      { key: 'modules', href: '/modules' },
      { key: 'ai', href: '/ai' },
      { key: 'integration', href: '/integration' },
    ],
    resources: [
      { key: 'business', href: '/business' },
      { key: 'developer', href: '/developer' },
      { key: 'about', href: '/about' },
    ],
    external: [
      { name: 'Main Website', href: 'https://smeanalytica.dev' },
      { name: 'MenuFlow Demo', href: 'https://restaurants.smeanalytica.dev' },
      { name: 'Mobile App', href: 'https://smeanalytica.dev/mobile' },
    ]
  };

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">SA</span>
              </div>
              <span className="font-bold text-xl">SME Analytica</span>
            </div>
            <p className="text-gray-300 mb-4">
              Democratizing AI analytics for every small business. Empowering SMEs with enterprise-level insights.
            </p>
            <div className="flex space-x-4">
              <a
                href="https://twitter.com/smeanalytica"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-primary-400 transition-colors"
              >
                <Twitter className="w-5 h-5" />
              </a>
              <a
                href="mailto:<EMAIL>"
                className="text-gray-400 hover:text-primary-400 transition-colors"
              >
                <Mail className="w-5 h-5" />
              </a>
              <a
                href="https://smeanalytica.dev"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-primary-400 transition-colors"
              >
                <Globe className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* Platform Links */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Platform</h3>
            <ul className="space-y-2">
              {footerLinks.platform.map((link) => (
                <li key={link.key}>
                  <Link
                    href={`/${currentLocale}${link.href}`}
                    className="text-gray-300 hover:text-primary-400 transition-colors"
                  >
                    {t(`nav.${link.key}` as any)}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Resources Links */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Resources</h3>
            <ul className="space-y-2">
              {footerLinks.resources.map((link) => (
                <li key={link.key}>
                  <Link
                    href={`/${currentLocale}${link.href}`}
                    className="text-gray-300 hover:text-primary-400 transition-colors"
                  >
                    {t(`nav.${link.key}` as any)}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* External Links */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Products</h3>
            <ul className="space-y-2">
              {footerLinks.external.map((link) => (
                <li key={link.name}>
                  <a
                    href={link.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-300 hover:text-primary-400 transition-colors"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © {new Date().getFullYear()} SME Analytica. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <Link
                href={`/${currentLocale}/privacy`}
                className="text-gray-400 hover:text-primary-400 text-sm transition-colors"
              >
                Privacy Policy
              </Link>
              <Link
                href={`/${currentLocale}/terms`}
                className="text-gray-400 hover:text-primary-400 text-sm transition-colors"
              >
                Terms of Service
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer; 