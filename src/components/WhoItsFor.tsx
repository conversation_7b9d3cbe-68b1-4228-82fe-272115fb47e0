'use client';

import { useTranslations } from 'next-intl';
import { Store, Coffee, Heart, ShoppingBag } from 'lucide-react';

const WhoItsFor = () => {
  const t = useTranslations('home.whoItsFor');

  const industries = [
    {
      key: 'sme',
      icon: Store,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100 dark:bg-blue-900/30',
    },
    {
      key: 'hospitality',
      icon: Coffee,
      color: 'text-green-600',
      bgColor: 'bg-green-100 dark:bg-green-900/30',
    },
    {
      key: 'retail',
      icon: ShoppingBag,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100 dark:bg-purple-900/30',
    },
    {
      key: 'healthcare',
      icon: Heart,
      color: 'text-red-600',
      bgColor: 'bg-red-100 dark:bg-red-900/30',
    },
  ];

  return (
    <section className="py-20 bg-gray-50 dark:bg-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            {t('title')}
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            SME Analytica serves a wide range of industries, providing tailored AI insights for every business type.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {industries.map((industry) => {
            const Icon = industry.icon;
            return (
              <div
                key={industry.key}
                className="bg-white dark:bg-gray-900 rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow border border-gray-200 dark:border-gray-700"
              >
                <div className={`w-12 h-12 ${industry.bgColor} rounded-lg flex items-center justify-center mb-4`}>
                  <Icon className={`w-6 h-6 ${industry.color}`} />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                  {t(`${industry.key}.title` as any)}
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  {t(`${industry.key}.description` as any)}
                </p>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default WhoItsFor; 