<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>MenuFlow - A subsidiary of SME Analytica</title>
    <meta name="description" content="Digital menu and ordering system for restaurants" />
    <meta name="author" content="SME Analytica" />

    <meta property="og:title" content="MenuFlow" />
    <meta property="og:description" content="Digital menu and ordering system for restaurants" />
    <meta property="og:type" content="website" />

    <style>
      body {
        font-family: Arial, sans-serif;
        background-color: #171f31;
        color: #d5dce2;
        margin: 0;
        padding: 0;
      }
      #root {
        min-height: 100vh;
      }
    </style>
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    
    <!-- Quick Test Link - Only visible during development -->
    <div id="test-links" style="position: fixed; bottom: 20px; right: 20px; z-index: 9999; display: none;">
      <a href="/test-qr-menu" style="display: block; background-color: #0ea5e9; color: white; font-family: system-ui, sans-serif; padding: 8px 16px; border-radius: 4px; text-decoration: none; box-shadow: 0 2px 4px rgba(0,0,0,0.1); font-weight: 500;">
        Dynamic Pricing Test
      </a>
    </div>
    
    <script>
      // Only show test links in development
      if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        document.getElementById('test-links').style.display = 'block';
      }
    </script>
  </body>
</html>
