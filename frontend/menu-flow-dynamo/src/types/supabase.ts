export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      analyses: {
        Row: {
          business_name: string
          business_type: string | null
          completed_at: string | null
          created_at: string
          error_message: string | null
          id: string
          input_data: Json
          location: string | null
          result_data: Json | null
          status: string
          tokens_used: number | null
          type: string
          updated_at: string
          user_id: string
        }
        Insert: {
          business_name: string
          business_type?: string | null
          completed_at?: string | null
          created_at?: string
          error_message?: string | null
          id?: string
          input_data?: Json
          location?: string | null
          result_data?: Json | null
          status?: string
          tokens_used?: number | null
          type: string
          updated_at?: string
          user_id: string
        }
        Update: {
          business_name?: string
          business_type?: string | null
          completed_at?: string | null
          created_at?: string
          error_message?: string | null
          id?: string
          input_data?: Json
          location?: string | null
          result_data?: Json | null
          status?: string
          tokens_used?: number | null
          type?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_user_profile"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      analysis_limits: {
        Row: {
          frameworks: Json
          historical_data_months: number
          max_analyses_per_month: number
          max_competitors: number
          tier: string
          update_frequency: unknown
        }
        Insert: {
          frameworks: Json
          historical_data_months: number
          max_analyses_per_month: number
          max_competitors: number
          tier: string
          update_frequency: unknown
        }
        Update: {
          frameworks?: Json
          historical_data_months?: number
          max_analyses_per_month?: number
          max_competitors?: number
          tier?: string
          update_frequency?: unknown
        }
        Relationships: []
      }
      analysis_stats: {
        Row: {
          analysis_id: string
          created_at: string
          historical_data_months: number
          id: string
          max_analyses_per_month: number
          max_competitors: number
          update_frequency: unknown
          updated_at: string
          user_id: string
        }
        Insert: {
          analysis_id: string
          created_at?: string
          historical_data_months?: number
          id?: string
          max_analyses_per_month?: number
          max_competitors?: number
          update_frequency?: unknown
          updated_at?: string
          user_id: string
        }
        Update: {
          analysis_id?: string
          created_at?: string
          historical_data_months?: number
          id?: string
          max_analyses_per_month?: number
          max_competitors?: number
          update_frequency?: unknown
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_analysis"
            columns: ["analysis_id"]
            isOneToOne: false
            referencedRelation: "analyses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_user_profile"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      business_monitoring: {
        Row: {
          alert_config: Json
          business_id: string
          created_at: string
          id: string
          last_check: string
          next_check: string
          updated_at: string
          user_id: string
        }
        Insert: {
          alert_config?: Json
          business_id: string
          created_at?: string
          id?: string
          last_check?: string
          next_check?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          alert_config?: Json
          business_id?: string
          created_at?: string
          id?: string
          last_check?: string
          next_check?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "business_monitoring_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      growth_metrics: {
        Row: {
          analysis_id: string
          confidence_score: number | null
          created_at: string
          id: string
          insights: string[] | null
          metrics: Json
          recommendations: string[] | null
          updated_at: string
        }
        Insert: {
          analysis_id: string
          confidence_score?: number | null
          created_at?: string
          id?: string
          insights?: string[] | null
          metrics?: Json
          recommendations?: string[] | null
          updated_at?: string
        }
        Update: {
          analysis_id?: string
          confidence_score?: number | null
          created_at?: string
          id?: string
          insights?: string[] | null
          metrics?: Json
          recommendations?: string[] | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "growth_metrics_analysis_id_fkey"
            columns: ["analysis_id"]
            isOneToOne: false
            referencedRelation: "analyses"
            referencedColumns: ["id"]
          },
        ]
      }
      market_trends: {
        Row: {
          analysis_id: string
          created_at: string
          growth_rate: number | null
          id: string
          keyword: string
          source: string
          timestamp: string
          trend_value: number
          updated_at: string
        }
        Insert: {
          analysis_id: string
          created_at?: string
          growth_rate?: number | null
          id?: string
          keyword: string
          source: string
          timestamp?: string
          trend_value: number
          updated_at?: string
        }
        Update: {
          analysis_id?: string
          created_at?: string
          growth_rate?: number | null
          id?: string
          keyword?: string
          source?: string
          timestamp?: string
          trend_value?: number
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "market_trends_analysis_id_fkey"
            columns: ["analysis_id"]
            isOneToOne: false
            referencedRelation: "analyses"
            referencedColumns: ["id"]
          },
        ]
      }
      menu_items: {
        Row: {
          allergens: string[] | null
          base_price: number | null
          calories: number | null
          category: string
          created_at: string | null
          current_price: number
          description: string | null
          id: string
          image_url: string | null
          ingredients: string[] | null
          is_available: boolean | null
          menu_id: string
          name: string
          preparation_time: number | null
          price: number
          price_adjustment_factor: number | null
          price_history: Json | null
          restaurant_id: string | null
          updated_at: string | null
        }
        Insert: {
          allergens?: string[] | null
          base_price?: number | null
          calories?: number | null
          category: string
          created_at?: string | null
          current_price: number
          description?: string | null
          id?: string
          image_url?: string | null
          ingredients?: string[] | null
          is_available?: boolean | null
          menu_id: string
          name: string
          preparation_time?: number | null
          price?: number
          price_adjustment_factor?: number | null
          price_history?: Json | null
          restaurant_id?: string | null
          updated_at?: string | null
        }
        Update: {
          allergens?: string[] | null
          base_price?: number | null
          calories?: number | null
          category?: string
          created_at?: string | null
          current_price?: number
          description?: string | null
          id?: string
          image_url?: string | null
          ingredients?: string[] | null
          is_available?: boolean | null
          menu_id?: string
          name?: string
          preparation_time?: number | null
          price?: number
          price_adjustment_factor?: number | null
          price_history?: Json | null
          restaurant_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_menu"
            columns: ["menu_id"]
            isOneToOne: false
            referencedRelation: "menus"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "menu_items_restaurant_id_fkey"
            columns: ["restaurant_id"]
            isOneToOne: false
            referencedRelation: "restaurants"
            referencedColumns: ["id"]
          },
        ]
      }
      menu_table_assignments: {
        Row: {
          created_at: string
          id: string
          menu_id: string
          table_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          menu_id: string
          table_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          menu_id?: string
          table_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "menu_table_assignments_menu_id_fkey"
            columns: ["menu_id"]
            isOneToOne: false
            referencedRelation: "menus"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "menu_table_assignments_table_id_fkey"
            columns: ["table_id"]
            isOneToOne: false
            referencedRelation: "restaurant_tables"
            referencedColumns: ["id"]
          },
        ]
      }
      menus: {
        Row: {
          created_at: string | null
          days_available: number[] | null
          description: string | null
          end_time: string | null
          id: string
          is_active: boolean | null
          name: string
          restaurant_id: string
          start_time: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          days_available?: number[] | null
          description?: string | null
          end_time?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          restaurant_id: string
          start_time?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          days_available?: number[] | null
          description?: string | null
          end_time?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          restaurant_id?: string
          start_time?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_restaurant"
            columns: ["restaurant_id"]
            isOneToOne: false
            referencedRelation: "restaurants"
            referencedColumns: ["id"]
          },
        ]
      }
      notifications: {
        Row: {
          action_label: string | null
          action_url: string | null
          created_at: string | null
          id: string
          message: string
          priority: string | null
          read: boolean
          restaurant_id: string
          timestamp: string
          title: string
          type: string
          updated_at: string | null
        }
        Insert: {
          action_label?: string | null
          action_url?: string | null
          created_at?: string | null
          id?: string
          message: string
          priority?: string | null
          read?: boolean
          restaurant_id: string
          timestamp?: string
          title: string
          type: string
          updated_at?: string | null
        }
        Update: {
          action_label?: string | null
          action_url?: string | null
          created_at?: string | null
          id?: string
          message?: string
          priority?: string | null
          read?: boolean
          restaurant_id?: string
          timestamp?: string
          title?: string
          type?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "notifications_restaurant_id_fkey"
            columns: ["restaurant_id"]
            isOneToOne: false
            referencedRelation: "restaurants"
            referencedColumns: ["id"]
          },
        ]
      }
      order_items: {
        Row: {
          created_at: string | null
          id: string
          menu_item_id: string
          order_id: string
          quantity: number
          special_instructions: string | null
          status: string
          unit_price: number
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          menu_item_id: string
          order_id: string
          quantity: number
          special_instructions?: string | null
          status?: string
          unit_price: number
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          menu_item_id?: string
          order_id?: string
          quantity?: number
          special_instructions?: string | null
          status?: string
          unit_price?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_menu_item"
            columns: ["menu_item_id"]
            isOneToOne: false
            referencedRelation: "menu_items"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_menu_item"
            columns: ["menu_item_id"]
            isOneToOne: false
            referencedRelation: "menu_sales_analytics"
            referencedColumns: ["menu_item_id"]
          },
          {
            foreignKeyName: "fk_order"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["id"]
          },
        ]
      }
      orders: {
        Row: {
          completed_at: string | null
          created_at: string | null
          customer_session_id: string | null
          id: string
          restaurant_id: string
          special_requests: string | null
          status: string
          table_id: string
          total_amount: number
          updated_at: string | null
        }
        Insert: {
          completed_at?: string | null
          created_at?: string | null
          customer_session_id?: string | null
          id?: string
          restaurant_id: string
          special_requests?: string | null
          status?: string
          table_id: string
          total_amount: number
          updated_at?: string | null
        }
        Update: {
          completed_at?: string | null
          created_at?: string | null
          customer_session_id?: string | null
          id?: string
          restaurant_id?: string
          special_requests?: string | null
          status?: string
          table_id?: string
          total_amount?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_restaurant"
            columns: ["restaurant_id"]
            isOneToOne: false
            referencedRelation: "restaurants"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_table"
            columns: ["table_id"]
            isOneToOne: false
            referencedRelation: "restaurant_tables"
            referencedColumns: ["id"]
          },
        ]
      }
      price_adjustment_performance: {
        Row: {
          adjusted_at: string | null
          adjustment_percentage: number
          adjustment_reason: string | null
          id: string
          menu_item_id: string
          new_price: number
          occupancy_percentage: number | null
          performance_measured_at: string | null
          previous_price: number
          restaurant_id: string
          revenue_impact: number | null
          sales_after: number | null
          sales_before: number | null
          traffic_level: string | null
        }
        Insert: {
          adjusted_at?: string | null
          adjustment_percentage: number
          adjustment_reason?: string | null
          id?: string
          menu_item_id: string
          new_price: number
          occupancy_percentage?: number | null
          performance_measured_at?: string | null
          previous_price: number
          restaurant_id: string
          revenue_impact?: number | null
          sales_after?: number | null
          sales_before?: number | null
          traffic_level?: string | null
        }
        Update: {
          adjusted_at?: string | null
          adjustment_percentage?: number
          adjustment_reason?: string | null
          id?: string
          menu_item_id?: string
          new_price?: number
          occupancy_percentage?: number | null
          performance_measured_at?: string | null
          previous_price?: number
          restaurant_id?: string
          revenue_impact?: number | null
          sales_after?: number | null
          sales_before?: number | null
          traffic_level?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "price_adjustment_performance_menu_item_id_fkey"
            columns: ["menu_item_id"]
            isOneToOne: false
            referencedRelation: "menu_items"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "price_adjustment_performance_menu_item_id_fkey"
            columns: ["menu_item_id"]
            isOneToOne: false
            referencedRelation: "menu_sales_analytics"
            referencedColumns: ["menu_item_id"]
          },
          {
            foreignKeyName: "price_adjustment_performance_restaurant_id_fkey"
            columns: ["restaurant_id"]
            isOneToOne: false
            referencedRelation: "restaurants"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          avatar_url: string | null
          banner_url: string | null
          business_name: string | null
          business_type: string | null
          created_at: string
          email: string
          full_name: string | null
          id: string
          last_sign_in_at: string | null
          location: string | null
          subscription_status: string
          subscription_tier: string
          subscription_updated_at: string
          updated_at: string
        }
        Insert: {
          avatar_url?: string | null
          banner_url?: string | null
          business_name?: string | null
          business_type?: string | null
          created_at?: string
          email: string
          full_name?: string | null
          id: string
          last_sign_in_at?: string | null
          location?: string | null
          subscription_status?: string
          subscription_tier?: string
          subscription_updated_at?: string
          updated_at?: string
        }
        Update: {
          avatar_url?: string | null
          banner_url?: string | null
          business_name?: string | null
          business_type?: string | null
          created_at?: string
          email?: string
          full_name?: string | null
          id?: string
          last_sign_in_at?: string | null
          location?: string | null
          subscription_status?: string
          subscription_tier?: string
          subscription_updated_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      restaurant_staff: {
        Row: {
          created_at: string
          id: string
          restaurant_id: string
          role: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          restaurant_id: string
          role: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          restaurant_id?: string
          role?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "restaurant_staff_restaurant_id_fkey"
            columns: ["restaurant_id"]
            isOneToOne: false
            referencedRelation: "restaurants"
            referencedColumns: ["id"]
          },
        ]
      }
      restaurant_subscriptions: {
        Row: {
          created_at: string | null
          current_period_end: string | null
          id: string
          restaurant_id: string
          status: string
          stripe_customer_id: string
          stripe_subscription_id: string
          trial_end: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          current_period_end?: string | null
          id?: string
          restaurant_id: string
          status: string
          stripe_customer_id: string
          stripe_subscription_id: string
          trial_end?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          current_period_end?: string | null
          id?: string
          restaurant_id?: string
          status?: string
          stripe_customer_id?: string
          stripe_subscription_id?: string
          trial_end?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      restaurant_tables: {
        Row: {
          capacity: number | null
          created_at: string | null
          id: string
          is_occupied: boolean | null
          location: string | null
          qr_code_url: string | null
          restaurant_id: string
          status: string | null
          table_number: string
          updated_at: string | null
        }
        Insert: {
          capacity?: number | null
          created_at?: string | null
          id?: string
          is_occupied?: boolean | null
          location?: string | null
          qr_code_url?: string | null
          restaurant_id: string
          status?: string | null
          table_number: string
          updated_at?: string | null
        }
        Update: {
          capacity?: number | null
          created_at?: string | null
          id?: string
          is_occupied?: boolean | null
          location?: string | null
          qr_code_url?: string | null
          restaurant_id?: string
          status?: string | null
          table_number?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_restaurant"
            columns: ["restaurant_id"]
            isOneToOne: false
            referencedRelation: "restaurants"
            referencedColumns: ["id"]
          },
        ]
      }
      restaurants: {
        Row: {
          address: string
          contact_email: string
          contact_phone: string | null
          created_at: string | null
          description: string | null
          dynamic_pricing_enabled: boolean | null
          id: string
          is_active: boolean | null
          logo_url: string | null
          name: string
          opening_hours: Json | null
          pricing_rules: Json | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          address: string
          contact_email: string
          contact_phone?: string | null
          created_at?: string | null
          description?: string | null
          dynamic_pricing_enabled?: boolean | null
          id?: string
          is_active?: boolean | null
          logo_url?: string | null
          name: string
          opening_hours?: Json | null
          pricing_rules?: Json | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          address?: string
          contact_email?: string
          contact_phone?: string | null
          created_at?: string | null
          description?: string | null
          dynamic_pricing_enabled?: boolean | null
          id?: string
          is_active?: boolean | null
          logo_url?: string | null
          name?: string
          opening_hours?: Json | null
          pricing_rules?: Json | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      strategy_recommendations: {
        Row: {
          analysis_id: string
          confidence_score: number | null
          created_at: string
          estimated_roi: number | null
          id: string
          impact_areas: string[] | null
          implementation_timeline: string | null
          priority: string | null
          recommendations: Json
          updated_at: string
        }
        Insert: {
          analysis_id: string
          confidence_score?: number | null
          created_at?: string
          estimated_roi?: number | null
          id?: string
          impact_areas?: string[] | null
          implementation_timeline?: string | null
          priority?: string | null
          recommendations?: Json
          updated_at?: string
        }
        Update: {
          analysis_id?: string
          confidence_score?: number | null
          created_at?: string
          estimated_roi?: number | null
          id?: string
          impact_areas?: string[] | null
          implementation_timeline?: string | null
          priority?: string | null
          recommendations?: Json
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "strategy_recommendations_analysis_id_fkey"
            columns: ["analysis_id"]
            isOneToOne: false
            referencedRelation: "analyses"
            referencedColumns: ["id"]
          },
        ]
      }
      subscription_tiers: {
        Row: {
          created_at: string
          max_analyses_per_month: number
          max_concurrent_analyses: number
          max_tokens_per_analysis: number
          tier: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          max_analyses_per_month?: number
          max_concurrent_analyses?: number
          max_tokens_per_analysis?: number
          tier: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          max_analyses_per_month?: number
          max_concurrent_analyses?: number
          max_tokens_per_analysis?: number
          tier?: string
          updated_at?: string
        }
        Relationships: []
      }
      traffic_heatmap: {
        Row: {
          active_tables: number | null
          confidence: number | null
          created_at: string | null
          day_of_week: number
          hour_of_day: number | null
          id: string
          occupancy_percentage: number | null
          restaurant_id: string
          source: string | null
          timestamp: string | null
          total_tables: number | null
          traffic_level: number | null
        }
        Insert: {
          active_tables?: number | null
          confidence?: number | null
          created_at?: string | null
          day_of_week: number
          hour_of_day?: number | null
          id?: string
          occupancy_percentage?: number | null
          restaurant_id: string
          source?: string | null
          timestamp?: string | null
          total_tables?: number | null
          traffic_level?: number | null
        }
        Update: {
          active_tables?: number | null
          confidence?: number | null
          created_at?: string | null
          day_of_week?: number
          hour_of_day?: number | null
          id?: string
          occupancy_percentage?: number | null
          restaurant_id?: string
          source?: string | null
          timestamp?: string | null
          total_tables?: number | null
          traffic_level?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_restaurant"
            columns: ["restaurant_id"]
            isOneToOne: false
            referencedRelation: "restaurants"
            referencedColumns: ["id"]
          },
        ]
      }
      traffic_sensors: {
        Row: {
          created_at: string | null
          id: string
          latest_reading: number
          location: string
          restaurant_id: string
          sensor_type: string
          status: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          latest_reading: number
          location: string
          restaurant_id: string
          sensor_type: string
          status?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          latest_reading?: number
          location?: string
          restaurant_id?: string
          sensor_type?: string
          status?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "traffic_sensors_restaurant_id_fkey"
            columns: ["restaurant_id"]
            isOneToOne: false
            referencedRelation: "restaurants"
            referencedColumns: ["id"]
          },
        ]
      }
      user_profiles: {
        Row: {
          avatar_url: string | null
          business_name: string | null
          created_at: string
          email: string | null
          full_name: string | null
          id: string
          location: string | null
          updated_at: string
        }
        Insert: {
          avatar_url?: string | null
          business_name?: string | null
          created_at?: string
          email?: string | null
          full_name?: string | null
          id: string
          location?: string | null
          updated_at?: string
        }
        Update: {
          avatar_url?: string | null
          business_name?: string | null
          created_at?: string
          email?: string | null
          full_name?: string | null
          id?: string
          location?: string | null
          updated_at?: string
        }
        Relationships: []
      }
    }
    Views: {
      menu_sales_analytics: {
        Row: {
          average_price: number | null
          category: string | null
          current_price: number | null
          day_of_week: number | null
          item_name: string | null
          menu_item_id: string | null
          order_count: number | null
          order_date: string | null
          order_hour: number | null
          restaurant_id: string | null
          total_quantity: number | null
          total_sales: number | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_restaurant"
            columns: ["restaurant_id"]
            isOneToOne: false
            referencedRelation: "restaurants"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Functions: {
      create_missing_profiles: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      debug_auth_flow: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      fix_auth_issues: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_menu_item_analytics: {
        Args: { restaurant_id_param: string; time_filter: string }
        Returns: {
          id: string
          name: string
          category: string
          base_price: number
          current_price: number
          orders_count: number
          total_sales: number
          popularity_score: number
          price_elasticity: number
        }[]
      }
      record_price_adjustment: {
        Args: {
          p_restaurant_id: string
          p_menu_item_id: string
          p_previous_price: number
          p_new_price: number
          p_adjustment_percentage: number
          p_adjustment_reason: string
          p_traffic_level: string
          p_occupancy_percentage: number
        }
        Returns: string
      }
      update_price_adjustment_performance: {
        Args: { adjustment_id: string }
        Returns: boolean
      }
    }
    Enums: {
      metric_type:
        | "revenue_growth"
        | "customer_acquisition"
        | "customer_retention"
        | "conversion_rate"
        | "order_value"
        | "marketing_roi"
        | "website_traffic"
        | "social_engagement"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      metric_type: [
        "revenue_growth",
        "customer_acquisition",
        "customer_retention",
        "conversion_rate",
        "order_value",
        "marketing_roi",
        "website_traffic",
        "social_engagement",
      ],
    },
  },
} as const
