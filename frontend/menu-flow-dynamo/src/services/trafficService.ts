import { supabase } from '@/integrations/supabase/client';
import { getRestaurantId } from './restaurantDbService';
import { User } from '@supabase/supabase-js';
import { PostgrestError } from '@supabase/supabase-js';
import { trafficLogger } from '@/utils/debugLogger';

// Define database table column types to fix TypeScript errors
// Custom database type definitions for traffic/restaurant tables
interface TrafficSensor {
  id: string;
  restaurant_id: string;
  latest_reading: number;
  updated_at: string;
  sensor_type: string;
  location: string;
  status: string;
}
/**
 * Record a traffic event (e.g., menu_view, qr_scan) by inserting a data point.
 */
export const recordTrafficEvent = async (
  eventType: 'menu_view' | 'qr_scan',
  restaurantId?: string
): Promise<void> => {
  // Determine restaurant ID if not provided
  let restId = restaurantId;
  if (!restId) {
    restId = await getRestaurantId();
  }
  if (!restId) {
    console.warn('No restaurant ID available for recording traffic event');
    return;
  }
  try {
    // Insert minimal entry into traffic_heatmap to record the event
    const now = new Date();
    const dayOfWeek = now.getDay();  // 0 = Sunday
    const hourOfDay = now.getHours();
    const { error } = await supabase
      .from<TrafficHeatmapRow>('traffic_heatmap')
      .insert({
        restaurant_id: restId,
        timestamp: now.toISOString(),
        day_of_week: dayOfWeek,
        hour_of_day: hourOfDay,
        occupancy_percentage: 0,
        source: eventType
      });
    if (error) {
      console.error('Error recording traffic event:', error);
    }
  } catch (e) {
    console.error('Exception recording traffic event:', e);
  }
};

interface TrafficHeatmapRow {
  id: string;
  restaurant_id: string;
  timestamp: string;
  day_of_week: number; // 0-6 integer (0 = Sunday)
  hour_of_day: number; // 0-23 integer
  occupancy_percentage: number; // 0-100 numeric value
  created_at: string;
  // Optional columns
  active_tables?: number;
  total_tables?: number;
  source?: 'orders' | 'manual' | 'sensors' | 'estimated';
  confidence?: number;
}

interface RestaurantTable {
  id: string;
  restaurant_id: string;
  status: string;
  capacity: number;
  table_number: string;
  created_at: string;
}

interface Order {
  id: string;
  restaurant_id: string;
  table_id: string;
  status: string;
  total: number;
  created_at: string;
  completed_at: string | null;
}

/**
 * Interface for traffic data point
 */
interface TrafficDataPoint {
  timestamp: string;
  occupancy_percentage: number;
  active_tables: number;
  total_tables: number;
  source: 'orders' | 'manual' | 'sensors' | 'estimated';
  confidence: number;
}

/**
 * Traffic data with aggregated information
 */
export interface TrafficData {
  current_occupancy: number; // 0-1 value
  active_tables: number;
  total_tables: number;
  hourly_trend: TrafficDataPoint[];
  daily_trend: {
    day: string;
    average_occupancy: number;
  }[];
  last_updated: string;
  confidence: number; // 0-1 value indicating data reliability
}

/**
 * Get current restaurant traffic from multiple sources
 */
export const getCurrentTraffic = async (
  restaurantId?: string,
  user?: User | null
): Promise<number> => {
  // Ensure we have a restaurant ID
  if (!restaurantId && user) {
    restaurantId = await getRestaurantId(user);
  }
  
  if (!restaurantId) {
    console.error('No restaurant ID provided for traffic data');
    return 0;
  }
  
  try {
    // First check real table occupancy data
    let realTableOccupancy = -1;
    try {
      const tableData = await getTrafficFromTables(restaurantId);
      if (tableData.value >= 0) {
        realTableOccupancy = tableData.value;
        console.log(`🏢 Real table occupancy data: ${Math.round(realTableOccupancy * 100)}%`);
      }
    } catch (tableError) {
      console.warn('Could not fetch real table occupancy data:', tableError);
    }
    
    // Check for enhanced traffic simulation data that includes both table and sensor data
    // Using localStorage instead of sessionStorage to share across tabs/windows
    const simulatedDataStr = localStorage.getItem('restaurant_traffic_data') || sessionStorage.getItem('restaurant_traffic_data');
    if (simulatedDataStr) {
      try {
        const simulatedData = JSON.parse(simulatedDataStr);
        if (simulatedData.restaurant_id === restaurantId) {
          const simulationTime = new Date(simulatedData.timestamp).getTime();
          const currentTime = Date.now();
          const oneHourMs = 60 * 60 * 1000;
          
          if (currentTime - simulationTime < oneHourMs) {
            // Calculate the effective traffic level using the weighted formula from the simulator
            const tableWeight = 0.7;
            const sensorWeight = 0.3;
            
            // Use simulated table data
            let tableOccupancy = simulatedData.table_occupancy || simulatedData.traffic_level;
            
            // If real table data is available and shows high occupancy, blend it with mock data
            // This ensures during demos that if tables are actually occupied, it will influence the result
            if (realTableOccupancy >= 0) {
              // If real table occupancy is higher than simulated, increase its influence
              if (realTableOccupancy > tableOccupancy) {
                // Blend them, favoring the higher value
                tableOccupancy = (tableOccupancy * 0.4) + (realTableOccupancy * 0.6);
                console.log(`🔀 Blending with REAL table data (higher): ${Math.round(realTableOccupancy * 100)}%`);
              } else {
                // Still blend but favor the mock data more
                tableOccupancy = (tableOccupancy * 0.8) + (realTableOccupancy * 0.2);
                console.log(`🔀 Blending with real table data: ${Math.round(realTableOccupancy * 100)}%`);
              }
            }
            
            const sensorReading = simulatedData.sensor_reading || simulatedData.traffic_level;
            
            const combinedTraffic = (tableOccupancy * tableWeight) + (sensorReading * sensorWeight);
            console.log(`🏙️ Using enhanced simulation: Tables=${Math.round(tableOccupancy * 100)}%, Sensors=${Math.round(sensorReading * 100)}%`);
            console.log(`⚖️ Combined traffic (weighted): ${Math.round(combinedTraffic * 100)}%`);
            
            return combinedTraffic;
          }
        }
      } catch (e) {
        console.error('Error parsing traffic simulation data:', e);
      }
    }
    
    // If no simulation is active, proceed with normal traffic data sources
    // Check if we have any traffic sensors first
    const { value: sensorValue, weight: sensorWeight } = await getTrafficFromSensors(restaurantId);
    
    // If sensors have good data, prioritize them
    if (sensorWeight > 0.7) {
      return sensorValue;
    }
    
    // Otherwise combine multiple data sources with weighted average
    const { value: tableValue, weight: tableWeight } = await getTrafficFromTables(restaurantId);
    const { value: orderValue, weight: orderWeight } = await getTrafficFromActiveOrders(restaurantId);
    const { value: historicalValue, weight: historicalWeight } = await getTrafficFromHistorical(restaurantId);
    
    // Calculate weighted average of all available data sources
    const totalWeight = sensorWeight + tableWeight + orderWeight + historicalWeight;
    
    // If no weights, use historical only
    if (totalWeight === 0) {
      console.log('🔍 No traffic data sources available, using estimated value');
      return getEstimatedTraffic();
    }
    
    const weightedTraffic = (
      (sensorValue * sensorWeight) +
      (tableValue * tableWeight) +
      (orderValue * orderWeight) +
      (historicalValue * historicalWeight)
    ) / totalWeight;
    
    console.log('📊 Traffic data sources:', {
      sensor: { value: sensorValue, weight: sensorWeight },
      tables: { value: tableValue, weight: tableWeight },
      orders: { value: orderValue, weight: orderWeight },
      historical: { value: historicalValue, weight: historicalWeight },
      weighted: weightedTraffic
    });
    
    // Record this data point for future reference (improves historical patterns)
    recordTrafficDataPoint(restaurantId, weightedTraffic).catch((error) => {
      console.error('Error recording traffic data:', error);
    });
    
    return weightedTraffic;
  } catch (error) {
    console.error('Error getting traffic data:', error);
    return getEstimatedTraffic();
  }
};

/**
 * Get traffic from active orders
 */
const getTrafficFromActiveOrders = async (restaurantId: string): Promise<{value: number, weight: number}> => {
  try {
    // Get active orders (not completed or cancelled)
    const { data: orders, error } = await supabase
      .from('orders')
      .select('id, table_id, status')
      .eq('restaurant_id', restaurantId)
      .in('status', ['pending', 'preparing', 'ready', 'delivered'])
      .is('completed_at', null);
    
    if (error) throw error;
    
    if (!orders || orders.length === 0) {
      console.log('No active orders found for traffic calculation');
      return { value: -1, weight: 0 }; // No data
    }
    
    // Get total number of tables to calculate occupancy
    const { data: tables, error: tablesError } = await supabase
      .from('restaurant_tables')
      .select('id')
      .eq('restaurant_id', restaurantId);
      
    if (tablesError) throw tablesError;
    
    if (!tables || tables.length === 0) {
      return { value: -1, weight: 0 }; // No table data
    }
    
    // Count unique tables with active orders
    const tablesWithOrders = new Set(orders.map(order => order.table_id));
    const occupancy = tables.length > 0 ? tablesWithOrders.size / tables.length : 0;
    
    console.log(`Traffic from orders: ${Math.round(occupancy * 100)}% (${tablesWithOrders.size}/${tables.length} tables)`);
    
    // Order data is supplementary to table status - lower weight
    return { value: occupancy, weight: 2.0 };
  } catch (error) {
    console.error('Error getting traffic from orders:', error);
    return { value: -1, weight: 0 };
  }
};

/**
 * Get traffic from table management system
 */
const getTrafficFromTables = async (restaurantId: string): Promise<{value: number, weight: number}> => {
  try {
    // Get tables and their status
    const { data: tables, error } = await supabase
      .from('restaurant_tables')
      .select('id, status')
      .eq('restaurant_id', restaurantId);
      
    if (error) throw error;
    
    if (!tables || tables.length === 0) {
      return { value: -1, weight: 0 }; // No data
    }
    
    // Count occupied tables
    const occupiedTables = tables.filter(table => table.status === 'occupied');
    const occupancy = tables.length > 0 ? occupiedTables.length / tables.length : 0;
    
    console.log(`Traffic from table management: ${Math.round(occupancy * 100)}% (${occupiedTables.length}/${tables.length} tables)`);
    
    // Table status is the primary indicator of restaurant traffic - highest weight
    return { value: occupancy, weight: 6.0 };
  } catch (error) {
    console.error('Error getting traffic from tables:', error);
    return { value: -1, weight: 0 };
  }
};

/**
 * Get traffic from IoT sensors or other data sources (if available)
 */
const getTrafficFromSensors = async (restaurantId: string): Promise<{value: number, weight: number}> => {
  try {
    // Use Promise.resolve to handle the case where traffic_sensors table might not exist
    const sensorData = async () => {
      try {
        // Use a type assertion to handle traffic_sensors table which might not be in the generated types
        return await (supabase
          .from('traffic_sensors' as any)
          .select('latest_reading, updated_at')
          .eq('restaurant_id', restaurantId)
          .order('updated_at', { ascending: false })
          .limit(10));
      } catch (e) {
        console.log('Error accessing traffic sensors, might not exist yet:', e);
        return { data: null, error: e };
      }
    };
    
    const { data: sensors, error } = await sensorData();
      
    if (error) {
      // Table might not exist yet
      console.log('No sensor data available (table might not exist)');
      return { value: -1, weight: 0 };
    }
    
    if (!sensors || sensors.length === 0) {
      return { value: -1, weight: 0 }; // No data
    }
    
    // Type assertion to handle TS errors due to potential schema discrepancy
    const sensor = sensors[0] as { latest_reading: number; updated_at: string };
    
    // Check if data is fresh (less than 15 minutes old)
    const updatedAt = new Date(sensor.updated_at);
    const now = new Date();
    const ageInMinutes = (now.getTime() - updatedAt.getTime()) / (1000 * 60);
    
    if (ageInMinutes > 15) {
      console.log(`Sensor data is too old (${Math.round(ageInMinutes)} minutes)`);
      return { value: -1, weight: 0 };
    }
    
    console.log(`Traffic from sensors: ${Math.round(sensor.latest_reading * 100)}%`);
    
    // Sensor data is very reliable if recent
    return { value: sensor.latest_reading, weight: 4.5 };
  } catch (error) {
    console.error('Error getting traffic from sensors:', error);
    return { value: -1, weight: 0 };
  }
};

/**
 * Get traffic from historical patterns
 */
const getTrafficFromHistorical = async (restaurantId: string): Promise<{value: number, weight: number}> => {
  try {
    const now = new Date();
    const dayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const hourOfDay = now.getHours();
    
    // Get historical traffic data for this day/hour
    const { data: historical, error } = await supabase
      .from('traffic_heatmap')
      .select('occupancy_percentage')
      .eq('restaurant_id', restaurantId)
      .eq('day_of_week', dayOfWeek)
      .eq('hour', hourOfDay)
      .order('timestamp', { ascending: false })
      .limit(5); // Last 4 weeks of data
      
    if (error) {
      // Table might not exist yet
      console.log('No historical data available (table might not exist)');
      return { value: -1, weight: 0 };
    }
    
    if (!historical || historical.length === 0) {
      return { value: -1, weight: 0 }; // No data
    }
    
    // Average of historical occupancy
    const averageOccupancy = historical.reduce((sum, item) => sum + item.occupancy_percentage, 0) / historical.length;
    
    console.log(`Traffic from historical data: ${Math.round(averageOccupancy * 100)}% (based on ${historical.length} data points)`);
    
    // Historical data is less reliable than real-time data
    // But better than pure estimates
    return { value: averageOccupancy, weight: 2.0 };
  } catch (error) {
    console.error('Error getting traffic from historical data:', error);
    return { value: -1, weight: 0 };
  }
};

/**
 * Record current traffic data point
 * 
 * This function is modified to gracefully handle RLS errors and permissions issues
 * to prevent disrupting the user experience when RLS policies are in effect
 */
const recordTrafficDataPoint = async (restaurantId: string, occupancyPercentage: number): Promise<void> => {
  try {
    
    // Store in session storage regardless of database capability
    // This provides data for the current session even if DB writes fail
    try {
      const trafficData = JSON.parse(sessionStorage.getItem('localTrafficData') || '[]');
      const now = new Date();
      trafficData.push({
        timestamp: now.toISOString(),
        occupancy_percentage: occupancyPercentage,
        restaurant_id: restaurantId
      });
      // Only keep last 100 entries
      if (trafficData.length > 100) trafficData.shift();
      sessionStorage.setItem('localTrafficData', JSON.stringify(trafficData));
    } catch (e) {
      console.log('📊 Error storing traffic data in session:', e);
    }
    
    // For menu-flow-dynamo (customer-facing app), we don't access traffic_heatmap directly
  // Instead, we collect data in session storage and use that for our calculations
  try {
    // Always collect traffic data in session storage for local use
    const now = new Date();
    const timestamp = now.toISOString();
    const dayOfWeek = now.getDay();
    const hour = now.getHours();
    
    console.log('📊 Menu app collecting traffic data for local use only');
    
    // Store traffic metrics in a local analytics stream that's browser-only
    // We'll read from this for dynamic pricing on the customer side
    const localAnalytics = {
      timestamp,
      restaurant_id: restaurantId,
      occupancy_percentage: occupancyPercentage,
      day_of_week: dayOfWeek,
      hour_of_day: hour
    };
    
    // Store in sessionStorage for dynamic pricing calculations
    sessionStorage.setItem('latestTrafficData', JSON.stringify(localAnalytics));
    
    // Don't attempt to write to traffic_heatmap directly from customer app
    // Instead, use a serverless function or store in a customer events table
    console.log('📊 Customer app doesn\'t write to traffic_heatmap directly');
    
    // In a production environment, we'd call a serverless function like this:
    // await fetch('/api/record-traffic-analytics', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(localAnalytics)
    // });
    
    // For simplicity in this implementation, we won't attempt to write to database from customer app
    // The restaurant dashboard app would handle traffic_heatmap writes with proper authentication
    } catch (e) {
      // Just log the error, don't rethrow - this should never break the app experience
      console.log('📊 Error recording traffic data:', e);
    }
  } catch (error) {
    // Top-level error catch - absolutely should not disrupt the app
    console.error('📊 Unexpected error in traffic recording:', error);
  }
};

/**
 * Get estimated traffic based on time of day and day of week
 * This is a fallback when no real data is available
 */
const getEstimatedTraffic = (): number => {
  // This is a placeholder - in production, this would connect to actual restaurant 
  // occupancy data, table management system, or other traffic sensors
  const now = new Date();
  const hour = now.getHours();
  const day = now.getDay();
  
  // Weekend (higher traffic)
  if (day === 0 || day === 6) {
    if (hour >= 12 && hour <= 14) return 0.9; // Weekend lunch
    if (hour >= 18 && hour <= 21) return 0.95; // Weekend dinner
    if (hour >= 9 && hour <= 23) return 0.7; // Other weekend hours
    return 0.3; // Early morning/late night
  }
  
  // Weekday
  if (hour >= 12 && hour <= 13) return 0.8; // Weekday lunch rush
  if (hour >= 18 && hour <= 20) return 0.75; // Weekday dinner
  if (hour >= 7 && hour <= 9) return 0.6; // Breakfast
  if (hour >= 9 && hour <= 23) return 0.4; // Other business hours
  return 0.1; // Early morning/late night
};

/**
 * Get detailed traffic data for display in analytics dashboard
 */
/**
 * Update table status based on order event
 * @param tableId The table ID to update
 * @param status The new status ('occupied' | 'available' | 'reserved')
 */
export const updateTableStatus = async (tableId: string, status: 'occupied' | 'available' | 'reserved'): Promise<void> => {
  try {
    console.log(`[TrafficService] Updating table ${tableId} status to ${status}`);
    
    // Set is_occupied field for backward compatibility
    const isOccupied = status === 'occupied';
    
    // Update both status and is_occupied fields for full compatibility
    const { data, error } = await supabase
      .from('restaurant_tables')
      .update({ 
        status: status,
        is_occupied: isOccupied,
        updated_at: new Date().toISOString()
      })
      .eq('id', tableId)
      .select('*');

    if (error) {
      console.error('[TrafficService] Error updating table status:', error);
      throw error;
    }
    
    if (!data || data.length === 0) {
      console.warn('[TrafficService] Table updated but no data returned');
    } else {
      console.log(`[TrafficService] Table ${tableId} status updated to ${status}. Table number: ${data[0]?.table_number}`);
    }
    
    // Force refresh analytics data
    await refreshTrafficData(tableId);
  } catch (error) {
    console.error('[TrafficService] Failed to update table status:', error);
    throw error; // Re-throw so calling code knows something went wrong
  }
};

/**
 * Force refresh traffic data after a table status change
 * @param tableId The table ID that was updated
 */
async function refreshTrafficData(tableId: string): Promise<void> {
  try {
    // Get restaurant ID from the table
    const { data, error } = await supabase
      .from('restaurant_tables')
      .select('restaurant_id')
      .eq('id', tableId)
      .single();
      
    if (error || !data) {
      console.error('[TrafficService] Error getting restaurant ID for refresh:', error);
      return;
    }
    
    // Trigger a new traffic data point to be recorded
    const restaurantId = data.restaurant_id;
    console.log(`[TrafficService] Refreshing traffic data for restaurant ${restaurantId}`);
    
    // Get current occupancy and record it
    const occupancy = await getCurrentTraffic(restaurantId);
    await recordTrafficDataPoint(restaurantId, occupancy);
    
    console.log(`[TrafficService] Traffic data refreshed. Current occupancy: ${occupancy}`);
  } catch (error) {
    console.error('[TrafficService] Error refreshing traffic data:', error);
  }
};

/**
 * Automatically mark a table as occupied when an order is created
 * @param orderId The order ID that was just created
 */
export const markTableOccupiedForOrder = async (orderId: string): Promise<void> => {
  try {
    // Get the order to find the associated table
    const { data: order, error } = await supabase
      .from('orders')
      .select('table_id')
      .eq('id', orderId)
      .single();

    if (error) throw error;
    if (!order) throw new Error('Order not found');

    // Update the table status to occupied
    await updateTableStatus(order.table_id, 'occupied');
  } catch (error) {
    console.error('Error marking table as occupied:', error);
  }
};

/**
 * Mark a table as available when an order is completed
 * @param orderId The order ID that was just completed
 */
export const markTableAvailableAfterOrder = async (orderId: string): Promise<void> => {
  try {
    // Get the order to find the associated table
    const { data: order, error } = await supabase
      .from('orders')
      .select('table_id')
      .eq('id', orderId)
      .single();

    if (error) throw error;
    if (!order) throw new Error('Order not found');

    // Check if there are any other active orders for this table
    const { data: activeOrders, error: activeOrdersError } = await supabase
      .from('orders')
      .select('id')
      .eq('table_id', order.table_id)
      .in('status', ['pending', 'preparing', 'ready'])
      .is('completed_at', null);

    if (activeOrdersError) throw activeOrdersError;

    // Only mark as available if there are no other active orders for this table
    if (!activeOrders || activeOrders.length === 0) {
      await updateTableStatus(order.table_id, 'available');
    }
  } catch (error) {
    console.error('Error marking table as available:', error);
  }
};

export const getTrafficData = async (
  restaurantId?: string,
  user?: User | null
): Promise<TrafficData | null> => {
  try {
    // Get restaurant ID if not provided
    const targetRestaurantId = restaurantId || await getRestaurantId(user);
    
    if (!targetRestaurantId) {
      console.error('No restaurant ID found for traffic data');
      return null;
    }
    
    // Get current occupancy
    const currentOccupancy = await getCurrentTraffic(targetRestaurantId);
    
    // Get table data safely with error handling
    const tableResult = await Promise.resolve().then(async () => {
      try {
        return await supabase
          .from('restaurant_tables')
          .select('id, status')
          .eq('restaurant_id', targetRestaurantId);
      } catch (e) {
        console.error('Error accessing restaurant_tables:', e);
        return { data: [], error: e };
      }
    });
    
    const tables = tableResult.data || [];
    
    // Type assertion to handle potential schema mismatch
    const typedTables = tables as { id: string; status: string }[];
    
    const totalTables = typedTables.length || 0;
    const activeTables = typedTables.filter(table => table.status === 'occupied').length || 0;
    
    // Get hourly trend for today
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    // Safely get hourly data with error handling
    const hourlyResult = await Promise.resolve().then(async () => {
      try {
        return await supabase
          .from('traffic_heatmap')
          .select('*')
          .eq('restaurant_id', targetRestaurantId)
          .gte('timestamp', today.toISOString())
          .order('timestamp', { ascending: true });
      } catch (e) {
        console.warn('Error accessing traffic_heatmap for hourly data:', e);
        return { data: [], error: e };
      }
    });
    
    const hourlyData = hourlyResult.data || [];
    
    // Get daily average for last 7 days
    const lastWeek = new Date();
    lastWeek.setDate(lastWeek.getDate() - 7);
    
    // Safely get daily data with error handling
    const dailyResult = await Promise.resolve().then(async () => {
      try {
        return await supabase
          .from('traffic_heatmap')
          .select('*')
          .eq('restaurant_id', targetRestaurantId)
          .gte('timestamp', lastWeek.toISOString())
          .order('timestamp', { ascending: true });
      } catch (e) {
        console.warn('Error accessing traffic_heatmap for daily data:', e);
        return { data: [], error: e };
      }
    });
    
    const dailyData = dailyResult.data || [];
    
    // Define the expected structure for traffic data points
    type HeatmapData = {
      timestamp: string;
      occupancy_percentage: number;
      active_tables?: number;
      total_tables?: number;
      source?: string;
      confidence?: number;
    };
    
    // Format hourly trend with type assertion to handle schema mismatch
    const hourlyTrend: TrafficDataPoint[] = hourlyData.map(data => {
      const typedData = data as unknown as HeatmapData;
      return {
        timestamp: typedData.timestamp,
        occupancy_percentage: typedData.occupancy_percentage || 0,
        active_tables: typedData.active_tables || 0,
        total_tables: typedData.total_tables || totalTables,
        source: (typedData.source as 'orders' | 'manual' | 'sensors' | 'estimated') || 'estimated',
        confidence: typedData.confidence || 0.7
      };
    });
    
    // Format daily trend by aggregating data
    const dailyMap = new Map<string, {sum: number, count: number}>();
    
    dailyData.forEach(data => {
      const typedData = data as unknown as HeatmapData;
      const date = new Date(typedData.timestamp).toISOString().split('T')[0];
      const existing = dailyMap.get(date) || {sum: 0, count: 0};
      
      dailyMap.set(date, {
        sum: existing.sum + (typedData.occupancy_percentage || 0),
        count: existing.count + 1
      });
    });
    
    const dailyTrend = Array.from(dailyMap.entries()).map(([day, data]) => ({
      day,
      average_occupancy: data.sum / data.count
    }));
    
    return {
      current_occupancy: currentOccupancy,
      active_tables: activeTables,
      total_tables: totalTables,
      hourly_trend: hourlyTrend,
      daily_trend: dailyTrend,
      last_updated: new Date().toISOString(),
      confidence: 0.85 // Confidence in the overall data
    };
  } catch (error) {
    console.error('Error getting traffic data:', error);
    return null;
  }
};
