import { supabase } from '@/integrations/supabase/client';
import { User } from '@supabase/supabase-js';
import { getRestaurantId } from './restaurantDbService';
import { updateTableStatus as refreshAnalyticsTableStatus } from './trafficService';

// Define database table column types to fix TypeScript errors
export interface RestaurantTable {
  id: string;
  restaurant_id: string;
  table_number: string;
  capacity: number;
  location: string;
  is_occupied: boolean;
  status: 'available' | 'occupied' | 'reserved';
  qr_code_url: string;
  created_at: string;
  updated_at: string;
}

// Type for database error handling
interface DatabaseError {
  message: string;
  details?: string;
  hint?: string;
  code?: string;
  stack?: string;
}

// Fetch all tables for a restaurant
// Type to handle Supabase query responses with proper typing
interface SupabaseQueryResult<T> {
  data: T | null;
  error: DatabaseError | null;
};

export const fetchTables = async (user?: User | null, tableId?: string): Promise<RestaurantTable[]> => {
  try {
    console.log('fetchTables called with user:', user?.id, 'and tableId:', tableId);

    let restaurantId = await getRestaurantId(user, tableId);

    if (!restaurantId) {
      console.error('Could not determine restaurant ID for fetching tables');
      // Security fix: Never show tables from other restaurants
      // Return empty array instead of using a hardcoded ID
      return [];
    }

    console.log('Fetching tables for restaurant ID:', restaurantId);

    // Log the query we're about to make
    console.log(`Making Supabase query: SELECT * FROM restaurant_tables WHERE restaurant_id = '${restaurantId}' ORDER BY table_number`);

    const { data, error } = await supabase
      .from('restaurant_tables')
      .select('*')
      .eq('restaurant_id', restaurantId)
      .order('table_number') as unknown as SupabaseQueryResult<RestaurantTable[]>;

    if (error) {
      console.error('Error in Supabase query:', error.message || error);
      throw error;
    }

    // Log the raw response
    console.log('Raw Supabase response:', data);
    console.log(`Found ${data?.length || 0} tables for restaurant ID:`, restaurantId);

    // Return the data
    return data as RestaurantTable[];
  } catch (error) {
    console.error('Error fetching tables:', error.message || error);
    return [];
  }
};

// Fetch a single table by ID
export const fetchTableById = async (tableId: string): Promise<RestaurantTable | null> => {
  try {
    const { data, error } = await supabase
      .from('restaurant_tables')
      .select('restaurant_id')
      .eq('id', tableId)
      .single() as unknown as SupabaseQueryResult<{restaurant_id: string}>;

    if (error) throw error;
    return data as RestaurantTable | null;
  } catch (error) {
    console.error('Error fetching table by ID:', error.message || error);
    throw error;
  }
};

// Create a new table
export const createTable = async (tableData: Omit<RestaurantTable, 'id' | 'created_at' | 'updated_at'>, user?: User | null): Promise<RestaurantTable> => {
  try {
    // If restaurant_id is already provided in tableData, use it
    // Otherwise, get it from the user
    let restaurantId = tableData.restaurant_id;
    if (!restaurantId && user) {
      // Pass the user to getRestaurantId to properly fetch the restaurant
      restaurantId = await getRestaurantId(user);
      console.log('Got restaurant ID for table creation:', restaurantId);
    } else if (!restaurantId) {
      // If no user and no restaurant_id, use the default test restaurant ID
      restaurantId = '1fd5e254-ab0e-468b-adfe-22217536eee6';
      console.log('Using default restaurant ID for table creation:', restaurantId);
    }

    // Check if a table with this number already exists for this restaurant
    const { data: existingTables, error: checkError } = await supabase
      .from('restaurant_tables')
      .select('id, table_number')
      .eq('restaurant_id', restaurantId)
      .eq('table_number', tableData.table_number) as unknown as SupabaseQueryResult<RestaurantTable[]>;

    if (checkError) {
      console.error('Error checking for existing table:', checkError.message || checkError);
    } else if (existingTables && existingTables.length > 0) {
      console.log(`Table "${tableData.table_number}" already exists for this restaurant, skipping creation`);
      return existingTables[0] as RestaurantTable;
    }

    console.log('Creating table with data:', {
      ...tableData,
      restaurant_id: restaurantId
    });

    // Use type assertion to handle insert operation
    const insertData = {
      ...tableData,
      restaurant_id: restaurantId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      is_occupied: tableData.status === 'occupied',
      qr_code_url: tableData.qr_code_url || null
    };
    
    // NOTE: Using type assertions due to schema mismatch between TypeScript and Supabase
    // This is a necessary workaround when the TypeScript types don't align with the actual database schema
    const { data, error } = await supabase
      .from('restaurant_tables')
      // This line previously had a @ts-expect-error directive that wasn't needed
      .insert(insertData)
      .select()
      .single() as unknown as SupabaseQueryResult<RestaurantTable>;

    if (error) {
      console.error('Error in Supabase insert operation:', error.message || error);
      throw error;
    }

    console.log('Successfully created table:', data);
    return data as RestaurantTable;
  } catch (error) {
    console.error('Error creating table:', error.message || error);
    throw error;
  }
};

// Update an existing table
export const updateTable = async (tableId: string, tableData: Partial<RestaurantTable>): Promise<RestaurantTable> => {
  try {
    const { data, error } = await supabase
      .from('restaurant_tables')
      .update({
        ...tableData,
        updated_at: new Date().toISOString()
      })
      .eq('id', tableId)
      .select()
      .single() as unknown as SupabaseQueryResult<RestaurantTable>;

    if (error) {
      console.error('Error updating table:', error.message || error);
      throw error;
    }

    return data as RestaurantTable;
  } catch (error) {
    console.error('Error updating table:', error.message || error);
    throw error;
  }
};

// Toggle table status (occupied/available/reserved)
/**
 * Toggle the status of a table and ensure both status and is_occupied fields are synchronized
 * @param tableId The ID of the table to update
 * @param newStatus The new status to set ('available', 'occupied', or 'reserved')
 * @returns The updated table or throws an error if the update fails
 */
export const toggleTableStatus = async (tableId: string, newStatus: 'available' | 'occupied' | 'reserved'): Promise<RestaurantTable> => {
  console.log(`Attempting to set table ${tableId} status to ${newStatus}`);
  
  try {
    // Fetch current table state first
    const { data: tableData, error: tableError } = await supabase
      .from('restaurant_tables')
      .select('*')
      .eq('id', tableId)
      .single();
    
    if (tableError) {
      console.error('Error fetching table:', tableError.message || tableError);
      throw tableError;
    }
    
    if (!tableData) {
      console.error('Table not found with ID:', tableId);
      throw new Error(`Table not found with ID: ${tableId}`);
    }
    
    const currentTable = tableData as RestaurantTable;
    
    // Log the current state for debugging
    console.log(`Current table state: status=${currentTable.status}, is_occupied=${currentTable.is_occupied}`);
    console.log('Full table data:', currentTable);
    
    // Set is_occupied field for backward compatibility
    const isOccupied = newStatus === 'occupied';
    console.log(`Setting is_occupied to ${isOccupied} based on status ${newStatus}`);
    
    // If we're trying to set a table from occupied to available, check for active orders first
    if (currentTable.status === 'occupied' && newStatus === 'available') {
      const { data: allOrders, error: ordersError } = await supabase
        .from('orders')
        .select('id, status')
        .eq('table_id', tableId);
        
      if (ordersError) {
        console.error('Error checking for orders:', ordersError.message || ordersError);
      } else if (allOrders && allOrders.length > 0) {
        // Filter to only consider active orders (not cancelled, completed, or delivered)
        // Once an order is delivered, the table can be made available
        const activeOrders = allOrders.filter(order => 
          !['cancelled', 'completed', 'delivered'].includes(order.status));
          
        console.log('All orders:', allOrders);
        console.log('Active orders after filtering:', activeOrders);
        
        if (activeOrders.length > 0) {
          console.warn(`Found ${activeOrders.length} active orders preventing table status change`);
          console.warn('Active orders:', activeOrders);
          throw new Error(`Table has ${activeOrders.length} active orders. Please complete or cancel them before changing table status.`);
        } else {
          console.log('All orders are completed, cancelled, or delivered - table can be marked as available');
        }
      }
      
      // Handle the case where the table has cancelled, completed, or delivered orders but is still marked as occupied
      console.log('No active orders found, table can be marked as available');
    }
    
    // DIRECT UPDATE: Simplest approach with minimal data
    console.log('Trying direct update method with minimal fields');
    try {
      // Create the most minimal update payload to reduce conflict chances
      const updatePayload = {
        status: newStatus,
        is_occupied: isOccupied
      };
      
      // First attempt - standard update
      const { error } = await supabase
        .from('restaurant_tables')
        .update(updatePayload)
        .eq('id', tableId);
      
      if (error) {
        console.error('Standard update failed:', error.message);
      } else {
        console.log('Update attempted successfully');
      }
    } catch (err) {
      console.error('Exception during update:', err);
    }
    
    // Verify if our update worked
    const { data: verifyData, error: verifyError } = await supabase
      .from('restaurant_tables')
      .select('*')
      .eq('id', tableId)
      .single();
    
    if (!verifyError && verifyData) {
      const updatedTable = verifyData as RestaurantTable;
      
      if (updatedTable.status === newStatus) {
        console.log('Table status successfully updated!');
        
        // Sync with analytics
        try {
          await refreshAnalyticsTableStatus(tableId, newStatus);
          console.log('Analytics system updated successfully');
        } catch (analyticsError) {
          console.error('Failed to sync with analytics system:', analyticsError);
          // Continue anyway, we don't want to fail the whole operation
        }
        
        return updatedTable;
      }
      
      console.log('Update failed, table status unchanged');
    } else {
      console.error('Verification failed:', verifyError?.message);
    }
    
    // Second attempt - try a different update approach with all table data
    console.log('First method failed, trying complete table update');
    try {
      // Update all fields to force a full row replacement
      const fullUpdateData = {
        ...currentTable,
        status: newStatus,
        is_occupied: isOccupied,
        updated_at: new Date().toISOString()
      };
      
      const { data, error } = await supabase
        .from('restaurant_tables')
        .update(fullUpdateData)
        .eq('id', tableId)
        .select();
      
      if (error) {
        console.error('Full update failed:', error.message);
      } else if (data && data.length > 0) {
        console.log('Full update succeeded');
        const updatedTable = data[0] as RestaurantTable;
        
        // Sync with analytics
        try {
          await refreshAnalyticsTableStatus(tableId, newStatus);
        } catch (analyticsError) {
          console.error('Failed to sync with analytics system:', analyticsError);
        }
        
        return updatedTable;
      }
    } catch (err) {
      console.error('Exception during full update:', err);
    }
    
    // Third attempt - delete and reinsert approach (if this table doesn't have dependent records)
    console.log('Attempting delete and reinsert approach');
    try {
      // Store the current data
      const tableToReinsert = {
        ...currentTable,
        status: newStatus,
        is_occupied: isOccupied,
        updated_at: new Date().toISOString()
      };
      
      // Try to delete the table (this might fail if there are dependent records)
      const { error: deleteError } = await supabase
        .from('restaurant_tables')
        .delete()
        .eq('id', tableId);
      
      if (!deleteError) {
        // If delete succeeded, reinsert with new status
        const { data: insertData, error: insertError } = await supabase
          .from('restaurant_tables')
          .insert([tableToReinsert])
          .select();
        
        if (insertError) {
          console.error('Reinsert failed:', insertError.message);
        } else if (insertData && insertData.length > 0) {
          console.log('Delete and reinsert succeeded');
          
          // Sync with analytics
          try {
            await refreshAnalyticsTableStatus(tableId, newStatus);
          } catch (analyticsError) {
            console.error('Failed to sync with analytics system:', analyticsError);
          }
          
          return insertData[0] as RestaurantTable;
        }
      } else {
        console.error('Delete operation failed:', deleteError.message);
      }
    } catch (err) {
      console.error('Exception during delete/reinsert:', err);
    }
    
    // Final verification check - did any of our attempts succeed?
    const { data: finalCheck } = await supabase
      .from('restaurant_tables')
      .select('*')
      .eq('id', tableId)
      .single();
    
    if (finalCheck) {
      const finalTable = finalCheck as RestaurantTable;
      if (finalTable.status === newStatus) {
        console.log('A previous update actually succeeded!');
        return finalTable;
      }
    }
    
    // If we get here, all update methods failed
    throw new Error(`Failed to update table status to ${newStatus}. Row-level security policies may be preventing this action.`);
  } catch (error) {
    const errorMessage = `Table status could not be updated to ${newStatus}. Database constraint or trigger may be preventing updates.`;
    console.error('Error in toggleTableStatus:', errorMessage);
    console.error('Error details:', error);
    console.error('Error stack:', new Error(errorMessage).stack);
    throw new Error(errorMessage);
  }
};

// Delete a table
export const deleteTable = async (tableId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from<RestaurantTable>('restaurant_tables')
      .delete()
      .eq('id', tableId);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error('Error deleting table:', error.message || error);
    throw error;
  }
};

// Get restaurant ID from table ID
export const getRestaurantIdFromTableId = async (tableId: string): Promise<string | null> => {
  try {
    if (!tableId) {
      console.error('No table ID provided to getRestaurantIdFromTableId');
      return null;
    }

    console.log('Getting restaurant ID for table:', tableId);

    const { data, error } = await supabase
      .from<RestaurantTable>('restaurant_tables')
      .select('restaurant_id')
      .eq('id', tableId)
      .single();

    if (error) {
      console.error('Error getting restaurant ID from table:', error.message || error);
      return null;
    }

    if (!data || !data.restaurant_id) {
      console.error('No restaurant ID found for table:', tableId);
      return null;
    }

    if (data && 'restaurant_id' in data) {
      console.log('Found restaurant ID for table:', data.restaurant_id);
      return data.restaurant_id;
    }
    return null;
  } catch (error) {
    console.error('Error in getRestaurantIdFromTableId:', error.message || error);
    return null;
  }
};

// Define interface for menu table assignment
interface MenuTableAssignment {
  menu_id: string;
  table_id: string;
  created_at?: string;
  id?: string;
}

// Assign menus to a table
export const assignMenusToTable = async (menuId: string, tableIds: string[]): Promise<boolean> => {
  try {
    // First, delete existing assignments for this menu
    // NOTE: Using type assertions for menu_table_assignments which exists in the database
    // but is not properly represented in the TypeScript schema
    const { error: deleteError } = await supabase
      // @ts-expect-error Supabase schema type mismatch intentionally bypassed
      .from('menu_table_assignments')
      .delete()
      .eq('menu_id', menuId) as unknown as { error: null | DatabaseError };
      
    if (deleteError) throw deleteError;

    // If no tables to assign, we're done
    if (tableIds.length === 0) return true;

    // Create new assignments
    const assignments: MenuTableAssignment[] = tableIds.map(tableId => ({
      menu_id: menuId,
      table_id: tableId,
      created_at: new Date().toISOString()
    }));

    // NOTE: Using type assertions for menu_table_assignments which exists in the database
    // but is not properly represented in the TypeScript schema
    const { error: insertError } = await supabase
      // @ts-expect-error Supabase schema type mismatch intentionally bypassed
      .from('menu_table_assignments')
      // @ts-expect-error Using proper structure but bypassing type checks
      .insert(assignments)
      .select() as unknown as { error: null | DatabaseError };

    if (insertError) throw insertError;
    return true;
  } catch (error) {
    console.error('Error assigning menus to table:', error instanceof Error ? error.message : String(error));
    throw error;
  }
};
