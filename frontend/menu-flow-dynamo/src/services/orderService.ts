import { supabase } from '@/integrations/supabase/client';
import { MenuItemType } from '@/components/MenuItem';
import { getRestaurantId } from './restaurantDbService';
import { updateTableStatus, markTableOccupiedForOrder, markTableAvailableAfterOrder } from './trafficService';
import { OrderItem, OrderStatus } from '@/types';
import { createNotification, createServerNotification } from './notificationService';

export interface CartItem extends MenuItemType {
  quantity: number;
}

export interface OrderDetails {
  id: string;
  orderNumber: string;
  status: string;
  estimatedTime: number;
  createdAt: string;
  items: CartItem[];
  totalAmount: number;
  tableId?: string;
}

// Local service interface (differs from the shared Order type in @/types)
export interface OrderServiceData {
  id: string;
  restaurant_id: string;
  table_id: string;
  customer_session_id: string;
  status: string;
  total_amount: number;
  created_at: string;
  updated_at: string;
  completed_at?: string;
}

/**
 * Submit an order to the database
 * @param items - Items to be ordered
 * @param tableId - Optional table ID for in-restaurant orders
 * @returns Order details with ID, items, and status
 */
export const submitOrder = async (
  items: OrderItem[],
  tableId?: string
): Promise<{
  orderId: string;
  orderNumber: string;
  estimatedTime: number;
  restaurantId: string;
} | null> => {
  try {
    // Calculate total amount
    const totalAmount = items.reduce((sum, item) => sum + item.price * item.quantity, 0);

    // Generate order number (3-digit padded number) - store this in special_requests for now
    const orderNumber = Math.floor(Math.random() * 1000).toString().padStart(3, '0');

    // Set estimated time between 10-20 minutes
    const estimatedTime = Math.floor(Math.random() * 11) + 10;

    // Get customer session ID (or generate one if not exists)
    let customerSessionId = sessionStorage.getItem('customerSessionId');
    if (!customerSessionId) {
      customerSessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      sessionStorage.setItem('customerSessionId', customerSessionId);
    }

    // Get restaurant ID and table ID
    let restaurantId = null;
    let effectiveTableId = null;

    // Check if tableId is provided and valid
    if (!tableId) {
      console.error('No table ID provided, cannot place order');
      return null;
    }

    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(tableId)) {
      console.error(`Table ID "${tableId}" is not a valid UUID`);
      return null;
    }

    // Get restaurant ID directly from the table ID
    restaurantId = await getRestaurantId(null, tableId);
    if (!restaurantId) {
      console.error(`Table ID "${tableId}" exists but no restaurant found`);
      return null;
    }

    // Use the provided table ID directly - no fallback
    effectiveTableId = tableId;

    // Insert the order into the database with extra notifications to ensure real-time updates
    // Prepare timestamp for consistent use
    const timestamp = new Date().toISOString();

    // IMPORTANT: For the restaurant dashboard, status must be exactly 'pending' (not 'new')
    // This ensures compatibility with the restaurant-side dashboard filters
    // Match the exact schema from the database
    const orderData = {
        restaurant_id: restaurantId,
        table_id: effectiveTableId,
        customer_session_id: customerSessionId,
        status: 'pending', // Must be 'pending' to show up in restaurant dashboard
        total_amount: totalAmount,
        created_at: timestamp,
        updated_at: timestamp,
        special_requests: `ORDER_NUMBER:${orderNumber}` // Store order number for retrieval
    };

    // CRITICAL: Insert order with explicit transaction for visibility in restaurant dashboard
    const { data: order, error } = await supabase
        .from('orders')
        .insert(orderData)
        .select('id');

    if (error) {
      console.error('Error submitting order:', error);
      console.error('Error details:', JSON.stringify(error, null, 2));
      return null;
    }

    if (!order || order.length === 0) {
      console.error('No order data returned after insertion');
      return null;
    }

    const orderId = order[0].id;

    // Ensure we have a valid order ID
    if (!orderId) {
      console.error('Order was created but no ID was returned');
      return null;
    }

    // IMPORTANT: After creating the order, trigger an additional update to ensure
    // the restaurant dashboard's real-time listeners detect the change
    // This helps with the Supabase real-time channel subscription in OrderManagement.tsx
    const { error: updateError } = await supabase
      .from('orders')
      .update({
        updated_at: new Date().toISOString(),
        // This action ensures the real-time listener triggers
      })
      .eq('id', orderId);

    if (updateError) {
      console.warn('Failed to trigger real-time notification:', updateError);
      // Continue anyway, as the order was created successfully
    }

    // Insert order items according to the existing database schema
    const orderItems = items.map(item => ({
      order_id: orderId,
      menu_item_id: item.id, // This must match the id in the menu_items table
      quantity: item.quantity,
      unit_price: item.price,
      status: 'pending',
      special_instructions: null, // This matches the existing records
      created_at: timestamp, // Use the same timestamp for consistency
      updated_at: timestamp
    }));

    // Insert order items directly - no fallback
    const { error: itemsError } = await supabase
      .from('order_items')
      .insert(orderItems);

    if (itemsError) {
      console.error('Error inserting order items:', JSON.stringify(itemsError, null, 2));
      return null;
    }

    // Mark table as occupied if this is an in-restaurant order
    if (effectiveTableId) {
      try {
        // Always use effectiveTableId instead of tableId
        // Existing tables are marked as is_occupied (boolean)
        const { error: tableError } = await supabase
          .from('restaurant_tables')
          .update({
            is_occupied: true,
            updated_at: new Date().toISOString()
          })
          .eq('id', effectiveTableId);

        if (tableError) {
          console.warn('Could not update table status:', tableError);
        }
      } catch (e) {
        console.error('Error updating table status:', e);
      }
    }

    // Create notification for restaurant about new order
    try {
      // Use the server notification function to bypass RLS policies
      // This is needed because the customer doesn't have permission to create notifications for the restaurant
      await createServerNotification({
        type: 'order',
        title: `New Order #${orderNumber}`,
        message: `A new order has been placed with ${items.length} items. Total: €${totalAmount.toFixed(2)}`,
        restaurant_id: restaurantId,
        action_label: 'View Order',
        action_url: `/order-management?orderId=${orderId}`,
        priority: 'high'
      });
    } catch (notifyError) {
      console.error('Failed to create order notification:', notifyError);

      // Fallback to direct notification if server notification fails
      try {
        await createNotification({
          type: 'order',
          title: `New Order #${orderNumber}`,
          message: `A new order has been placed with ${items.length} items. Total: €${totalAmount.toFixed(2)}`,
          restaurant_id: restaurantId,
          action_label: 'View Order',
          action_url: `/order-management?orderId=${orderId}`,
          priority: 'high'
        }, true);
      } catch (fallbackError) {
        console.error('Fallback notification also failed:', fallbackError);
      }

      // Continue even if notification fails
    }

    // Return order details
    const orderDetails = {
      orderId,
      orderNumber,
      estimatedTime,
      restaurantId
    };

    return orderDetails;
  } catch (error) {
    console.error('Error submitting order:', error);
    return null;
  }
};

// ... (rest of the code remains the same)
export const getOrderStatus = async (orderId: string): Promise<OrderDetails | null> => {
  try {
    // Get order details from the database
    const { data: orderData, error: orderError } = await supabase
      .from('orders')
      .select(`
        *,
        restaurant_tables (table_number)
      `)
      .eq('id', orderId)
      .single();

    if (orderError) {
      console.error('Error fetching order:', orderError);
      return null;
    }

    // Get order items
    const { data: orderItems, error: itemsError } = await supabase
      .from('order_items')
      .select(`
        *,
        menu_items (id, name, description, category, current_price, image_url)
      `)
      .eq('order_id', orderId);

    if (itemsError) {
      console.error('Error fetching order items:', itemsError);
      return null;
    }

    // Transform order items to cart items
    const cartItems: CartItem[] = (orderItems || []).map(item => ({
      id: item.menu_items.id,
      name: item.menu_items.name,
      description: item.menu_items.description || '',
      category: item.menu_items.category,
      price: item.unit_price,
      image: item.menu_items.image_url || '',
      quantity: item.quantity
    }));

    // Extract order number from special_requests
    let orderNumber = '000'; // Default fallback
    if (orderData.special_requests && orderData.special_requests.includes('ORDER_NUMBER:')) {
      const orderNumberMatch = orderData.special_requests.match(/ORDER_NUMBER:(\d{3})/);
      if (orderNumberMatch) {
        orderNumber = orderNumberMatch[1];
      }
    } else {
      // Fallback for old orders - use a hash of the ID to get consistent 3-digit number
      const idHash = orderData.id.split('-')[0];
      const numericHash = parseInt(idHash.substring(0, 8), 16);
      orderNumber = (numericHash % 1000).toString().padStart(3, '0');
    }

    // Return order details
    return {
      id: orderData.id,
      orderNumber,
      status: orderData.status,
      estimatedTime: 15, // Default to 15 minutes
      createdAt: orderData.created_at,
      items: cartItems,
      totalAmount: orderData.total_amount,
      tableId: orderData.table_id
    };
  } catch (error) {
    console.error('Error getting order status:', error);
    return null;
  }
};

// Update order status
export const updateOrderStatus = async (orderId: string, status: string): Promise<boolean> => {
  try {
    // Update real order in database
    const { error } = await supabase
      .from('orders')
      .update({
        status: status,
        updated_at: new Date().toISOString(),
        ...(status === 'completed' ? { completed_at: new Date().toISOString() } : {})
      })
      .eq('id', orderId);

    if (error) {
      console.error('Error updating order status:', error);
      return false;
    }

    // If order is completed, update table status to available
    if (status === 'completed' || status === 'cancelled') {
      try {
        await markTableAvailableAfterOrder(orderId);
      } catch (statusError) {
        console.error('Error updating table status after order completion:', statusError);
        // Continue with order status update - non-critical error
      }
    }

    return true;
  } catch (error) {
    console.error('Error updating order status:', error);
    return false;
  }
};


