
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/components/ui/use-toast';
import AdminLayout from '@/components/AdminLayout';
import { useLanguage } from '@/contexts/LanguageContext';
import { useRestaurant } from '@/contexts/RestaurantContext';
import { useAuth } from '@/contexts/AuthContext';
import { 
  getPricingRules, 
  clearPricingRulesCache, 
  forcePricingRulesRefresh, 
  updatePricingRules, 
  type PricingRules 
} from '@/services/dynamicPricingService';

// Simple component to display current database settings
// This helps restaurant owners see exactly what's in the database
const ViewDatabaseSettings = ({ restaurantId }: { restaurantId: string }) => {
  const [dbSettings, setDbSettings] = useState<{food: boolean, drinks: boolean} | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setLoading(true);
        const rules = await getPricingRules(restaurantId);
        if (rules && rules.applyToCategories) {
          setDbSettings({
            food: rules.applyToCategories.food === true,
            drinks: rules.applyToCategories.drinks === true
          });
        } else {
          setError('No settings found');
        }
      } catch (err) {
        console.error('Error fetching DB settings:', err);
        setError('Failed to load settings');
      } finally {
        setLoading(false);
      }
    };
    
    fetchSettings();
  }, [restaurantId]);
  
  if (loading) return <p>Loading settings...</p>;
  if (error) return <p className="text-red-500">{error}</p>;
  if (!dbSettings) return <p>No settings found</p>;
  
  return (
    <div className="text-xs">
      <div className="flex items-center justify-between">
        <span>Food pricing:</span>
        <span className={dbSettings.food ? "text-green-600 font-bold" : "text-red-500"}>
          {dbSettings.food ? "ENABLED" : "DISABLED"}
        </span>
      </div>
      <div className="flex items-center justify-between">
        <span>Drinks pricing:</span>
        <span className={dbSettings.drinks ? "text-green-600 font-bold" : "text-red-500"}>
          {dbSettings.drinks ? "ENABLED" : "DISABLED"}
        </span>
      </div>
    </div>
  );
};

const Settings = () => {
  const { t, language, setLanguage } = useLanguage();
  const { restaurantInfo, updateRestaurantInDb } = useRestaurant();
  const { user } = useAuth();
  const { toast } = useToast();

  // Restaurant profile form state
  const [restaurantName, setRestaurantName] = useState('');
  const [logoUrl, setLogoUrl] = useState('');
  const [address, setAddress] = useState('');
  const [contactEmail, setContactEmail] = useState('');
  const [contactPhone, setContactPhone] = useState('');
  const [description, setDescription] = useState('');
  const [openingHours, setOpeningHours] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  // Dynamic pricing form state
  const [dynamicEnabled, setDynamicEnabled] = useState(false);
  const [highTrafficAdjustment, setHighTrafficAdjustment] = useState(10); // Default 10%
  const [mediumTrafficAdjustment, setMediumTrafficAdjustment] = useState(5); // Default 5%
  const [lowTrafficAdjustment, setLowTrafficAdjustment] = useState(-5); // Default -5%
  const [applyToFood, setApplyToFood] = useState(true);
  const [applyToDrinks, setApplyToDrinks] = useState(false);
  const [confidenceThreshold, setConfidenceThreshold] = useState(70); // Default 70%
  const [isSavingPricing, setIsSavingPricing] = useState(false);

  // Load restaurant data when component mounts
  useEffect(() => {
    if (restaurantInfo) {
      // Load restaurant profile data
      setRestaurantName(restaurantInfo.name || '');
      setLogoUrl(restaurantInfo.logo_url || '');
      setAddress(restaurantInfo.address || '');
      setContactEmail(restaurantInfo.contact_email || '');
      setContactPhone(restaurantInfo.contact_phone || '');
      setDescription(restaurantInfo.description || '');

      // Format opening hours for display if it exists
      if (restaurantInfo.opening_hours) {
        try {
          if (typeof restaurantInfo.opening_hours === 'string') {
            setOpeningHours(restaurantInfo.opening_hours);
          } else {
            setOpeningHours(JSON.stringify(restaurantInfo.opening_hours, null, 2));
          }
        } catch (e) {
          console.error('Error parsing opening hours:', e);
          setOpeningHours('');
        }
      }

      // Load dynamic pricing flag and settings if they exist
      setDynamicEnabled(Boolean(restaurantInfo.dynamic_pricing_enabled));
      try {
        // Check if pricing_rules exists in restaurantInfo
        if (restaurantInfo.pricing_rules) {
          // Ensure we're working with the correct PricingRules type
          const pricingRules: PricingRules = typeof restaurantInfo.pricing_rules === 'string'
            ? JSON.parse(restaurantInfo.pricing_rules) as PricingRules
            : restaurantInfo.pricing_rules as PricingRules;

          console.log('Loading pricing rules from database:', pricingRules);
          
          // Set state from saved rules
          setHighTrafficAdjustment(pricingRules.highTraffic?.percentage || 10);
          setMediumTrafficAdjustment(pricingRules.mediumTraffic?.percentage || 5);
          setLowTrafficAdjustment(pricingRules.lowTraffic?.percentage || -5);
          
          // Handle category settings
          if (pricingRules.applyToCategories) {
            // Use the exact boolean values from the database
            setApplyToFood(Boolean(pricingRules.applyToCategories.food ?? true));
            setApplyToDrinks(Boolean(pricingRules.applyToCategories.drinks ?? false));
            console.log('Loaded category settings:', {
              food: pricingRules.applyToCategories.food,
              drinks: pricingRules.applyToCategories.drinks,
              foodAfterConversion: Boolean(pricingRules.applyToCategories.food ?? true),
              drinksAfterConversion: Boolean(pricingRules.applyToCategories.drinks ?? false)
            });
          } else {
            // Set defaults if no category settings
            setApplyToFood(true);
            setApplyToDrinks(false);
            console.log('No category settings found, using defaults');
          }
          
          setConfidenceThreshold(pricingRules.confidenceThreshold || 70);
        }
      } catch (e) {
        console.error('Error loading dynamic pricing rules:', e);
        // Use defaults if there's an error
      }
    }
  }, [restaurantInfo]);

  const handleLanguageChange = (newLanguage: 'en' | 'es') => {
    setLanguage(newLanguage);
    toast({
      title: "Language Changed",
      description: newLanguage === 'en' ? "Language set to English" : "Idioma establecido en Español",
    });
  };

  const handleSaveRestaurantProfile = async () => {
    if (!user) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "You must be logged in to update your restaurant profile",
      });
      return;
    }

    setIsSaving(true);
    try {
      // Parse opening hours if provided
      let parsedOpeningHours = null;
      if (openingHours.trim()) {
        try {
          parsedOpeningHours = JSON.parse(openingHours);
        } catch (e) {
          // If not valid JSON, store as string
          parsedOpeningHours = openingHours;
        }
      }

      // Update restaurant in database
      await updateRestaurantInDb({
        name: restaurantName,
        logo_url: logoUrl || null,
        address: address || null,
        contact_email: contactEmail || null,
        contact_phone: contactPhone || null,
        description: description || null,
        opening_hours: parsedOpeningHours
      });

      toast({
        title: "Profile Updated",
        description: "Your restaurant profile has been updated successfully",
      });
    } catch (error) {
      console.error('Error saving restaurant profile:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update restaurant profile",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleSavePricingRules = async () => {
    if (!user || !restaurantInfo?.id) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "You must be logged in to update pricing rules",
      });
      return;
    }

    setIsSavingPricing(true);
    try {
      // Log the current toggle states exactly as they appear in the UI
      console.log('💾 SETTINGS UI STATE BEFORE SAVE:', {
        "Food toggle": applyToFood ? "ON" : "OFF",
        "Drinks toggle": applyToDrinks ? "ON" : "OFF",
        highTrafficAdjustment,
        mediumTrafficAdjustment,
        lowTrafficAdjustment,
        confidenceThreshold
      });
      
      // Triple check that our boolean values are correct
      const foodToggleValue = applyToFood === true;
      const drinksToggleValue = applyToDrinks === true;
      
      console.log('💾 VERIFIED TOGGLE VALUES:', {
        "Food setting": foodToggleValue,
        "Drinks setting": drinksToggleValue
      });

      // Format pricing rules as JSON object with correct typing
      // Be extremely careful with the boolean values here
      const pricingRules: PricingRules = {
        highTraffic: {
          percentage: highTrafficAdjustment,
          threshold: 0.8, // 80% capacity
        },
        mediumTraffic: {
          percentage: mediumTrafficAdjustment,
          threshold: 0.5, // 50% capacity
        },
        lowTraffic: {
          percentage: lowTrafficAdjustment,
          threshold: 0.3, // 30% capacity
        },
        applyToCategories: {
          // Use the explicit boolean values we've verified
          food: foodToggleValue,
          drinks: drinksToggleValue
        },
        confidenceThreshold: confidenceThreshold / 100, // Convert to decimal (0-1)
        updatedAt: new Date().toISOString(),
      };

      console.log('💾 EXACT PRICING RULES BEING SAVED:', JSON.stringify(pricingRules, null, 2));

      // First, use our new function to update the database and clear all caches
      const success = await updatePricingRules(restaurantInfo.id, pricingRules);

      if (success) {
        console.log('✅ Successfully updated pricing rules with new function');
        
        // Also update the restaurant record with the same rules
        await updateRestaurantInDb({
          dynamic_pricing_enabled: dynamicEnabled,
          pricing_rules: pricingRules
        });
      } else {
        console.warn('⚠️ Failed to update pricing rules with new function');

        // Try using just the updateRestaurantInDb method
        await updateRestaurantInDb({
          dynamic_pricing_enabled: dynamicEnabled,
          pricing_rules: pricingRules
        });

        // Fallback to localStorage as a last resort
        localStorage.setItem(
          `pricing_rules_${restaurantInfo.id}`,
          JSON.stringify(pricingRules)
        );
      }

      // Clear all caches to ensure fresh data is loaded
      clearPricingRulesCache(restaurantInfo.id);

      // Force a refresh of the pricing rules just to be safe
      try {
        // Use a try/catch here to handle any potential errors
        const refreshedRules = await forcePricingRulesRefresh(restaurantInfo.id);
        console.log('Refreshed pricing rules:', refreshedRules);
      } catch (error) {
        console.warn('Error refreshing pricing rules:', error);
      }

      toast({
        title: "Pricing Rules Updated",
        description: "Your dynamic pricing rules have been saved successfully",
      });

      console.log('✅ Pricing rules update complete');
    } catch (error) {
      console.error('Error saving pricing rules:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update pricing rules",
      });
    } finally {
      setIsSavingPricing(false);
    }
  };

  return (
    <AdminLayout
      title={t('settings')}
      description="Configure your restaurant's settings and preferences"
    >
      <Tabs defaultValue="general">
        <TabsList className="mb-6">
          <TabsTrigger value="general">{t('generalSettings')}</TabsTrigger>
          <TabsTrigger value="profile">{t('restaurantProfile')}</TabsTrigger>
          <TabsTrigger value="pricing">{t('pricingRules')}</TabsTrigger>
        </TabsList>

        <TabsContent value="general">
          <Card>
            <CardHeader>
              <CardTitle>{t('language')}</CardTitle>
              <CardDescription>Select your preferred language</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4">
                <Button
                  variant={language === 'en' ? 'default' : 'outline'}
                  onClick={() => handleLanguageChange('en')}
                >
                  {t('english')}
                </Button>
                <Button
                  variant={language === 'es' ? 'default' : 'outline'}
                  onClick={() => handleLanguageChange('es')}
                >
                  {t('spanish')}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="profile">
          <Card>
            <CardHeader>
              <CardTitle>{t('restaurantProfile')}</CardTitle>
              <CardDescription>Update your restaurant's information</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="restaurant-name">{t('restaurantName')}</Label>
                  <Input
                    id="restaurant-name"
                    value={restaurantName}
                    onChange={(e) => setRestaurantName(e.target.value)}
                    placeholder={t('restaurantName')}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="logo-url">{t('logoUrl')} ({t('optional')})</Label>
                  <Input
                    id="logo-url"
                    value={logoUrl}
                    onChange={(e) => setLogoUrl(e.target.value)}
                    placeholder={t('logoUrl')}
                  />
                  {logoUrl && (
                    <div className="mt-2 p-2 border rounded">
                      <p className="text-xs mb-1">Logo Preview:</p>
                      <img
                        src={logoUrl}
                        alt="Restaurant Logo"
                        className="max-h-20 max-w-full"
                        onError={(e) => {
                          e.currentTarget.src = 'https://placehold.co/200x100?text=Invalid+Image';
                        }}
                      />
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="address">Address ({t('optional')})</Label>
                  <Input
                    id="address"
                    value={address}
                    onChange={(e) => setAddress(e.target.value)}
                    placeholder="Restaurant Address"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="contact-email">Contact Email</Label>
                  <Input
                    id="contact-email"
                    type="email"
                    value={contactEmail}
                    onChange={(e) => setContactEmail(e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="contact-phone">Contact Phone ({t('optional')})</Label>
                  <Input
                    id="contact-phone"
                    value={contactPhone}
                    onChange={(e) => setContactPhone(e.target.value)}
                    placeholder="+****************"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description ({t('optional')})</Label>
                  <Textarea
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="A brief description of your restaurant"
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="opening-hours">Opening Hours ({t('optional')})</Label>
                  <Textarea
                    id="opening-hours"
                    value={openingHours}
                    onChange={(e) => setOpeningHours(e.target.value)}
                    placeholder="Enter opening hours information"
                    rows={4}
                  />
                  <p className="text-xs text-muted-foreground">
                    You can enter a simple text description or a JSON object with detailed hours
                  </p>
                </div>

                <Button
                  onClick={handleSaveRestaurantProfile}
                  disabled={isSaving || !restaurantName.trim() || !contactEmail.trim()}
                  className="w-full"
                >
                  {isSaving ? t('saving') : t('saveProfile')}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="pricing">
            <Card>
            <CardHeader>
              <CardTitle>{t('dynamicPricing')}</CardTitle>
              <CardDescription>Configure your restaurant's dynamic pricing rules</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Toggle dynamic pricing on/off */}
                <div className="flex items-center space-x-2">
                  <Switch
                    id="dynamic-enabled"
                    checked={dynamicEnabled}
                    onCheckedChange={(val) => setDynamicEnabled(Boolean(val))}
                  />
                  <Label htmlFor="dynamic-enabled" className="text-sm font-medium">
                    Enable Dynamic Pricing
                  </Label>
                </div>
                {/* Per-category toggles */}
                <div className="flex items-center space-x-6">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="apply-food"
                      checked={applyToFood}
                      onCheckedChange={(val) => setApplyToFood(Boolean(val))}
                    />
                    <Label htmlFor="apply-food" className="text-sm font-medium">
                      Apply to Food Items
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="apply-drinks"
                      checked={applyToDrinks}
                      onCheckedChange={(val) => setApplyToDrinks(Boolean(val))}
                    />
                    <Label htmlFor="apply-drinks" className="text-sm font-medium">
                      Apply to Drink Items
                    </Label>
                  </div>
                </div>
                {/* High Traffic Adjustment */}
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="high-traffic-adjustment" className="text-sm font-medium flex justify-between">
                      <span>High Traffic Adjustment (80%+ capacity)</span>
                      <span className="text-sm text-sky-600">+{highTrafficAdjustment}%</span>
                    </Label>
                    <div className="flex items-center gap-2">
                      <span className="text-xs">0%</span>
                      <Input
                        id="high-traffic-adjustment"
                        type="range"
                        min="0"
                        max="30"
                        step="1"
                        value={highTrafficAdjustment}
                        onChange={(e) => setHighTrafficAdjustment(parseInt(e.target.value))}
                        className="flex-1"
                      />
                      <span className="text-xs">30%</span>
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    During high traffic periods (80% or more capacity), prices will increase by this percentage.
                  </p>
                </div>

                {/* Medium Traffic Adjustment */}
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="medium-traffic-adjustment" className="text-sm font-medium flex justify-between">
                      <span>Medium Traffic Adjustment (50-80% capacity)</span>
                      <span className="text-sm text-sky-600">+{mediumTrafficAdjustment}%</span>
                    </Label>
                    <div className="flex items-center gap-2">
                      <span className="text-xs">0%</span>
                      <Input
                        id="medium-traffic-adjustment"
                        type="range"
                        min="0"
                        max="20"
                        step="1"
                        value={mediumTrafficAdjustment}
                        onChange={(e) => setMediumTrafficAdjustment(parseInt(e.target.value))}
                        className="flex-1"
                      />
                      <span className="text-xs">20%</span>
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    During medium traffic periods (50-80% capacity), prices will increase by this percentage.
                  </p>
                </div>

                {/* Low Traffic Adjustment */}
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="low-traffic-adjustment" className="text-sm font-medium flex justify-between">
                      <span>Low Traffic Adjustment (Below 30% capacity)</span>
                      <span className="text-sm text-sky-600">{lowTrafficAdjustment > 0 ? '+' : ''}{lowTrafficAdjustment}%</span>
                    </Label>
                    <div className="flex items-center gap-2">
                      <span className="text-xs">-15%</span>
                      <Input
                        id="low-traffic-adjustment"
                        type="range"
                        min="-15"
                        max="5"
                        step="1"
                        value={lowTrafficAdjustment}
                        onChange={(e) => setLowTrafficAdjustment(parseInt(e.target.value))}
                        className="flex-1"
                      />
                      <span className="text-xs">+5%</span>
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    During low traffic periods (under 30% capacity), prices can be decreased to attract more customers.
                  </p>
                </div>

                {/* Apply to Categories */}
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium mb-2 block">Apply Dynamic Pricing To:</Label>
                    <div className="grid grid-cols-2 gap-6 bg-slate-100 p-4 rounded-md">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="apply-to-food" className="text-sm font-medium">Food Items</Label>
                        <Switch
                          id="apply-to-food"
                          checked={applyToFood}
                          onCheckedChange={(checked) => {
                            console.log('Changing applyToFood to:', checked);
                            setApplyToFood(Boolean(checked));
                          }}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label htmlFor="apply-to-drinks" className="text-sm font-medium">Drink Items</Label>
                        <Switch
                          id="apply-to-drinks"
                          checked={applyToDrinks}
                          onCheckedChange={(checked) => {
                            console.log('Changing applyToDrinks to:', checked);
                            setApplyToDrinks(Boolean(checked));
                          }}
                        />
                      </div>
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Choose which menu item categories should have dynamic pricing applied.
                  </p>
                </div>

                {/* Confidence Threshold */}
                <div className="space-y-2">
                  <Label htmlFor="confidence-threshold" className="text-sm font-medium flex justify-between">
                    <span>AI Confidence Threshold</span>
                    <span className="text-sm text-sky-600">{confidenceThreshold}%</span>
                  </Label>
                  <Input
                    id="confidence-threshold"
                    type="range"
                    min="50"
                    max="95"
                    step="5"
                    value={confidenceThreshold}
                    onChange={(e) => setConfidenceThreshold(parseInt(e.target.value))}
                    className="w-full"
                  />
                  <p className="text-xs text-muted-foreground">
                    Only apply price changes when the AI is at least this confident in its recommendations.
                  </p>
                </div>

                <div className="space-y-2">
                  <Button
                    onClick={handleSavePricingRules}
                    disabled={isSavingPricing}
                    className="w-full bg-sky-600 hover:bg-sky-700"
                  >
                    {isSavingPricing ? 'Saving...' : 'Save Pricing Rules'}
                  </Button>
                  
                  <div className="pt-4 border-t mt-4 space-y-3">
                    {/* Status indicator */}
                    <div className="bg-slate-100 rounded-md p-3 text-xs">
                      <h4 className="font-semibold mb-1 flex items-center">
                        <span className="inline-block w-2 h-2 bg-sky-500 rounded-full mr-2"></span>
                        Current Database Settings
                      </h4>
                      <div className="text-slate-600 pl-4 space-y-1 mb-2">
                        <ViewDatabaseSettings restaurantId={restaurantInfo.id} />                        
                      </div>
                      <p className="text-gray-500 italic">If what you see above doesn't match your toggles, use the sync button below</p>
                    </div>
                    
                    <p className="text-xs text-muted-foreground">
                      If menu pricing isn't reflecting your changes, sync your selections with the database:
                    </p>
                    <Button 
                      onClick={async () => {
                        console.log('Force syncing category settings with database...');
                        console.log('Current UI settings:', { applyToFood, applyToDrinks });
                        
                        try {
                          // Create minimal rules with just the category settings
                          const minimalRules: PricingRules = {
                            applyToCategories: {
                              // Use strict equality check to ensure true boolean values
                              food: applyToFood === true,
                              drinks: applyToDrinks === true,
                            },
                            updatedAt: new Date().toISOString(),
                          };
                          
                          // Force update in database and clear all caches
                          await updatePricingRules(restaurantInfo.id, minimalRules);
                          clearPricingRulesCache(restaurantInfo.id);
                          
                          // Force refresh to verify new settings
                          const refreshedRules = await forcePricingRulesRefresh(restaurantInfo.id);
                          console.log('Updated database settings:', refreshedRules?.applyToCategories);
                          
                          toast({
                            title: "Settings Synced Successfully",
                            description: `New settings: Food=${applyToFood ? 'ON' : 'OFF'}, Drinks=${applyToDrinks ? 'ON' : 'OFF'}`
                          });
                          
                          // Force a page refresh to show updated settings
                          setTimeout(() => {
                            window.location.reload();
                          }, 1500);
                          
                        } catch (error) {
                          console.error('Error force syncing settings:', error);
                          toast({
                            variant: "destructive",
                            title: "Sync Failed",
                            description: "Failed to force sync settings."
                          });
                        }
                      }}
                      variant="outline"
                      className="w-full py-2 bg-sky-50 hover:bg-sky-100 text-sky-700 border-sky-200"
                      size="sm"
                    >
                      Sync Settings to Database
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </AdminLayout>
  );
};

export default Settings;
