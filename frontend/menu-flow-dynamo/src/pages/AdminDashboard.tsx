import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  <PERSON><PERSON><PERSON>, 
  BarChart2, 
  Settings, 
  Utensils, 
  QrCode, 
  Clock, 
  LogOut, 
  Plus, 
  ArrowUp, 
  ArrowDown,
  Percent,
  TrendingUp,
  TrendingDown,
  Users,
  DollarSign,
  ShoppingCart,
  Activity,
  Eye,
  RefreshCw,
  Zap
} from 'lucide-react';
import AdminLayout from '@/components/AdminLayout';
import { useLanguage } from '@/contexts/LanguageContext';
import { Link } from 'react-router-dom';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { useRestaurant } from '@/contexts/RestaurantContext';
import { Switch } from '@/components/ui/switch';
import { 
  getCachedDynamicPricingData, 
  isDynamicPricingEnabled, 
  setDynamicPricingEnabled 
} from '@/services/dynamicPricingService';
import { 
  fetchDashboardMetrics, 
  fetchCurrentTraffic, 
  fetchWeeklyRevenue,
  createTrafficRecord,
  type DashboardMetrics,
  type TrafficData 
} from '@/services/dashboardAnalyticsService';
import { toast } from 'sonner';

const AdminDashboard = () => {
  const { t } = useLanguage();
  const { user } = useAuth();
  const { restaurantInfo } = useRestaurant();
  const queryClient = useQueryClient();
  
  const [pricingData, setPricingData] = useState<{
    priceFactor: number;
    insights: string[];
    nextUpdateTime: string;
    confidence: number;
  } | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const restaurantId = restaurantInfo?.id;

  // Fetch dashboard metrics with real-time updates
  const { 
    data: dashboardMetrics, 
    isLoading: metricsLoading,
    error: metricsError,
    refetch: refetchMetrics 
  } = useQuery({
    queryKey: ['dashboardMetrics', restaurantId],
    queryFn: () => fetchDashboardMetrics(restaurantId!),
    enabled: !!restaurantId,
    refetchInterval: 30000, // Refresh every 30 seconds
    staleTime: 15000, // Consider data stale after 15 seconds
    retry: 3,
    retryDelay: 1000,
  });

  // Fetch current traffic data
  const { 
    data: trafficData,
    isLoading: trafficLoading 
  } = useQuery({
    queryKey: ['currentTraffic', restaurantId],
    queryFn: () => fetchCurrentTraffic(restaurantId!),
    enabled: !!restaurantId,
    refetchInterval: 10000, // Refresh every 10 seconds for real-time traffic
    staleTime: 5000,
    retry: 2,
  });

  // Fetch weekly revenue trend
  const { 
    data: weeklyRevenue,
    isLoading: revenueLoading 
  } = useQuery({
    queryKey: ['weeklyRevenue', restaurantId],
    queryFn: () => fetchWeeklyRevenue(restaurantId!),
    enabled: !!restaurantId,
    refetchInterval: 60000, // Refresh every minute
    staleTime: 30000,
    retry: 2,
  });

  // Fetch dynamic pricing status using React Query
  const { 
    data: isDynamicPricingOn, 
    isLoading: loadingPricingStatus,
    refetch: refetchPricingStatus 
  } = useQuery({
    queryKey: ['dynamicPricingStatus', restaurantId],
    queryFn: () => isDynamicPricingEnabled(restaurantId!, user!),
    enabled: !!restaurantId && !!user,
    refetchInterval: 60000, // Check every minute
    staleTime: 30000,
    retry: 2,
  });

  // Update pricing data when dynamic pricing is enabled
  useEffect(() => {
    if (isDynamicPricingOn) {
      const cachedData = getCachedDynamicPricingData();
      if (cachedData) {
        setPricingData(cachedData);
      }
      
      // Set up interval to refresh pricing data
      const intervalId = setInterval(() => {
        const cachedData = getCachedDynamicPricingData();
        if (cachedData) setPricingData(cachedData);
      }, 60000);
      
      return () => clearInterval(intervalId);
    } else {
      setPricingData(null);
    }
  }, [isDynamicPricingOn]);

  // Handle dynamic pricing toggle
  const handleDynamicPricingToggle = async (enabled: boolean) => {
    if (restaurantId && user) {
      try {
        const success = await setDynamicPricingEnabled(enabled, restaurantId, user);
        
        if (success) {
          // Refetch the pricing status to update the UI
          refetchPricingStatus();
          toast.success(
            enabled 
              ? 'AI Dynamic pricing enabled - menus will now reflect current demand' 
              : 'Dynamic pricing disabled - standard pricing is now in effect'
          );
        } else {
          toast.error('Failed to update dynamic pricing settings. Please try again.');
        }
      } catch (error) {
        console.error('Error toggling dynamic pricing:', error);
        toast.error('An error occurred while updating pricing settings');
      }
    }
  };

  // Manual refresh function
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await Promise.all([
        refetchMetrics(),
        queryClient.invalidateQueries({ queryKey: ['currentTraffic'] }),
        queryClient.invalidateQueries({ queryKey: ['weeklyRevenue'] }),
        refetchPricingStatus()
      ]);
      toast.success('Dashboard data refreshed');
    } catch (error) {
      console.error('Error refreshing dashboard:', error);
      toast.error('Failed to refresh data');
    } finally {
      setIsRefreshing(false);
    }
  };

  // Test traffic function for dynamic pricing
  const handleTestTraffic = async (percentage: number) => {
    if (restaurantId) {
      try {
        const success = await createTrafficRecord(restaurantId, percentage);
        if (success) {
          toast.success(`Traffic set to ${percentage}% for testing`);
          // Refresh traffic data
          queryClient.invalidateQueries({ queryKey: ['currentTraffic'] });
        } else {
          toast.error('Failed to set traffic level');
        }
      } catch (error) {
        toast.error('Error setting traffic level');
      }
    }
  };

  // Calculate trends
  const getRevenueChange = () => {
    if (!weeklyRevenue || weeklyRevenue.length < 2) return 0;
    const today = weeklyRevenue[weeklyRevenue.length - 1]?.revenue || 0;
    const yesterday = weeklyRevenue[weeklyRevenue.length - 2]?.revenue || 0;
    return yesterday > 0 ? ((today - yesterday) / yesterday) * 100 : 0;
  };

  const getTrafficStatusColor = (status: string) => {
    switch (status) {
      case 'high': return 'text-red-500';
      case 'low': return 'text-blue-500';
      default: return 'text-green-500';
    }
  };

  const getTrafficStatusIcon = (status: string) => {
    switch (status) {
      case 'high': return <TrendingUp className="h-4 w-4" />;
      case 'low': return <TrendingDown className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  if (metricsError) {
    return (
      <AdminLayout title={t('dashboard')} description={t('errorLoadingDashboard')}>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-500 mb-4">{t('failedToLoadDashboardData')}</p>
            <Button onClick={handleRefresh}>{t('tryAgain')}</Button>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout
      title={t('dashboardOverview')}
      description={t('realTimeInsights')}
    >
      {/* Header with refresh button */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            {t('refresh')}
          </Button>
          <div className="flex gap-1">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => handleTestTraffic(15)}
              title={t('testLowTraffic')}
            >
              15%
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => handleTestTraffic(85)}
              title={t('testHighTraffic')}
            >
              85%
            </Button>
          </div>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        {/* Today's Orders */}
        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardDescription>{t('todaysOrders')}</CardDescription>
              <ShoppingCart className="h-4 w-4 text-restaurant-muted" />
            </div>
            <CardTitle className="text-3xl">
              {metricsLoading ? '...' : dashboardMetrics?.todayOrders || 0}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-xs text-restaurant-muted">
              {dashboardMetrics?.todayOrders === 0 
                ? t('noOrdersToday') 
                : `${dashboardMetrics?.recentOrders?.length || 0} ${t('recentOrders').toLowerCase()}`}
            </p>
          </CardContent>
        </Card>

        {/* Today's Revenue */}
        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardDescription>{t('todaysRevenue')}</CardDescription>
              <DollarSign className="h-4 w-4 text-restaurant-muted" />
            </div>
            <CardTitle className="text-3xl">
              €{metricsLoading ? '...' : (dashboardMetrics?.todayRevenue || 0).toFixed(2)}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center text-xs">
              {getRevenueChange() > 0 ? (
                <ArrowUp className="h-3 w-3 text-green-500 mr-1" />
              ) : getRevenueChange() < 0 ? (
                <ArrowDown className="h-3 w-3 text-red-500 mr-1" />
              ) : null}
              <span className={getRevenueChange() > 0 ? 'text-green-500' : getRevenueChange() < 0 ? 'text-red-500' : 'text-restaurant-muted'}>
                {Math.abs(getRevenueChange()).toFixed(1)}% {t('vsYesterday')}
              </span>
            </div>
          </CardContent>
        </Card>

        {/* Current Traffic */}
        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardDescription>{t('currentTraffic')}</CardDescription>
              <Users className="h-4 w-4 text-restaurant-muted" />
            </div>
            <CardTitle className="text-3xl flex items-center">
              {metricsLoading ? '...' : `${dashboardMetrics?.currentTraffic || 0}%`}
              {dashboardMetrics?.trafficStatus && (
                <span className={`ml-2 ${getTrafficStatusColor(dashboardMetrics.trafficStatus)}`}>
                  {getTrafficStatusIcon(dashboardMetrics.trafficStatus)}
                </span>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Badge 
              variant={dashboardMetrics?.trafficStatus === 'high' ? 'destructive' : 
                      dashboardMetrics?.trafficStatus === 'low' ? 'secondary' : 'default'}
              className="text-xs"
            >
              {t(`${dashboardMetrics?.trafficStatus || 'normal'}Traffic`).toUpperCase()}
            </Badge>
          </CardContent>
        </Card>

        {/* Dynamic Pricing */}
        <Card>
          <CardHeader className="pb-2">
            <div className="flex justify-between items-center">
              <CardDescription>{t('dynamicPricing')}</CardDescription>
              <div className="flex items-center space-x-2">
                <Zap className="h-4 w-4 text-restaurant-muted" />
                <Switch
                  checked={isDynamicPricingOn || false}
                  onCheckedChange={handleDynamicPricingToggle}
                  disabled={loadingPricingStatus}
                />
              </div>
            </div>
            <CardTitle className="text-3xl flex items-center">
              {loadingPricingStatus ? (
                '...'
              ) : pricingData && isDynamicPricingOn ? (
                <>
                  {pricingData.priceFactor > 1 ? (
                    <ArrowUp className="text-green-500 mr-1 h-5 w-5" />
                  ) : pricingData.priceFactor < 1 ? (
                    <ArrowDown className="text-amber-500 mr-1 h-5 w-5" />
                  ) : null}
                  {((pricingData.priceFactor - 1) * 100).toFixed(1)}%
                </>
              ) : isDynamicPricingOn ? (
                <span className="text-green-500">{t('on')}</span>
              ) : (
                <span className="text-restaurant-muted">{t('off')}</span>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loadingPricingStatus ? (
              <p className="text-xs text-restaurant-muted">{t('loading')}...</p>
            ) : isDynamicPricingOn ? (
              pricingData ? (
                <p className="text-xs text-restaurant-muted">
                  {t('confidence')}: {(pricingData.confidence * 100).toFixed(0)}%
                </p>
              ) : (
                <p className="text-xs text-restaurant-muted">{t('analyzingTraffic')}...</p>
              )
            ) : (
              <p className="text-xs text-restaurant-muted">{t('enableAiPricing')}</p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        {/* Popular Items */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>{t('popularItems')} ({t('last7Days')})</CardTitle>
              <Eye className="h-4 w-4 text-restaurant-muted" />
            </div>
          </CardHeader>
          <CardContent>
            {metricsLoading ? (
              <div className="flex items-center justify-center h-40">
                <p className="text-restaurant-muted">{t('loadingPopularItems')}...</p>
              </div>
            ) : dashboardMetrics?.popularItems && dashboardMetrics.popularItems.length > 0 ? (
              <div className="space-y-3">
                {dashboardMetrics.popularItems.map((item, index) => (
                  <div key={item.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center justify-center w-8 h-8 bg-restaurant-primary/10 rounded-full">
                        <span className="text-sm font-medium text-restaurant-primary">#{index + 1}</span>
                      </div>
                      <div>
                        <p className="font-medium">{item.name}</p>
                        <p className="text-xs text-restaurant-muted capitalize">{item.category}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">€{item.currentPrice.toFixed(2)}</p>
                      <p className="text-xs text-restaurant-muted">{item.orderCount} {t('orders')}</p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex items-center justify-center h-40">
                <p className="text-restaurant-muted">{t('noSalesData')}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Recent Orders */}
        <Card>
          <CardHeader>
            <CardTitle>{t('recentOrders')}</CardTitle>
          </CardHeader>
          <CardContent>
            {metricsLoading ? (
              <div className="flex items-center justify-center h-40">
                <p className="text-restaurant-muted">{t('loadingOrders')}...</p>
              </div>
            ) : dashboardMetrics?.recentOrders && dashboardMetrics.recentOrders.length > 0 ? (
              <div className="space-y-3 max-h-80 overflow-y-auto">
                {dashboardMetrics.recentOrders.slice(0, 5).map((order) => (
                  <div key={order.id} className="flex items-center justify-between p-2 border rounded">
                    <div>
                      <p className="font-medium text-sm">
                        {order.customerName || t('anonymous')}
                      </p>
                      <p className="text-xs text-restaurant-muted">
                        {order.itemCount} {t('items')} • {new Date(order.createdAt).toLocaleTimeString()}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">€{order.totalAmount.toFixed(2)}</p>
                      <Badge variant={order.status === 'completed' ? 'default' : 'secondary'} className="text-xs">
                        {t(order.status)}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex items-center justify-center h-40">
                <p className="text-restaurant-muted">{t('noRecentOrders')}</p>
              </div>
            )}
            <Link to="/admin/orders">
              <Button variant="outline" className="w-full mt-4">
                {t('viewAllOrders')}
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Utensils className="h-5 w-5 mr-2" />
              {t('menuManagement')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Link to="/admin/menu-items">
              <Button className="w-full">
                <Plus className="h-4 w-4 mr-2" />
                {t('addMenuItem')}
              </Button>
            </Link>
            <Link to="/admin/menu-items">
              <Button variant="outline" className="w-full">
                {t('manageItems')}
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart2 className="h-5 w-5 mr-2" />
              {t('analytics')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Link to="/admin/analytics">
              <Button className="w-full">
                {t('viewReports')}
              </Button>
            </Link>
            <Link to="/admin/settings">
              <Button variant="outline" className="w-full">
                <Settings className="h-4 w-4 mr-2" />
                {t('settings')}
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <QrCode className="h-5 w-5 mr-2" />
              {t('customerExperience')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Link to="/admin/tables">
              <Button className="w-full">
                {t('manageTables')}
              </Button>
            </Link>
            <Link to="/menu" target="_blank">
              <Button variant="outline" className="w-full">
                <Eye className="h-4 w-4 mr-2" />
                {t('previewMenu')}
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
};

export default AdminDashboard;
