import React, { createContext, useContext, useState, ReactNode } from 'react';

// Define available languages
export type Language = 'en' | 'es';

// Define translation type
type Translations = {
  [key in Language]: {
    [key: string]: string;
  };
};

// App translations
export const translations: Translations = {
  en: {
    // Navigation
    dashboard: "Dashboard",
    menuManagement: "Menu Management",
    manageMenuItems: "Manage Menu Items",
    
    // Traffic Analytics
    trafficAnalytics: "Traffic Analytics",
    salesAnalytics: "Sales Analytics",
    menuAnalytics: "Menu Analytics",
    totalSales: "Total Sales",
    orderCount: "Order Count",
    averageOrderValue: "Average Order Value",
    salesByHour: "Sales by Hour",
    salesByCategory: "Sales by Category",
    salesByDayOfWeek: "Sales by Day of Week",
    popularItems: "Popular Items",
    bestSellingItems: "Best-selling menu items",
    priceRecommendations: "Price Recommendations",
    smartPricingSuggestions: "Smart pricing suggestions based on popularity and traffic",
    categoryPerformance: "Category Performance",
    last24Hours: "Last 24 hours",
    last7Days: "Last 7 days",
    last30Days: "Last 30 days",
    day: "Day",
    week: "Week",
    month: "Month",
    orders: "orders",
    sales: "sales",
    applyToMenu: "Apply to Menu",
    noPricingRecommendations: "No pricing recommendations available",
    noCategoryData: "No category data available",
    refreshData: "Refresh Data",
    currentTraffic: "Current Traffic",
    tableOccupancy: "Table Occupancy",
    pricingStatus: "Pricing Status",
    dataConfidence: "Data Confidence",
    high: "High",
    medium: "Medium",
    low: "Low",
    normal: "Normal",
    lastUpdated: "Last updated",
    tables: "tables",
    occupancy: "occupancy",
    currentDynamicPricing: "Current dynamic pricing",
    highConfidence: "High confidence",
    mediumConfidence: "Medium confidence",
    lowConfidence: "Low confidence",
    trafficTrend: "Traffic Trend",
    todayTraffic: "Today's hourly traffic",
    tableStatus: "Table Status",
    currentOccupancy: "Current occupancy",
    occupiedTable: "Occupied",
    availableTable: "Available",
    time: "Time",
    noTrafficData: "No traffic data available for today",
    noTableData: "No table data available",
    weeklyTrafficPattern: "Weekly Traffic Pattern",
    weeklyLast7Days: "Last 7 days",
    averageOccupancy: "Average occupancy",
    weeklyDay: "Day",
    noWeeklyData: "No weekly data available",
    pricingRules: "Pricing Rules",
    currentDynamicPricingRules: "Current dynamic pricing rules",
    editRules: "Edit Rules",
    priceAdjustment: "price adjustment",
    noPricingRules: "No pricing rules available",
    noSalesData: "No sales data available",
    noPopularItems: "No popular items data available",
    reviewAnalytics: "Review your restaurant's performance analytics",
    tableManagement: "Table Management",
    orderManagement: "Order Management",
    analytics: "Analytics",
    settings: "Settings",
    signOut: "Sign Out",
    login: "Login",

    // Common
    welcome: "Welcome back!",
    todaysOverview: "Here's what's happening with your restaurant today.",
    cookieConsentMessage: "We use cookies to enhance your experience and remember your order history. By using our service, you agree to our use of cookies.",
    accept: "Accept",
    decline: "Decline",
    todaysOrders: "Today's Orders",
    currentTrafficOverview: "Current Traffic",
    dynamicPricing: "Dynamic Pricing",
    recentOrders: "Recent Orders",
    viewAllOrders: "View All Orders",
    trafficHeatmap: "Traffic Heatmap",
    quickMenuManagement: "Quick Menu Management",
    addItem: "Add Item",
    configureRules: "Configure Pricing Rules",
    optional: "optional",

    // Hero Section
    welcomeToRestaurant: "Welcome to our Restaurant",
    enjoyDiningExperience: "Enjoy a delightful dining experience with our digital menu service",
    viewMenuAndOrder: "View Menu & Order",
    restaurantOwnerLogin: "Restaurant Owner Login",
    scanQrCode: "Scan QR Code",
    scanQrCodeDesc: "Scan the QR code on your table to access our digital menu",
    placeYourOrder: "Place Your Order",
    placeYourOrderDesc: "Browse our menu and select items to add to your order",
    enjoyYourMeal: "Enjoy Your Meal",
    enjoyYourMealDesc: "Receive updates on your order status and enjoy when it arrives",

    // Menu Management
    menuItems: "Menu Items",
    category: "Category",
    name: "Name",
    price: "Price",
    basePrice: "Base Price",
    currentPrice: "Current Price",
    available: "Available",
    duration: "Duration",
    edit: "Edit",
    delete: "Delete",
    addMenuItem: "Add Menu Item",
    editMenuItem: "Edit Menu Item",
    save: "Save",
    cancel: "Cancel",
    createMenuItem: "Create a new menu item for your restaurant",
    updateMenuItem: "Update the details of this menu item",
    image: "Image",
    uploadImage: "Upload Image",
    imageUploadHint: "Upload a photo of your menu item (up to 2MB)",
    description: "Description",
    menu: "Menu",
    availableDescription: "Whether this item is currently available for ordering",
    food: "Food",
    drinks: "Drinks",
    desserts: "Desserts",
    starters: "Starters",
    mains: "Main Courses",
    sides: "Side Dishes",
    specials: "Specials",
    selectCategory: "Select a category",
    selectMenu: "Select a menu",
    startTime: "Start Time",
    endTime: "End Time",
    create: "Create",
    addMenu: "Add Menu",

    // Table Management
    tablesSection: "Tables",
    tableNumber: "Table Number",
    capacity: "Capacity",
    status: "Status",
    location: "Location",
    occupied: "Occupied",
    tableAvailable: "Available",
    addTable: "Add Table",
    editTable: "Edit Table",
    generateQR: "Generate QR Code",

    // Order Management
    ordersList: "Orders",
    orderId: "Order ID",
    table: "Table",
    items: "Items",
    total: "Total",
    orderStatus: "Status",
    orderTime: "Order Time",
    pending: "Pending",
    preparing: "Preparing",
    ready: "Ready",
    delivered: "Delivered",
    completed: "Completed",
    cancelled: "Cancelled",
    viewOrder: "View Order",
    updateStatus: "Update Status",

    // Analytics
    salesOverview: "Sales Overview",
    analyticsPopularItems: "Popular Items",
    peakHours: "Peak Hours",
    revenueMetric: "Revenue",
    dailySales: "Daily Sales",
    monthlySales: "Monthly Sales",

    // Settings
    generalSettings: "General Settings",
    restaurantProfile: "Restaurant Profile",
    pricingRulesSettings: "Pricing Rules",
    userProfile: "User Profile",
    language: "Language",
    english: "English",
    spanish: "Spanish",
    peakHoursAdjustment: "Peak Hours Adjustment",
    quietHoursAdjustment: "Quiet Hours Adjustment",
    restaurantName: "Restaurant Name",
    logoUrl: "Logo URL",
    saveProfile: "Save Restaurant Profile",
    saving: "Saving...",
    profileUpdated: "Profile Updated",
    profileUpdateSuccess: "Your restaurant profile has been updated successfully",

    // Dynamic Pricing
    highTrafficAdjustment: "High Traffic Adjustment",
    mediumTrafficAdjustment: "Medium Traffic Adjustment",
    lowTrafficAdjustment: "Low Traffic Adjustment",
    applyDynamicPricingTo: "Apply Dynamic Pricing To",
    foodItems: "Food Items",
    drinkItems: "Drink Items",
    aiConfidenceThreshold: "AI Confidence Threshold",
    
    // Dashboard specific
    dashboardOverview: "Dashboard Overview",
    realTimeInsights: "Real-time insights for your business",
    refresh: "Refresh",
    testLowTraffic: "Test Low Traffic",
    testHighTraffic: "Test High Traffic",
    todaysRevenue: "Today's Revenue",
    vsYesterday: "vs yesterday",
    highTraffic: "HIGH TRAFFIC",
    mediumTraffic: "MEDIUM TRAFFIC",
    lowTraffic: "LOW TRAFFIC",
    normalTraffic: "NORMAL TRAFFIC",
    on: "ON",
    off: "OFF",
    loading: "Loading",
    confidence: "Confidence",
    analyzingTraffic: "Analyzing traffic",
    enableAiPricing: "Enable AI pricing",
    noOrdersToday: "No orders yet today",
    loadingPopularItems: "Loading popular items",
    loadingOrders: "Loading orders",
    anonymous: "Anonymous",
    items: "items",
    noRecentOrders: "No recent orders",
    viewReports: "View Reports",
    customerExperience: "Customer Experience",
    manageTables: "Manage Tables",
    manageItems: "Manage Items",
    previewMenu: "Preview Menu",
    
    // Error handling
    errorLoadingDashboard: "Error loading dashboard",
    failedToLoadDashboardData: "Failed to load dashboard data",
    tryAgain: "Try Again",
    
    // Notifications
    newOrderReceived: "New order received!",
    order: "Order",
    hasBeenPlaced: "has been placed.",
    savePricingRules: "Save Pricing Rules",
    pricingRulesUpdated: "Pricing Rules Updated",
    pricingRulesUpdateSuccess: "Your dynamic pricing rules have been saved successfully",
    highTrafficExplanation: "During high traffic periods (80% or more capacity), prices will increase by this percentage.",
    mediumTrafficExplanation: "During medium traffic periods (50-80% capacity), prices will increase by this percentage.",
    lowTrafficExplanation: "During low traffic periods (under 30% capacity), prices can be decreased to attract more customers.",
    confidenceExplanation: "Only apply price changes when the AI is at least this confident in its recommendations.",
    
    // Feedback Form
    shareFeedback: "Share Feedback",
    foodQuality: "Food Quality",
    serviceQuality: "Service Quality",
    appExperience: "App Experience",
    additionalComments: "Additional Comments",
    tellUsMoreAboutExperience: "Tell us more about your experience",
    shareEmailForUpdates: "Share email for updates",
    submitFeedback: "Submit Feedback",
    skipFeedback: "Skip Feedback",
    thankYouForFeedback: "Thank you for your feedback",
    errorSubmittingFeedback: "Error submitting feedback",
    pleaseTryAgainLater: "Please try again later",
  },
  es: {
    // Navigation
    dashboard: "Panel Principal",
    menuManagement: "Gestión de Menú",
    manageMenuItems: "Administrar Elementos del Menú",
    tableManagement: "Gestión de Mesas",
    orderManagement: "Gestión de Pedidos",
    analytics: "Análisis",
    settings: "Configuración",
    signOut: "Cerrar Sesión",
    login: "Iniciar Sesión",

    // Traffic Analytics
    trafficAnalytics: "Análisis de Tráfico",
    salesAnalytics: "Análisis de Ventas",
    menuAnalytics: "Análisis de Menú",
    totalSales: "Ventas Totales",
    orderCount: "Número de Órdenes",
    averageOrderValue: "Valor Medio de Pedido",
    salesByHour: "Ventas por Hora",
    salesByCategory: "Ventas por Categoría",
    salesByDayOfWeek: "Ventas por Día de la Semana",
    popularItems: "Artículos Populares",
    bestSellingItems: "Artículos más vendidos del menú",
    priceRecommendations: "Recomendaciones de Precio",
    smartPricingSuggestions: "Sugerencias de precios inteligentes basadas en popularidad y tráfico",
    categoryPerformance: "Rendimiento por Categoría",
    last24Hours: "Últimas 24 horas",
    last7Days: "Últimos 7 días",
    last30Days: "Últimos 30 días",
    day: "Día",
    week: "Semana",
    month: "Mes",
    orders: "pedidos",
    sales: "ventas",
    applyToMenu: "Aplicar al Menú",
    noPricingRecommendations: "No hay recomendaciones de precios disponibles",
    noCategoryData: "No hay datos de categoría disponibles",
    refreshData: "Actualizar Datos",
    currentTraffic: "Tráfico Actual",
    tableOccupancy: "Ocupación de Mesas",
    pricingStatus: "Estado de Precios",
    dataConfidence: "Confianza de Datos",
    high: "Alto",
    medium: "Medio",
    low: "Bajo",
    normal: "Normal",
    lastUpdated: "Última actualización",
    tables: "mesas",
    occupancy: "ocupación",
    currentDynamicPricing: "Precio dinámico actual",
    highConfidence: "Alta confianza",
    mediumConfidence: "Confianza media",
    lowConfidence: "Baja confianza",
    trafficTrend: "Tendencia de Tráfico",
    todayTraffic: "Tráfico por hora de hoy",
    tableStatus: "Estado de Mesas",
    currentOccupancy: "Ocupación actual",
    occupiedTable: "Ocupadas",
    availableTable: "Disponibles",
    time: "Hora",
    noTrafficData: "No hay datos de tráfico disponibles para hoy",
    noTableData: "No hay datos de mesas disponibles",
    weeklyTrafficPattern: "Patrón de Tráfico Semanal",
    weeklyLast7Days: "Últimos 7 días",
    averageOccupancy: "Ocupación promedio",
    weeklyDay: "Día",
    noWeeklyData: "No hay datos semanales disponibles",
    pricingRules: "Reglas de Precios",
    currentDynamicPricingRules: "Reglas actuales de precios dinámicos",
    editRules: "Editar Reglas",
    priceAdjustment: "ajuste de precio",
    noPricingRules: "No hay reglas de precios disponibles",
    noSalesData: "No hay datos de ventas disponibles",
    noPopularItems: "No hay datos de elementos populares disponibles",
    reviewAnalytics: "Revise los análisis de rendimiento de su restaurante",

    // Common
    welcome: "¡Bienvenido de nuevo!",
    todaysOverview: "Esto es lo que está sucediendo con su restaurante hoy.",
    cookieConsentMessage: "Utilizamos cookies para mejorar su experiencia y recordar su historial de pedidos. Al utilizar nuestro servicio, acepta nuestro uso de cookies.",
    accept: "Aceptar",
    decline: "Rechazar",
    todaysOrders: "Pedidos de Hoy",
    currentTrafficOverview: "Tráfico Actual",
    dynamicPricing: "Precios Dinámicos",
    recentOrders: "Pedidos Recientes",
    viewAllOrders: "Ver Todos los Pedidos",
    trafficHeatmap: "Mapa de Calor de Tráfico",
    quickMenuManagement: "Gestión Rápida de Menú",
    addItem: "Añadir Elemento",
    configureRules: "Configurar Reglas de Precios",
    optional: "opcional",

    // Hero Section
    welcomeToRestaurant: "Bienvenido a nuestro Restaurante",
    enjoyDiningExperience: "Disfruta de una experiencia gastronómica excepcional con nuestro servicio de menú digital",
    viewMenuAndOrder: "Ver Menú y Pedir",
    restaurantOwnerLogin: "Acceso para Propietarios",
    scanQrCode: "Escanea el Código QR",
    scanQrCodeDesc: "Escanea el código QR en tu mesa para acceder a nuestro menú digital",
    placeYourOrder: "Haz tu Pedido",
    placeYourOrderDesc: "Navega por nuestro menú y selecciona los platos para añadir a tu pedido",
    enjoyYourMeal: "Disfruta tu Comida",
    enjoyYourMealDesc: "Recibe actualizaciones sobre el estado de tu pedido y disfruta cuando llegue",

    // Menu Management
    menuItems: "Elementos del Menú",
    category: "Categoría",
    name: "Nombre",
    price: "Precio",
    basePrice: "Precio Base",
    currentPrice: "Precio Actual",
    available: "Disponible",
    duration: "Duración",
    edit: "Editar",
    delete: "Eliminar",
    addMenuItem: "Añadir Elemento al Menú",
    editMenuItem: "Editar Elemento del Menú",
    save: "Guardar",
    cancel: "Cancelar",
    createMenuItem: "Crear un nuevo elemento de menú para su restaurante",
    updateMenuItem: "Actualizar los detalles de este elemento del menú",
    image: "Imagen",
    uploadImage: "Subir Imagen",
    imageUploadHint: "Sube una foto de tu elemento de menú (hasta 2MB)",
    description: "Descripción",
    menu: "Menú",
    availableDescription: "Si este elemento está actualmente disponible para ordenar",
    food: "Comida",
    drinks: "Bebidas",
    desserts: "Postres",
    starters: "Entrantes",
    mains: "Platos Principales",
    sides: "Guarniciones",
    specials: "Especialidades",
    selectCategory: "Seleccionar categoría",
    selectMenu: "Seleccionar menú",
    startTime: "Hora de Inicio",
    endTime: "Hora de Fin",
    create: "Crear",
    addMenu: "Añadir Menú",

    // Table Management
    tablesList: "Mesas",
    tableNumber: "Número de Mesa",
    capacity: "Capacidad",
    status: "Estado",
    location: "Ubicación",
    occupied: "Ocupada",
    tableAvailable: "Disponible",
    addTable: "Añadir Mesa",
    editTable: "Editar Mesa",
    generateQR: "Generar Código QR",

    // Order Management
    ordersList: "Pedidos",
    orderId: "ID de Pedido",
    table: "Mesa",
    items: "Artículos",
    total: "Total",
    orderStatus: "Estado",
    orderTime: "Hora del Pedido",
    pending: "Pendiente",
    preparing: "Preparando",
    ready: "Listo",
    delivered: "Entregado",
    completed: "Completado",
    cancelled: "Cancelado",
    viewOrder: "Ver Pedido",
    updateStatus: "Actualizar Estado",

    // Analytics
    salesOverview: "Resumen de Ventas",
    analyticsPopularItems: "Artículos Populares",
    peakHours: "Horas Pico",
    revenue: "Ingresos",
    dailySales: "Ventas Diarias",
    monthlySales: "Ventas Mensuales",

    // Settings
    generalSettings: "Configuración General",
    restaurantProfile: "Perfil del Restaurante",
    settingsPricingRules: "Reglas de Precios",
    userProfile: "Perfil de Usuario",
    language: "Idioma",
    english: "Inglés",
    spanish: "Español",
    peakHoursAdjustment: "Ajuste de horas pico",
    quietHoursAdjustment: "Ajuste de horas tranquilas",
    restaurantName: "Nombre del Restaurante",
    logoUrl: "URL del Logotipo",
    saveProfile: "Guardar Perfil del Restaurante",
    saving: "Guardando...",
    profileUpdated: "Perfil Actualizado",
    profileUpdateSuccess: "El perfil de su restaurante ha sido actualizado con éxito",
    
    // Dynamic Pricing
    highTrafficAdjustment: "Ajuste de Tráfico Alto",
    mediumTrafficAdjustment: "Ajuste de Tráfico Medio",
    lowTrafficAdjustment: "Ajuste de Tráfico Bajo",
    applyDynamicPricingTo: "Aplicar Precios Dinámicos A",
    productosAlimenticios: "Productos Alimenticios",
    bebidasItems: "Bebidas",
    umbralDeConfianzaAI: "Umbral de Confianza de IA",
    
    // Dashboard specific
    dashboardOverview: "Resumen del Panel Principal",
    realTimeInsights: "Información en tiempo real para su negocio",
    refresh: "Actualizar",
    testLowTraffic: "Prueba de Tráfico Bajo",
    testHighTraffic: "Prueba de Tráfico Alto",
    todaysRevenue: "Ingresos de Hoy",
    vsYesterday: "vs ayer",
    highTraffic: "TRÁFICO ALTO",
    mediumTraffic: "TRÁFICO MEDIO",
    lowTraffic: "TRÁFICO BAJO",
    normalTraffic: "TRÁFICO NORMAL",
    on: "ENCENDIDO",
    off: "APAGADO",
    loading: "Cargando",
    confidence: "Confianza",
    analyzingTraffic: "Analizando tráfico",
    enableAiPricing: "Habilitar Precio AI",
    noOrdersToday: "Aún no hay pedidos hoy",
    loadingPopularItems: "Cargando elementos populares",
    loadingOrders: "Cargando pedidos",
    anonymous: "Anónimo",
    items: "artículos",
    noRecentOrders: "No hay pedidos recientes",
    viewReports: "Ver Informes",
    customerExperience: "Experiencia del Cliente",
    manageTables: "Administrar Mesas",
    manageItems: "Administrar Elementos",
    previewMenu: "Vista Previa del Menú",
    
    // Error handling
    errorLoadingDashboard: "Error cargando el panel principal",
    failedToLoadDashboardData: "Error al cargar los datos del panel principal",
    tryAgain: "Intentar de Nuevo",
    
    // Notifications
    newOrderReceived: "¡Nuevo pedido recibido!",
    order: "Pedido",
    hasBeenPlaced: "ha sido realizado.",
    savePricingRules: "Guardar Reglas de Precios",
    pricingRulesUpdated: "Reglas de Precios Actualizadas",
    pricingRulesUpdateSuccess: "Las reglas de precios dinámicos se han guardado correctamente",
    highTrafficExplanation: "Durante períodos de tráfico alto (80% o más de capacidad), los precios aumentarán en este porcentaje.",
    mediumTrafficExplanation: "Durante períodos de tráfico medio (50-80% de capacidad), los precios aumentarán en este porcentaje.",
    lowTrafficExplanation: "Durante períodos de tráfico bajo (menos del 30% de capacidad), los precios pueden reducirse para atraer más clientes.",
    confidenceExplanation: "Solo aplicar cambios de precio cuando la IA tenga al menos este nivel de confianza en sus recomendaciones.",
    
    // Feedback Form
    shareFeedback: "Compartir Comentarios",
    foodQuality: "Calidad de la Comida",
    serviceQuality: "Calidad del Servicio",
    appExperience: "Experiencia de la Aplicación",
    additionalComments: "Comentarios Adicionales",
    tellUsMoreAboutExperience: "Cuéntanos más sobre tu experiencia",
    shareEmailForUpdates: "Compartir correo electrónico para actualizaciones",
    submitFeedback: "Enviar Comentarios",
    skipFeedback: "Omitir Comentarios",
    thankYouForFeedback: "Gracias por tus comentarios",
    errorSubmittingFeedback: "Error al enviar comentarios",
    pleaseTryAgainLater: "Por favor, inténtalo de nuevo más tarde",
  }
};

// Create language context
interface LanguageContextType {
  language: Language;
  setLanguage: (language: Language) => void;
  t: (key: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Create language provider
export const LanguageProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [language, setLanguage] = useState<Language>('en');

  // Translation function
  const t = (key: string): string => {
    return translations[language][key] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
};

// Create hook for using language context
export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
