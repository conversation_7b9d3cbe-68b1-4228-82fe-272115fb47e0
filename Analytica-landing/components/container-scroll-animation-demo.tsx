"use client";
import React from "react";
import { ContainerScroll } from "./ui/container-scroll-animation";
import { motion } from "framer-motion";

export default function HeroScrollDemo() {
  return (
    <div className="flex flex-col overflow-hidden">
      <ContainerScroll
        titleComponent={
          <>
            <h1 className="text-4xl font-semibold text-black dark:text-white">
              Powerful Analytics for <br />
              <span className="text-4xl md:text-[6rem] font-bold mt-1 leading-none bg-clip-text text-transparent bg-gradient-to-r from-[#11a5e8] to-[#5f7790]">
                Your Business
              </span>
            </h1>
          </>
        }
      >
        <div className="relative mx-auto w-full max-w-5xl">
          {/* Main large screenshot */}
          <motion.img
            src="/images/sme-ios/1.png"
            alt="SME Analytica Dashboard"
            height={720}
            width={1400}
            className="mx-auto rounded-2xl object-cover shadow-2xl border border-[#5f7790]/20 bg-[#151c28] z-10 relative"
            draggable={false}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          />

          {/* Secondary screenshot positioned for visual interest */}
          <motion.img
            src="/images/sme-ios/2.png"
            alt="SME Analytica Features"
            height={400}
            width={800}
            className="absolute -bottom-10 -right-10 md:right-10 w-64 rounded-xl shadow-lg border border-[#5f7790]/20 bg-[#151c28] z-20"
            draggable={false}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
          />

          {/* Decorative elements */}
          <motion.div
            className="absolute top-1/4 -left-4 md:left-10 z-20 bg-[#11a5e8] text-white text-sm font-medium px-3 py-1.5 rounded-full"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.8 }}
          >
            Real-time Analytics
          </motion.div>

          <motion.div
            className="absolute bottom-1/3 -left-2 md:left-20 z-20 bg-[#5f7790] text-white text-sm font-medium px-3 py-1.5 rounded-full"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 1 }}
          >
            AI-Powered Insights
          </motion.div>
        </div>
      </ContainerScroll>
    </div>
  );
}
