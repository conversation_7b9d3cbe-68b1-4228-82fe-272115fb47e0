"use client";
import {
  Navbar,
  NavBody,
  NavItems,
  MobileNav,
  NavbarLogo,
  NavbarButton,
  MobileNavHeader,
  MobileNavToggle,
  MobileNavMenu,
} from "./ui/resizable-navbar";
import { useState } from "react";
import InterestForm from "../app/components/InterestForm";

export default function NavbarDemo() {
  type NavItem = {
    name: string;
    link: string;
    onClick?: (e: React.MouseEvent) => void;
  };

  const navItems: NavItem[] = [
    {
      name: "Características",
      link: "/features",
    },
    {
      name: "Casos de Uso",
      link: "/use-cases",
    },
    {
      name: "Precio<PERSON>",
      link: "/pricing",
    },
    {
      name: "Acerca de",
      link: "/about",
    },
    {
      name: "Blog",
      link: "/blog",
    },
  ];

  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isContactFormOpen, setIsContactFormOpen] = useState(false);

  const handleContactClick = (e: React.MouseEvent) => {
    e.preventDefault();
    window.location.href = '/contact';
    setIsMobileMenuOpen(false);
  };

  // Use the original navItems without adding Contact
  const allNavItems = navItems;

  return (
    <>
      <div className="relative w-full">
        <Navbar>
          {/* Desktop Navigation */}
          <NavBody>
            <NavbarLogo />
            <NavItems
              items={allNavItems}
              onItemClick={(item) => {
                if (item.onClick) {
                  item.onClick(new MouseEvent('click') as unknown as React.MouseEvent);
                }
                setIsMobileMenuOpen(false);
              }}
            />
            <div className="flex items-center gap-4">
              <NavbarButton
                variant="gradient"
                onClick={handleContactClick}
              >
                Contacto
              </NavbarButton>
            </div>
          </NavBody>

          {/* Mobile Navigation */}
          <MobileNav>
            <MobileNavHeader>
              <NavbarLogo />
              <MobileNavToggle
                isOpen={isMobileMenuOpen}
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              />
            </MobileNavHeader>

            <MobileNavMenu
              isOpen={isMobileMenuOpen}
              onClose={() => setIsMobileMenuOpen(false)}
            >
              {allNavItems.map((item, idx) => (
                <a
                  key={`mobile-link-${idx}`}
                  href={item.link}
                  onClick={(e) => {
                    if (item.onClick && typeof item.onClick === 'function') {
                      e.preventDefault();
                      item.onClick(e);
                    } else {
                      setIsMobileMenuOpen(false);
                    }
                  }}
                  className="relative text-[#d5dce2]"
                >
                  <span className="block">{item.name}</span>
                </a>
              ))}
            </MobileNavMenu>
          </MobileNav>
        </Navbar>
      </div>

      <InterestForm
        isOpen={isContactFormOpen}
        onClose={() => setIsContactFormOpen(false)}
        formType="contact"
      />
    </>
  );
}
