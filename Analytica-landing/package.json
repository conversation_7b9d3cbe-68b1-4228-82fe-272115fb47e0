{"name": "analytica-landing", "version": "0.1.0", "private": true, "engines": {"node": ">=18.0.0"}, "scripts": {"dev": "next dev", "build": "next build --no-lint", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.2.0", "@radix-ui/react-label": "^2.1.4", "@react-three/drei": "^10.0.7", "@react-three/fiber": "^9.1.2", "@supabase/supabase-js": "^2.49.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^11.18.2", "lucide-react": "^0.487.0", "motion": "^12.12.1", "next": "14.1.0", "parallax": "^0.0.0", "react": "18.2.0", "react-countup": "^6.5.3", "react-dom": "18.2.0", "react-icons": "^5.5.0", "react-scroll-parallax": "^3.4.5", "sharp": "^0.33.5", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "three": "^0.176.0", "three-globe": "^2.42.4"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18.3.21", "@types/react-dom": "^18.3.7", "autoprefixer": "^10.4.21", "eslint": "^8", "eslint-config-next": "14.1.0", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}