import { MetadataRoute } from 'next';

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://smeanalytica.dev';

  // Main pages
  const routes = [
    '',
    '/features',
    '/use-cases',
    '/pricing',
    '/about',
    '/contact',
    '/blog',
    '/privacy-policy',
    '/terms',
    '/gdpr',
  ];

  // Blog posts
  const blogPosts = [
    '/blog/cash-flow-prediction-strategies-small-business',
    '/blog/retail-customer-behavior-analytics-increase-sales',
    '/blog/ai-inventory-management-small-business-comparison',
    '/blog/restaurant-food-waste-reduction-analytics',
    '/blog/ecommerce-analytics-small-business-guide-2024',
    '/blog/customer-lifetime-value-calculation-improvement-sme',
  ];

  // Combine main routes
  const mainSitemap = routes.map((route) => ({
    url: `${baseUrl}${route}`,
    lastModified: new Date(),
    changeFrequency: route === '' ? 'weekly' : route === '/blog' ? 'weekly' : 'monthly',
    priority: route === '' ? 1 : route === '/blog' ? 0.9 : 0.8,
  }));

  // Add blog posts
  const blogSitemap = blogPosts.map((post) => ({
    url: `${baseUrl}${post}`,
    lastModified: new Date(),
    changeFrequency: 'monthly' as const,
    priority: 0.7,
  }));

  return [...mainSitemap, ...blogSitemap];
}